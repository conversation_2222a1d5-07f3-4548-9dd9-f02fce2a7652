
import { useAuth } from './useAuth';
import { 
  hasRoleEnhanced as hasRole, 
  isAccountLockedEnhanced as isAccountLocked, 
  logFailedLoginAttemptEnhanced as logFailedLoginAttempt,
  isAdminEnhanced as isAdminCheck
} from '@/utils/enhancedSecurity';
import { useState, useEffect } from 'react';

interface SecureAuthHook {
  isAdmin: boolean;
  isSupervisor: boolean;
  isCashier: boolean;
  canDeleteProducts: boolean;
  canModifyCategories: boolean;
  canDeleteSales: boolean;
  canViewAuditLogs: boolean;
  secureLogin: (email: string, password: string) => Promise<{ error?: string }>;
  checkPermission: (permission: string) => Promise<boolean>;
}

export const useSecureAuth = (): SecureAuthHook => {
  const { login, profile, isAuthenticated } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isSupervisor, setIsSupervisor] = useState(false);
  const [isCashier, setIsCashier] = useState(false);

  useEffect(() => {
    const updateRoles = async () => {
      if (isAuthenticated && profile) {
        try {
          const adminStatus = await isAdminCheck();
          const supervisorStatus = profile.role === 'supervisor';
          const cashierStatus = profile.role === 'cashier';
          
          setIsAdmin(adminStatus);
          setIsSupervisor(supervisorStatus);
          setIsCashier(cashierStatus);
        } catch (error) {
          console.error('Error updating roles:', error);
          // Fallback to profile role
          setIsAdmin(profile.role === 'admin');
          setIsSupervisor(profile.role === 'supervisor');
          setIsCashier(profile.role === 'cashier');
        }
      } else {
        setIsAdmin(false);
        setIsSupervisor(false);
        setIsCashier(false);
      }
    };

    updateRoles();
  }, [isAuthenticated, profile]);

  const secureLogin = async (email: string, password: string): Promise<{ error?: string }> => {
    try {
      // Check if account is locked
      const locked = await isAccountLocked(email);
      if (locked) {
        return { error: 'الحساب مؤقتاً بسبب محاولات دخول خاطئة متعددة. حاول مرة أخرى لاحقاً.' };
      }

      const result = await login(email, password);
      
      if (result.error) {
        // Log failed attempt
        await logFailedLoginAttempt(email);
        return result;
      }

      return result;
    } catch (error) {
      await logFailedLoginAttempt(email);
      return { error: 'حدث خطأ أثناء تسجيل الدخول' };
    }
  };

  const checkPermission = async (permission: string): Promise<boolean> => {
    try {
      switch (permission) {
        case 'delete_products':
          return await hasRole('admin');
        case 'modify_categories':
          return await hasRole('admin');
        case 'delete_sales':
          return await hasRole('admin');
        case 'view_audit_logs':
          return await hasRole('admin');
        case 'manage_users':
          return await hasRole('admin');
        case 'manage_debts':
          return await hasRole('admin') || await hasRole('supervisor');
        case 'manage_installments':
          return await hasRole('admin') || await hasRole('supervisor');
        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  };

  return {
    isAdmin,
    isSupervisor,
    isCashier,
    canDeleteProducts: isAdmin,
    canModifyCategories: isAdmin,
    canDeleteSales: isAdmin,
    canViewAuditLogs: isAdmin,
    secureLogin,
    checkPermission
  };
};
