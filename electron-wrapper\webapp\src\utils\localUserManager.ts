export interface LocalUser {
  id: string;
  name: string;
  email: string;
  password: string;
  role: 'admin' | 'cashier' | 'supervisor';
  active: boolean;
  lastLogin?: string;
  created_at: string;
  updated_at: string;
}

export interface LoginSession {
  user: LocalUser;
  sessionId: string;
  loginTime: string;
  isActive: boolean;
}

const USERS_STORAGE_KEY = 'aridoo-local-users';
const SESSION_STORAGE_KEY = 'aridoo-current-session';
const FAILED_ATTEMPTS_KEY = 'aridoo-failed-attempts';

// المستخدمين الافتراضيين
const DEFAULT_USERS: LocalUser[] = [
  {
    id: '1',
    name: 'مدير النظام',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',  
    name: 'كاشير الفرع',
    email: '<EMAIL>',
    password: 'cashier123',
    role: 'cashier',
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '3',
    name: 'مولد التراخيص',
    email: '<EMAIL>',
    password: 'Ar2212412',
    role: 'admin',
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

export class LocalUserManager {
  private static instance: LocalUserManager;

  private constructor() {
    this.forceInitializeDefaultUsers();
  }

  public static getInstance(): LocalUserManager {
    if (!LocalUserManager.instance) {
      LocalUserManager.instance = new LocalUserManager();
    }
    return LocalUserManager.instance;
  }

  private forceInitializeDefaultUsers(): void {
    // إجبار إعادة تحميل المستخدمين الافتراضيين في كل مرة
    try {
      const existingUsers = this.getAllUsers();
      
      // التأكد من وجود جميع المستخدمين الافتراضيين
      let usersUpdated = false;
      
      DEFAULT_USERS.forEach(defaultUser => {
        const existingUser = existingUsers.find(user => user.email === defaultUser.email);
        if (!existingUser) {
          existingUsers.push(defaultUser);
          usersUpdated = true;
          console.log(`Added missing default user: ${defaultUser.email}`);
        } else if (existingUser.password !== defaultUser.password) {
          // تحديث كلمة المرور إذا تغيرت
          existingUser.password = defaultUser.password;
          existingUser.updated_at = new Date().toISOString();
          usersUpdated = true;
          console.log(`Updated password for user: ${defaultUser.email}`);
        }
      });

      if (usersUpdated || existingUsers.length === 0) {
        localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(existingUsers.length > 0 ? existingUsers : DEFAULT_USERS));
        console.log('Default users initialized/updated successfully');
      }
      
      // إلغاء قفل حساب المطور فوراً
      this.clearFailedAttempts('<EMAIL>');
      console.log('Developer account unlocked automatically');
      
    } catch (error) {
      console.error('Error initializing default users:', error);
      // في حالة الخطأ، إعادة تعيين كامل
      localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(DEFAULT_USERS));
    }
  }

  public getAllUsers(): LocalUser[] {
    try {
      const usersData = localStorage.getItem(USERS_STORAGE_KEY);
      return usersData ? JSON.parse(usersData) : [];
    } catch (error) {
      console.error('Error getting users from localStorage:', error);
      return [];
    }
  }

  public getUserById(id: string): LocalUser | null {
    const users = this.getAllUsers();
    return users.find(user => user.id === id) || null;
  }

  public getUserByEmail(email: string): LocalUser | null {
    const users = this.getAllUsers();
    const user = users.find(user => user.email.toLowerCase() === email.toLowerCase());
    console.log(`Looking for user with email: ${email}, found:`, user ? 'YES' : 'NO');
    return user || null;
  }

  public createUser(userData: Omit<LocalUser, 'id' | 'created_at' | 'updated_at'>): LocalUser {
    const users = this.getAllUsers();
    const newUser: LocalUser = {
      ...userData,
      id: Date.now().toString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    users.push(newUser);
    localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(users));
    return newUser;
  }

  public updateUser(id: string, updates: Partial<LocalUser>): LocalUser | null {
    const users = this.getAllUsers();
    const userIndex = users.findIndex(user => user.id === id);
    
    if (userIndex === -1) return null;
    
    users[userIndex] = {
      ...users[userIndex],
      ...updates,
      updated_at: new Date().toISOString()
    };
    
    localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(users));
    return users[userIndex];
  }

  public deleteUser(id: string): boolean {
    const users = this.getAllUsers();
    const filteredUsers = users.filter(user => user.id !== id);
    
    if (filteredUsers.length === users.length) return false;
    
    localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(filteredUsers));
    return true;
  }

  public unlockAccount(email: string): boolean {
    try {
      this.clearFailedAttempts(email);
      console.log(`Account unlocked for: ${email}`);
      return true;
    } catch (error) {
      console.error('Error unlocking account:', error);
      return false;
    }
  }

  public clearAllFailedAttempts(): void {
    try {
      localStorage.removeItem(FAILED_ATTEMPTS_KEY);
      console.log('All failed login attempts cleared');
    } catch (error) {
      console.error('Error clearing all failed attempts:', error);
    }
  }

  public login(email: string, password: string): { success: boolean; user?: LocalUser; error?: string } {
    console.log(`Login attempt for email: ${email}`);
    
    // تحقق من محاولات الدخول الفاشلة
    if (this.isAccountLocked(email)) {
      return { success: false, error: 'الحساب مؤقتاً بسبب محاولات دخول خاطئة متعددة' };
    }

    const user = this.getUserByEmail(email);
    
    if (!user) {
      console.log('User not found, available users:', this.getAllUsers().map(u => u.email));
      this.logFailedAttempt(email);
      return { success: false, error: 'البريد الإلكتروني غير موجود' };
    }

    if (!user.active) {
      return { success: false, error: 'هذا الحساب غير نشط' };
    }

    if (user.password !== password) {
      console.log(`Password mismatch for ${email}. Expected: ${user.password}, Got: ${password}`);
      this.logFailedAttempt(email);
      return { success: false, error: 'كلمة المرور غير صحيحة' };
    }

    // تحديث آخر دخول
    this.updateUser(user.id, { lastLogin: new Date().toISOString() });
    
    // إنشاء جلسة
    const session: LoginSession = {
      user: user,
      sessionId: Date.now().toString(),
      loginTime: new Date().toISOString(),
      isActive: true
    };
    
    localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(session));
    this.clearFailedAttempts(email);
    
    console.log(`Login successful for: ${email}`);
    return { success: true, user };
  }

  public logout(): void {
    localStorage.removeItem(SESSION_STORAGE_KEY);
  }

  public getCurrentSession(): LoginSession | null {
    const sessionData = localStorage.getItem(SESSION_STORAGE_KEY);
    return sessionData ? JSON.parse(sessionData) : null;
  }

  public isLoggedIn(): boolean {
    const session = this.getCurrentSession();
    return session?.isActive === true;
  }

  private logFailedAttempt(email: string): void {
    const attemptsData = localStorage.getItem(FAILED_ATTEMPTS_KEY);
    const attempts = attemptsData ? JSON.parse(attemptsData) : {};
    
    if (!attempts[email]) {
      attempts[email] = [];
    }
    
    attempts[email].push(new Date().toISOString());
    localStorage.setItem(FAILED_ATTEMPTS_KEY, JSON.stringify(attempts));
  }

  private clearFailedAttempts(email: string): void {
    const attemptsData = localStorage.getItem(FAILED_ATTEMPTS_KEY);
    if (!attemptsData) return;
    
    const attempts = JSON.parse(attemptsData);
    delete attempts[email];
    localStorage.setItem(FAILED_ATTEMPTS_KEY, JSON.stringify(attempts));
  }

  private isAccountLocked(email: string): boolean {
    const attemptsData = localStorage.getItem(FAILED_ATTEMPTS_KEY);
    if (!attemptsData) return false;
    
    const attempts = JSON.parse(attemptsData);
    const userAttempts = attempts[email] || [];
    
    // تحقق من آخر 5 محاولات في آخر 15 دقيقة
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    const recentAttempts = userAttempts.filter((attempt: string) => 
      new Date(attempt) > fifteenMinutesAgo
    );
    
    return recentAttempts.length >= 5;
  }

  public hasRole(role: 'admin' | 'cashier' | 'supervisor'): boolean {
    const session = this.getCurrentSession();
    return session?.user.role === role;
  }

  public isAdmin(): boolean {
    return this.hasRole('admin');
  }

  public canDeleteProducts(): boolean {
    return this.isAdmin();
  }

  public canModifyCategories(): boolean {
    return this.isAdmin();
  }

  public canManageUsers(): boolean {
    return this.isAdmin();
  }
}

// Immediately unlock the developer account
const manager = LocalUserManager.getInstance();
manager.unlockAccount('<EMAIL>');
console.log('Developer account unlocked automatically');

export const localUserManager = LocalUserManager.getInstance();
