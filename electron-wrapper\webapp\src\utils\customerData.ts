import { supabase } from '@/integrations/supabase/client';

export interface CustomerPurchaseHistory {
  id: string;
  date: string;
  amount: number;
  items: string[];
  status: string;
  paymentMethod: string;
  saleType?: string;
  itemsWithDetails?: Array<{
    name: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    category?: string;
  }>;
}

export interface CustomerPaymentHistory {
  id: string;
  date: string;
  amount: number;
  note: string;
  method: string;
}

export interface CustomerInstallmentHistory {
  id: string;
  totalAmount: number;
  downPayment: number;
  remainingAmount: number;
  monthlyPayment: number;
  paidInstallments: number;
  totalInstallments: number;
  nextPaymentDate: string;
  status: string;
  items: string[];
  itemsWithDetails?: Array<{
    name: string;
    quantity: number;
    category?: string;
  }>;
}

export interface CustomerStats {
  totalInvoices: number;
  paidInvoices: number;
  pendingInvoices: number;
  averageInvoice: number;
}

export const getCustomerPurchaseHistory = async (customerId: string): Promise<CustomerPurchaseHistory[]> => {
  console.log('Fetching enhanced purchase history for customer:', customerId);
  
  const { data: sales, error } = await supabase
    .from('sales')
    .select(`
      id,
      created_at,
      final_amount,
      payment_method,
      sale_items(
        quantity,
        unit_price,
        total_price,
        product:products(
          name,
          category:categories(name)
        )
      )
    `)
    .eq('customer_id', customerId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching purchase history:', error);
    throw error;
  }

  console.log('Raw sales data from database:', sales);

  return (sales || []).map(sale => {
    const itemsWithDetails = sale.sale_items?.map((item: any) => ({
      name: item.product?.name || 'منتج غير معروف',
      quantity: item.quantity || 0,
      unitPrice: Number(item.unit_price || 0),
      totalPrice: Number(item.total_price || 0),
      category: item.product?.category?.name || 'غير محدد'
    })) || [];

    return {
      id: sale.id,
      date: new Date(sale.created_at).toLocaleDateString('ar-IQ', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
      amount: Number(sale.final_amount || 0),
      items: itemsWithDetails.map(item => `${item.name} (${item.quantity})`),
      itemsWithDetails,
      status: 'مدفوع',
      paymentMethod: sale.payment_method || 'نقدي'
    };
  });
};

export const getCustomerPaymentHistory = async (customerId: string): Promise<CustomerPaymentHistory[]> => {
  console.log('Fetching payment history for customer:', customerId);
  
  // جلب مدفوعات الديون
  const { data: customerDebts, error: debtsFetchError } = await supabase
    .from('debts')
    .select('id')
    .eq('customer_id', customerId);

  if (debtsFetchError) {
    console.error('Error fetching customer debts:', debtsFetchError);
  }

  const debtIds = customerDebts?.map(debt => debt.id) || [];
  console.log('Customer debt IDs:', debtIds);

  let debtPayments: any[] = [];
  if (debtIds.length > 0) {
    const { data: payments, error: debtPaymentsError } = await supabase
      .from('debt_payments')
      .select(`
        id,
        payment_date,
        amount,
        notes,
        debt_id
      `)
      .in('debt_id', debtIds)
      .order('payment_date', { ascending: false });

    if (debtPaymentsError) {
      console.error('Error fetching debt payments:', debtPaymentsError);
    } else {
      debtPayments = payments || [];
    }
  }

  // جلب مدفوعات الأقساط
  const { data: customerInstallments, error: installmentsFetchError } = await supabase
    .from('installments')
    .select('id')
    .eq('customer_id', customerId);

  if (installmentsFetchError) {
    console.error('Error fetching customer installments:', installmentsFetchError);
  }

  const installmentIds = customerInstallments?.map(inst => inst.id) || [];
  console.log('Customer installment IDs:', installmentIds);

  let installmentPayments: any[] = [];
  if (installmentIds.length > 0) {
    const { data: payments, error: installmentPaymentsError } = await supabase
      .from('installment_payments')
      .select(`
        id,
        payment_date,
        amount,
        notes,
        installment_id
      `)
      .in('installment_id', installmentIds)
      .order('payment_date', { ascending: false });

    if (installmentPaymentsError) {
      console.error('Error fetching installment payments:', installmentPaymentsError);
    } else {
      installmentPayments = payments || [];
    }
  }

  const allPayments = [
    ...debtPayments.map(payment => ({
      id: payment.id,
      date: new Date(payment.payment_date).toLocaleDateString('ar-IQ', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
      amount: Number(payment.amount || 0),
      note: payment.notes || 'دفع دين',
      method: 'نقدي'
    })),
    ...installmentPayments.map(payment => ({
      id: payment.id,
      date: new Date(payment.payment_date).toLocaleDateString('ar-IQ', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
      amount: Number(payment.amount || 0),
      note: payment.notes || 'دفع قسط',
      method: 'نقدي'
    }))
  ];

  console.log('All customer payments found:', allPayments);
  return allPayments.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};

export const getCustomerInstallmentHistory = async (customerId: string): Promise<CustomerInstallmentHistory[]> => {
  console.log('Fetching enhanced installment history for customer:', customerId);
  
  const { data: installments, error } = await supabase
    .from('installments')
    .select('*')
    .eq('customer_id', customerId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching installments:', error);
    throw error;
  }

  console.log('Raw installments data from database:', installments);

  return (installments || []).map(installment => {
    const totalInstallments = Number(installment.months || 0);
    const paidInstallments = Number(installment.paid_months || 0);
    
    const nextPaymentDate = paidInstallments < totalInstallments 
      ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('ar-IQ', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      : 'مكتمل';

    // Enhanced items processing
    const itemsWithDetails = Array.isArray(installment.items) 
      ? installment.items.map((item: any) => ({
          name: item.name || 'منتج غير معروف',
          quantity: item.quantity || 0,
          category: item.category || 'غير محدد'
        }))
      : [];

    return {
      id: installment.id,
      totalAmount: Number(installment.total_amount || 0),
      downPayment: Number(installment.down_payment || 0),
      remainingAmount: Number(installment.remaining_amount || 0),
      monthlyPayment: Number(installment.monthly_payment || 0),
      paidInstallments,
      totalInstallments,
      nextPaymentDate,
      status: installment.status === 'completed' ? 'مكتمل' : 'نشط',
      items: itemsWithDetails.map(item => `${item.name} (${item.quantity})`),
      itemsWithDetails
    };
  });
};

export const getCustomerStats = async (customerId: string): Promise<CustomerStats> => {
  console.log('Calculating customer stats for:', customerId);
  
  try {
    // جلب جميع المبيعات المباشرة
    const { data: sales, error: salesError } = await supabase
      .from('sales')
      .select('id, final_amount, created_at')
      .eq('customer_id', customerId);

    if (salesError) {
      console.error('Error fetching sales for stats:', salesError);
    }

    // جلب جميع الديون
    const { data: debts, error: debtError } = await supabase
      .from('debts')
      .select('id, status, total_amount, remaining_amount, created_at')
      .eq('customer_id', customerId);

    if (debtError) {
      console.error('Error fetching debts for stats:', debtError);
    }

    // جلب جميع الأقساط
    const { data: installments, error: installmentError } = await supabase
      .from('installments')
      .select('id, status, total_amount, remaining_amount, created_at')
      .eq('customer_id', customerId);

    if (installmentError) {
      console.error('Error fetching installments for stats:', installmentError);
    }

    const salesData = sales || [];
    const debtsData = debts || [];
    const installmentsData = installments || [];

    // حساب إجمالي الفواتير (المبيعات + الديون + الأقساط)
    const totalInvoices = salesData.length + debtsData.length + installmentsData.length;
    
    // حساب الفواتير المدفوعة
    const paidSales = salesData.length; // كل المبيعات مدفوعة
    const paidDebts = debtsData.filter(debt => debt.status === 'paid').length;
    const completedInstallments = installmentsData.filter(inst => inst.status === 'completed').length;
    const paidInvoices = paidSales + paidDebts + completedInstallments;
    
    // حساب الفواتير المعلقة
    const pendingDebts = debtsData.filter(debt => debt.status === 'pending').length;
    const activeInstallments = installmentsData.filter(inst => inst.status === 'active').length;
    const pendingInvoices = pendingDebts + activeInstallments;
    
    // حساب متوسط الفاتورة من جميع المعاملات
    const totalSalesAmount = salesData.reduce((sum, sale) => sum + Number(sale.final_amount || 0), 0);
    const totalDebtsAmount = debtsData.reduce((sum, debt) => sum + Number(debt.total_amount || 0), 0);
    const totalInstallmentsAmount = installmentsData.reduce((sum, inst) => sum + Number(inst.total_amount || 0), 0);
    const totalAmount = totalSalesAmount + totalDebtsAmount + totalInstallmentsAmount;
    
    const averageInvoice = totalInvoices > 0 ? Math.round(totalAmount / totalInvoices) : 0;

    const stats = {
      totalInvoices,
      paidInvoices,
      pendingInvoices,
      averageInvoice
    };

    console.log('Customer stats calculated:', {
      ...stats,
      breakdown: {
        sales: { count: salesData.length, amount: totalSalesAmount },
        debts: { 
          total: debtsData.length, 
          paid: paidDebts, 
          pending: pendingDebts,
          amount: totalDebtsAmount 
        },
        installments: { 
          total: installmentsData.length, 
          completed: completedInstallments, 
          active: activeInstallments,
          amount: totalInstallmentsAmount 
        }
      }
    });

    return stats;
  } catch (error) {
    console.error('Error calculating customer stats:', error);
    return {
      totalInvoices: 0,
      paidInvoices: 0,
      pendingInvoices: 0,
      averageInvoice: 0
    };
  }
};

export const getCustomerFinancialSummary = async (customerId: string) => {
  console.log('Calculating financial summary for customer:', customerId);
  
  try {
    // جلب المبيعات المباشرة
    const { data: sales, error: salesError } = await supabase
      .from('sales')
      .select('final_amount, created_at')
      .eq('customer_id', customerId);

    if (salesError) {
      console.error('Error fetching sales for summary:', salesError);
    }

    // جلب الديون المعلقة فقط
    const { data: debts, error: debtError } = await supabase
      .from('debts')
      .select('total_amount, remaining_amount, status, created_at')
      .eq('customer_id', customerId)
      .eq('status', 'pending'); // فقط الديون المعلقة

    if (debtError) {
      console.error('Error fetching debts for summary:', debtError);
    }

    // جلب الأقساط النشطة فقط
    const { data: installments, error: installmentError } = await supabase
      .from('installments')
      .select('total_amount, remaining_amount, status, down_payment, created_at')
      .eq('customer_id', customerId)
      .eq('status', 'active'); // فقط الأقساط النشطة

    if (installmentError) {
      console.error('Error fetching installments for summary:', installmentError);
    }

    const salesData = sales || [];
    const debtsData = debts || [];
    const installmentsData = installments || [];

    // حساب إجمالي المشتريات المباشرة
    const totalPurchases = salesData.reduce((sum, sale) => sum + Number(sale.final_amount || 0), 0);

    // حساب الديون المعلقة (المبلغ المتبقي)
    const totalDebt = debtsData.reduce((sum, debt) => sum + Number(debt.remaining_amount || 0), 0);

    // حساب الأقساط المتبقية للأقساط النشطة
    const totalInstallments = installmentsData.reduce((sum, installment) => sum + Number(installment.remaining_amount || 0), 0);
    
    // حساب آخر عملية شراء مباشر
    const lastPurchase = salesData.length > 0 
      ? new Date(Math.max(...salesData.map(sale => new Date(sale.created_at).getTime()))).toLocaleDateString('ar-IQ', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      : 'لا يوجد';

    const summary = {
      totalPurchases: Math.round(totalPurchases),
      totalDebt: Math.round(totalDebt),
      totalInstallments: Math.round(totalInstallments),
      lastPurchase
    };

    console.log('Financial summary calculated:', {
      ...summary,
      breakdown: {
        salesCount: salesData.length,
        pendingDebtsCount: debtsData.length,
        activeInstallmentsCount: installmentsData.length
      }
    });

    return summary;
  } catch (error) {
    console.error('Error calculating financial summary:', error);
    return {
      totalPurchases: 0,
      totalDebt: 0,
      totalInstallments: 0,
      lastPurchase: 'لا يوجد'
    };
  }
};
