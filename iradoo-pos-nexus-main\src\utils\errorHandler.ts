
export const validateProductData = (productData: any): string | null => {
  if (!productData.name || productData.name.trim() === '') {
    return 'اسم المنتج مطلوب';
  }

  if (!productData.category_id) {
    return 'فئة المنتج مطلوبة';
  }

  if (!productData.barcode || productData.barcode.length < 8) {
    return 'الباركود يجب أن يكون 8 أرقام على الأقل';
  }

  if (productData.price <= 0) {
    return 'السعر يجب أن يكون أكبر من صفر';
  }

  if (productData.stock < 0) {
    return 'الكمية لا يمكن أن تكون أقل من صفر';
  }

  if (productData.min_stock < 0) {
    return 'الحد الأدنى لا يمكن أن يكون أقل من صفر';
  }

  return null;
};
