import CryptoJS from 'crypto-js';

export interface LicenseData {
  customerName: string;
  macAddress: string;
  deviceName: string;
  expirationDate?: string;
  features: string[];
  issuedDate: string;
  serialNumber: string;
}

export interface LicenseInfo {
  key: string;
  data: LicenseData;
  isValid: boolean;
  daysRemaining?: number;
  error?: string;
}

export interface LicenseRecord {
  id: string;
  licenseKey: string;
  customerName: string;
  customerEmail?: string;
  macAddress: string;
  deviceName: string;
  expirationDate?: string;
  features: string[];
  issuedDate: string;
  serialNumber: string;
  generatedBy: string;
  isActive: boolean;
  deactivatedBy?: string;
  deactivatedAt?: string;
}

const SECRET_KEY = 'ARIDOO_POS_LICENSE_KEY_2024_SECURE';
const LICENSE_STORAGE_KEY = 'aridoo-license-data';
const LICENSE_RECORDS_KEY = 'aridoo-license-records';

// المطورون المعفيون من الترخيص
const EXEMPT_DEVELOPERS = [
  '<EMAIL>'
];

export class LicenseManager {
  private static instance: LicenseManager;

  private constructor() {}

  public static getInstance(): LicenseManager {
    if (!LicenseManager.instance) {
      LicenseManager.instance = new LicenseManager();
    }
    return LicenseManager.instance;
  }

  // التحقق من كون المستخدم مطور معفي
  public isDeveloperExempt(): boolean {
    // التحقق من الجلسة المحلية للمستخدم
    const sessionData = localStorage.getItem('aridoo-current-session');
    if (sessionData) {
      try {
        const session = JSON.parse(sessionData);
        if (session?.user?.email && EXEMPT_DEVELOPERS.includes(session.user.email)) {
          console.log(`Developer exemption granted for: ${session.user.email}`);
          return true;
        }
      } catch (error) {
        console.error('Error checking developer exemption:', error);
      }
    }
    return false;
  }

  // توليد مفتاح ترخيص جديد
  public generateLicense(data: Omit<LicenseData, 'issuedDate' | 'serialNumber'>, generatedBy: string = 'system', customerEmail?: string): string {
    const licenseData: LicenseData = {
      ...data,
      issuedDate: new Date().toISOString(),
      serialNumber: this.generateSerialNumber()
    };

    const jsonData = JSON.stringify(licenseData);
    const encrypted = CryptoJS.AES.encrypt(jsonData, SECRET_KEY).toString();
    const base64License = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(encrypted));
    
    const licenseKey = `ARIDOO-${base64License}`;

    // حفظ سجل الترخيص
    this.saveLicenseRecord(licenseKey, licenseData, generatedBy, customerEmail);
    
    return licenseKey;
  }

  // فك تشفير والتحقق من المفتاح
  public validateLicense(licenseKey: string): LicenseInfo {
    try {
      if (!licenseKey.startsWith('ARIDOO-')) {
        return {
          key: licenseKey,
          data: {} as LicenseData,
          isValid: false,
          error: 'مفتاح الترخيص غير صالح - تنسيق خاطئ'
        };
      }

      // التحقق من حالة الترخيص في السجلات
      const record = this.getLicenseRecordByKey(licenseKey);
      if (record && !record.isActive) {
        return {
          key: licenseKey,
          data: record as LicenseData,
          isValid: false,
          error: 'تم إيقاف هذا الترخيص من قبل الإدارة'
        };
      }

      const base64Data = licenseKey.replace('ARIDOO-', '');
      const encryptedData = CryptoJS.enc.Base64.parse(base64Data).toString(CryptoJS.enc.Utf8);
      const decryptedBytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
      const decryptedData = decryptedBytes.toString(CryptoJS.enc.Utf8);
      
      if (!decryptedData) {
        return {
          key: licenseKey,
          data: {} as LicenseData,
          isValid: false,
          error: 'مفتاح الترخيص غير صالح - فشل في فك التشفير'
        };
      }

      const licenseData: LicenseData = JSON.parse(decryptedData);
      
      // التحقق من الجهاز
      const currentMac = this.getCurrentMacAddress();
      const currentDevice = this.getCurrentDeviceName();
      
      if (licenseData.macAddress !== currentMac && licenseData.deviceName !== currentDevice) {
        return {
          key: licenseKey,
          data: licenseData,
          isValid: false,
          error: 'مفتاح الترخيص غير صالح لهذا الجهاز'
        };
      }

      // التحقق من تاريخ الانتهاء
      if (licenseData.expirationDate) {
        const expirationDate = new Date(licenseData.expirationDate);
        const currentDate = new Date();
        
        if (currentDate > expirationDate) {
          return {
            key: licenseKey,
            data: licenseData,
            isValid: false,
            error: 'انتهت صلاحية مفتاح الترخيص'
          };
        }

        const daysRemaining = Math.ceil((expirationDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
        
        return {
          key: licenseKey,
          data: licenseData,
          isValid: true,
          daysRemaining
        };
      }

      return {
        key: licenseKey,
        data: licenseData,
        isValid: true
      };

    } catch (error) {
      return {
        key: licenseKey,
        data: {} as LicenseData,
        isValid: false,
        error: 'مفتاح الترخيص غير صالح - خطأ في التحليل'
      };
    }
  }

  // حفظ سجل الترخيص
  private saveLicenseRecord(licenseKey: string, data: LicenseData, generatedBy: string, customerEmail?: string): void {
    const records = this.getAllLicenseRecords();
    const record: LicenseRecord = {
      id: Date.now().toString(),
      licenseKey,
      customerName: data.customerName,
      customerEmail,
      macAddress: data.macAddress,
      deviceName: data.deviceName,
      expirationDate: data.expirationDate,
      features: data.features,
      issuedDate: data.issuedDate,
      serialNumber: data.serialNumber,
      generatedBy,
      isActive: true
    };

    records.push(record);
    localStorage.setItem(LICENSE_RECORDS_KEY, JSON.stringify(records));
    console.log(`License record saved for customer: ${data.customerName}`);
  }

  // الحصول على جميع سجلات التراخيص
  public getAllLicenseRecords(): LicenseRecord[] {
    try {
      const recordsData = localStorage.getItem(LICENSE_RECORDS_KEY);
      return recordsData ? JSON.parse(recordsData) : [];
    } catch (error) {
      console.error('Error getting license records:', error);
      return [];
    }
  }

  // البحث عن سجل ترخيص بالمفتاح
  public getLicenseRecordByKey(licenseKey: string): LicenseRecord | null {
    const records = this.getAllLicenseRecords();
    return records.find(record => record.licenseKey === licenseKey) || null;
  }

  // إيقاف ترخيص
  public deactivateLicense(licenseId: string, deactivatedBy: string): boolean {
    try {
      const records = this.getAllLicenseRecords();
      const recordIndex = records.findIndex(record => record.id === licenseId);
      
      if (recordIndex === -1) return false;
      
      records[recordIndex].isActive = false;
      records[recordIndex].deactivatedBy = deactivatedBy;
      records[recordIndex].deactivatedAt = new Date().toISOString();
      
      localStorage.setItem(LICENSE_RECORDS_KEY, JSON.stringify(records));
      console.log(`License deactivated by: ${deactivatedBy}`);
      return true;
    } catch (error) {
      console.error('Error deactivating license:', error);
      return false;
    }
  }

  // إعادة تفعيل ترخيص
  public reactivateLicense(licenseId: string): boolean {
    try {
      const records = this.getAllLicenseRecords();
      const recordIndex = records.findIndex(record => record.id === licenseId);
      
      if (recordIndex === -1) return false;
      
      records[recordIndex].isActive = true;
      delete records[recordIndex].deactivatedBy;
      delete records[recordIndex].deactivatedAt;
      
      localStorage.setItem(LICENSE_RECORDS_KEY, JSON.stringify(records));
      console.log(`License reactivated for: ${records[recordIndex].customerName}`);
      return true;
    } catch (error) {
      console.error('Error reactivating license:', error);
      return false;
    }
  }

  // حفظ مفتاح الترخيص
  public saveLicense(licenseKey: string): boolean {
    const validation = this.validateLicense(licenseKey);
    
    if (!validation.isValid) {
      return false;
    }

    localStorage.setItem(LICENSE_STORAGE_KEY, licenseKey);
    return true;
  }

  // استرجاع مفتاح الترخيص المحفوظ
  public getSavedLicense(): string | null {
    return localStorage.getItem(LICENSE_STORAGE_KEY);
  }

  // التحقق من حالة الترخيص الحالي
  public getCurrentLicenseStatus(): LicenseInfo | null {
    // إذا كان المستخدم مطور معفي، إرجاع ترخيص وهمي صالح
    if (this.isDeveloperExempt()) {
      return {
        key: 'DEVELOPER-EXEMPT',
        data: {
          customerName: 'Developer Account',
          macAddress: this.getCurrentMacAddress(),
          deviceName: this.getCurrentDeviceName(),
          features: ['sales', 'inventory', 'reports', 'customers', 'backup', 'multiUser'],
          issuedDate: new Date().toISOString(),
          serialNumber: 'DEV-EXEMPT'
        },
        isValid: true
      };
    }

    const savedLicense = this.getSavedLicense();
    
    if (!savedLicense) {
      return null;
    }

    return this.validateLicense(savedLicense);
  }

  // حذف الترخيص
  public removeLicense(): void {
    localStorage.removeItem(LICENSE_STORAGE_KEY);
  }

  // التحقق من وضع العرض فقط
  public isViewOnlyMode(): boolean {
    // إذا كان المستخدم مطور معفي، لا يكون في وضع العرض فقط
    if (this.isDeveloperExempt()) {
      return false;
    }
    
    const licenseStatus = this.getCurrentLicenseStatus();
    return !licenseStatus || !licenseStatus.isValid;
  }

  // التحقق من إمكانية تنفيذ عمليات التعديل
  public canPerformActions(): boolean {
    // إذا كان المستخدم مطور معفي، يمكنه تنفيذ جميع العمليات
    if (this.isDeveloperExempt()) {
      return true;
    }
    
    const licenseStatus = this.getCurrentLicenseStatus();
    return licenseStatus?.isValid === true;
  }

  // الحصول على عنوان MAC (محاكاة)
  private getCurrentMacAddress(): string {
    // في البيئة الحقيقية، يمكن الحصول على MAC من Node.js
    // هنا نستخدم fingerprint متقدم للمتصفح
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx!.textBaseline = 'top';
    ctx!.font = '14px Arial';
    ctx!.fillText('Device fingerprint', 2, 2);
    
    const fingerprint = canvas.toDataURL();
    return CryptoJS.MD5(fingerprint + navigator.userAgent + screen.width + screen.height).toString();
  }

  // الحصول على اسم الجهاز
  private getCurrentDeviceName(): string {
    return navigator.platform + '-' + navigator.userAgent.split(' ')[0];
  }

  // توليد رقم تسلسلي
  private generateSerialNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `AR${timestamp.slice(-6)}${random}`;
  }

  // التحقق من الميزات المتاحة
  public hasFeature(feature: string): boolean {
    // إذا كان المستخدم مطور معفي، لديه جميع الميزات
    if (this.isDeveloperExempt()) {
      return true;
    }
    
    const licenseStatus = this.getCurrentLicenseStatus();
    return licenseStatus?.isValid && licenseStatus.data.features.includes(feature) || false;
  }

  // الحصول على معلومات الجهاز الحالي
  public getCurrentDeviceInfo(): { macAddress: string; deviceName: string } {
    return {
      macAddress: this.getCurrentMacAddress(),
      deviceName: this.getCurrentDeviceName()
    };
  }

  // التحقق من وجود ترخيص صالح للإيميل
  public getLicenseByEmail(email: string): LicenseRecord | null {
    const records = this.getAllLicenseRecords();
    const record = records.find(record => 
      record.customerEmail?.toLowerCase() === email.toLowerCase() && 
      record.isActive
    );
    
    if (!record) return null;
    
    // التحقق من صلاحية الترخيص
    const validation = this.validateLicense(record.licenseKey);
    if (!validation.isValid) return null;
    
    return record;
  }

  // تسجيل دخول بالإيميل والترخيص
  public loginWithEmail(email: string): { success: boolean; licenseRecord?: LicenseRecord; error?: string } {
    const licenseRecord = this.getLicenseByEmail(email);
    
    if (!licenseRecord) {
      return { 
        success: false, 
        error: 'لا يوجد ترخيص صالح مرتبط بهذا البريد الإلكتروني' 
      };
    }

    // حفظ الترخيص كترخيص نشط
    this.saveLicense(licenseRecord.licenseKey);
    
    return { 
      success: true, 
      licenseRecord 
    };
  }
}

export const licenseManager = LicenseManager.getInstance();
