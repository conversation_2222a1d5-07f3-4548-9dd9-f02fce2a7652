
import { supabase } from '@/integrations/supabase/client';

export interface SecurityAuditLog {
  id: string;
  user_id: string;
  action: string;
  table_name?: string;
  record_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface UserSession {
  id: string;
  user_id: string;
  session_token: string;
  expires_at: string;
  ip_address?: string;
  user_agent?: string;
  is_active: boolean;
  created_at: string;
  last_activity: string;
}

export interface FailedLoginAttempt {
  id: string;
  email: string;
  ip_address?: string;
  attempted_at: string;
  user_agent?: string;
}

// Enhanced email validation
export const validateEmailEnhanced = (email: string): boolean => {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email) && email.length <= 254;
};

// Enhanced password validation
export const validatePasswordEnhanced = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
  }
  
  if (password.length > 128) {
    errors.push('كلمة المرور طويلة جداً (أكثر من 128 حرف)');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
  }
  
  // Check for common weak passwords
  const commonPasswords = ['password', '12345678', 'qwerty', 'abc123', 'password123'];
  if (commonPasswords.some(common => password.toLowerCase().includes(common))) {
    errors.push('كلمة المرور ضعيفة جداً، يرجى اختيار كلمة مرور أقوى');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Enhanced input sanitization
export const sanitizeInputEnhanced = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+=/gi, '')
    .replace(/script/gi, '')
    .slice(0, 1000); // Prevent extremely long inputs
};

// Phone number validation
export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

// Barcode validation
export const validateBarcode = (barcode: string): boolean => {
  const barcodeRegex = /^[0-9]{8,13}$/;
  return barcodeRegex.test(barcode);
};

// Check if account is locked using the new database function
export const isAccountLockedEnhanced = async (email: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('is_account_locked', { user_email: email });
    if (error) {
      console.error('Error checking account lock status:', error);
      return false;
    }
    return data || false;
  } catch (error) {
    console.error('Error checking account lock status:', error);
    return false;
  }
};

// Enhanced login attempt logging
export const logFailedLoginAttemptEnhanced = async (email: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('failed_login_attempts')
      .insert({
        email: sanitizeInputEnhanced(email),
        user_agent: navigator.userAgent.slice(0, 500) // Limit user agent length
      });
    
    if (error) {
      console.error('Error logging failed login attempt:', error);
    }
  } catch (error) {
    console.error('Error logging failed login attempt:', error);
  }
};

// Get current user role using the new security function
export const getCurrentUserRoleEnhanced = async (): Promise<string | null> => {
  try {
    const { data, error } = await supabase.rpc('get_current_user_role');
    if (error) {
      console.error('Error getting user role:', error);
      return null;
    }
    return data;
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
};

// Check if user has specific role using the new security function
export const hasRoleEnhanced = async (role: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('has_role', { required_role: role });
    if (error) {
      console.error('Error checking user role:', error);
      return false;
    }
    return data || false;
  } catch (error) {
    console.error('Error checking user role:', error);
    return false;
  }
};

// Check if user is admin using the new security function
export const isAdminEnhanced = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('is_admin');
    if (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
    return data || false;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
};

// Get security audit logs (admin only)
export const getSecurityAuditLogsEnhanced = async (): Promise<SecurityAuditLog[]> => {
  try {
    const { data, error } = await supabase
      .from('security_audit')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(100);
    
    if (error) {
      console.error('Error fetching audit logs:', error);
      return [];
    }
    
    return (data || []) as SecurityAuditLog[];
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return [];
  }
};

// Get user sessions (admin only)
export const getUserSessionsEnhanced = async (): Promise<UserSession[]> => {
  try {
    const { data, error } = await supabase
      .from('user_sessions')
      .select('*')
      .eq('is_active', true)
      .order('last_activity', { ascending: false });
    
    if (error) {
      console.error('Error fetching user sessions:', error);
      return [];
    }
    
    return (data || []) as UserSession[];
  } catch (error) {
    console.error('Error fetching user sessions:', error);
    return [];
  }
};

// Terminate user session
export const terminateSessionEnhanced = async (sessionId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('user_sessions')
      .update({ is_active: false })
      .eq('id', sessionId);
    
    if (error) {
      console.error('Error terminating session:', error);
    }
  } catch (error) {
    console.error('Error terminating session:', error);
  }
};

// Create secure session tracking
export const createUserSession = async (userId: string, sessionToken: string): Promise<void> => {
  try {
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour session

    const { error } = await supabase
      .from('user_sessions')
      .insert({
        user_id: userId,
        session_token: sessionToken,
        expires_at: expiresAt.toISOString(),
        user_agent: navigator.userAgent.slice(0, 500)
      });
    
    if (error) {
      console.error('Error creating user session:', error);
    }
  } catch (error) {
    console.error('Error creating user session:', error);
  }
};

// Update session activity
export const updateSessionActivity = async (sessionToken: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('user_sessions')
      .update({ last_activity: new Date().toISOString() })
      .eq('session_token', sessionToken)
      .eq('is_active', true);
    
    if (error) {
      console.error('Error updating session activity:', error);
    }
  } catch (error) {
    console.error('Error updating session activity:', error);
  }
};
