
import React, { useState, useEffect } from 'react';
import { Spark<PERSON> } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Plus, Package, AlertTriangle, Edit, Trash2, ClipboardCheck, ShoppingCart, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import AddProductDialog from './AddProductDialog';
import EditProductDialog from './EditProductDialog';
import CategoryManager from './CategoryManager';
import InventoryAdjustmentDialog from './InventoryAdjustmentDialog';
import PurchasesDialog from './PurchasesDialog';
import InventoryCountDialog from './InventoryCountDialog';
import { 
  getProducts, 
  getCategories, 
  deleteProduct, 
  generateBarcode,
  type Product,
  type Category 
} from '@/utils/database';

const InventoryPage = () => {
  const { toast } = useToast();
  const { t } = useSettings();
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [showAdjustmentDialog, setShowAdjustmentDialog] = useState(false);
  const [showPurchasesDialog, setShowPurchasesDialog] = useState(false);
  const [showCountDialog, setShowCountDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  const loadData = async () => {
    try {
      setLoading(true);
      console.log('Loading inventory data...');
      
      const [productsData, categoriesData] = await Promise.all([
        getProducts(),
        getCategories()
      ]);
      
      console.log('Products loaded:', productsData.length);
      console.log('Categories loaded:', categoriesData.length);
      
      setProducts(productsData);
      setCategories(categoriesData);
      
      toast({
        title: "تم تحميل البيانات",
        description: `تم تحميل ${productsData.length} منتج و ${categoriesData.length} فئة بنجاح`,
      });
    } catch (error: any) {
      console.error('Error loading inventory data:', error);
      toast({
        title: "خطأ في تحميل البيانات",
        description: error.message || "حدث خطأ أثناء تحميل بيانات المخزون. يرجى المحاولة مرة أخرى.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const getStockStatus = (stock: number, minStock: number) => {
    if (stock === 0) return { label: 'نفاد', color: 'bg-red-100 text-red-800' };
    if (stock <= minStock) return { label: 'منخفض', color: 'bg-yellow-100 text-yellow-800' };
    return { label: 'متوفر', color: 'bg-green-100 text-green-800' };
  };

  const handleAddProduct = async () => {
    try {
      console.log('Refreshing data after adding product...');
      await loadData();
      setShowAddDialog(false);
      toast({
        title: "تم إضافة المنتج",
        description: "تم إضافة المنتج بنجاح",
      });
    } catch (error: any) {
      console.error('Error refreshing data after add:', error);
      toast({
        title: "خطأ في إضافة المنتج",
        description: error.message || "حدث خطأ أثناء إضافة المنتج",
        variant: "destructive"
      });
    }
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    setShowEditDialog(true);
  };

  const handleUpdateProduct = async () => {
    try {
      console.log('Refreshing data after updating product...');
      await loadData();
      setShowEditDialog(false);
      setSelectedProduct(null);
      toast({
        title: "تم تحديث المنتج",
        description: "تم تحديث المنتج بنجاح",
      });
    } catch (error: any) {
      console.error('Error refreshing data after update:', error);
      toast({
        title: "خطأ في تحديث المنتج",
        description: error.message || "حدث خطأ أثناء تحديث المنتج",
        variant: "destructive"
      });
    }
  };

  const handleDeleteProduct = async (id: string) => {
    const productToDelete = products.find(p => p.id === id);
    try {
      console.log('Deleting product:', id);
      await deleteProduct(id);
      await loadData();
      toast({
        title: "تم حذف المنتج",
        description: `تم حذف ${productToDelete?.name || 'المنتج'} بنجاح`,
      });
    } catch (error: any) {
      console.error('Error deleting product:', error);
      toast({
        title: "خطأ في حذف المنتج",
        description: error.message || "حدث خطأ أثناء حذف المنتج. يرجى المحاولة مرة أخرى.",
        variant: "destructive"
      });
    }
  };

  const handleUpdateCategories = async () => {
    try {
      await loadData();
      toast({
        title: "تم تحديث الفئات",
        description: "تم تحديث قائمة الفئات بنجاح",
      });
    } catch (error: any) {
      console.error('Error updating categories:', error);
      toast({
        title: "خطأ في تحديث الفئات",
        description: error.message || "حدث خطأ أثناء تحديث الفئات",
        variant: "destructive"
      });
    }
  };

  const handleAdjustStock = (product: Product) => {
    setSelectedProduct(product);
    setShowAdjustmentDialog(true);
  };

  const handleAdjustmentComplete = async () => {
    try {
      await loadData();
      setShowAdjustmentDialog(false);
      setSelectedProduct(null);
      toast({
        title: "تم تعديل المخزون",
        description: "تم تعديل المخزون بنجاح",
      });
    } catch (error: any) {
      console.error('Error after adjustment:', error);
      toast({
        title: "خطأ في تعديل المخزون",
        description: error.message || "حدث خطأ أثناء تعديل المخزون",
        variant: "destructive"
      });
    }
  };

  const handlePurchaseComplete = async () => {
    try {
      await loadData();
      setShowPurchasesDialog(false);
      toast({
        title: "تم تسجيل المشتريات",
        description: "تم تسجيل المشتريات بنجاح",
      });
    } catch (error: any) {
      console.error('Error after purchase:', error);
      toast({
        title: "خطأ في تسجيل المشتريات",
        description: error.message || "حدث خطأ أثناء تسجيل المشتريات",
        variant: "destructive"
      });
    }
  };

  const handleCountComplete = async () => {
    try {
      await loadData();
      setShowCountDialog(false);
      toast({
        title: "تم تحديث الجرد",
        description: "تم تحديث الجرد بنجاح",
      });
    } catch (error: any) {
      console.error('Error after count:', error);
      toast({
        title: "خطأ في تحديث الجرد",
        description: error.message || "حدث خطأ أثناء تحديث الجرد",
        variant: "destructive"
      });
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.barcode && product.barcode.includes(searchTerm))
  );

  const totalProducts = products.length;
  const lowStockProducts = products.filter(p => p.stock <= p.min_stock && p.stock > 0).length;
  const outOfStockProducts = products.filter(p => p.stock === 0).length;
  const totalValue = products.reduce((sum, product) => sum + (product.price * product.stock), 0);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">جاري تحميل البيانات...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      {/* شريط البرنامج مع الشعار */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3 animate-fade-in">
          <div className="w-12 h-12 rounded-xl bg-primary flex items-center justify-center shadow-md">
            <Sparkles className="w-6 h-6 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-primary">
              أريدوو
            </h1>
            <p className="text-sm text-muted-foreground">نظام نقطة البيع المتقدم</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowAddDialog(true)} className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            إضافة منتج
          </Button>
          <Button onClick={() => setShowCategoryManager(true)} variant="outline" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            إدارة الفئات
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card className="bg-white shadow-sm border border-gray-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-50 rounded-lg border border-blue-100">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">إجمالي المنتجات</p>
                <p className="text-2xl font-bold text-gray-900">{totalProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-sm border border-gray-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-50 rounded-lg border border-green-100">
                <Package className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">قيمة المخزون</p>
                <p className="text-2xl font-bold text-gray-900">{totalValue.toLocaleString()} د.ع</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-sm border border-gray-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-50 rounded-lg border border-yellow-100">
                <AlertTriangle className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">مخزون منخفض</p>
                <p className="text-2xl font-bold text-gray-900">{lowStockProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-sm border border-gray-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-50 rounded-lg border border-red-100">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">نفاد المخزون</p>
                <p className="text-2xl font-bold text-gray-900">{outOfStockProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card className="mb-6 bg-white shadow-sm border border-gray-200">
        <CardContent className="p-4">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="البحث في المنتجات أو الباركود..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10 text-right bg-white border-gray-200"
              />
            </div>
            <Button variant="outline" className="border-gray-200">تصفية</Button>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card className="bg-white shadow-sm border border-gray-200">
        <CardHeader className="border-b border-gray-100">
          <CardTitle className="text-gray-900">قائمة المنتجات ({filteredProducts.length})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 border-b border-gray-100">
                <TableHead className="text-right text-gray-700">الصورة</TableHead>
                <TableHead className="text-right text-gray-700">اسم المنتج</TableHead>
                <TableHead className="text-right text-gray-700">الفئة</TableHead>
                <TableHead className="text-right text-gray-700">الباركود</TableHead>
                <TableHead className="text-right text-gray-700">السعر</TableHead>
                <TableHead className="text-right text-gray-700">الكمية</TableHead>
                <TableHead className="text-right text-gray-700">الحد الأدنى</TableHead>
                <TableHead className="text-right text-gray-700">الحالة</TableHead>
                <TableHead className="text-right text-gray-700">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product) => {
                const status = getStockStatus(product.stock, product.min_stock);
                return (
                  <TableRow key={product.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <TableCell className="py-4">
                      {product.image ? (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-10 h-10 object-cover rounded border border-gray-200"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center border border-gray-200">
                          <Package className="w-5 h-5 text-gray-400" />
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="font-medium text-gray-900">{product.name}</TableCell>
                    <TableCell className="text-gray-600">{product.category?.name}</TableCell>
                    <TableCell className="font-mono text-sm text-gray-600">{product.barcode}</TableCell>
                    <TableCell className="text-gray-900">{product.price.toLocaleString()} د.ع</TableCell>
                    <TableCell className="text-gray-900">{product.stock}</TableCell>
                    <TableCell className="text-gray-600">{product.min_stock}</TableCell>
                    <TableCell>
                      <Badge className={status.color}>
                        {status.label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          onClick={() => handleAdjustStock(product)}
                          variant="outline" 
                          size="sm"
                          className="text-blue-600 hover:text-blue-700 border-gray-200"
                        >
                          <Settings className="w-4 h-4" />
                        </Button>
                        <Button 
                          onClick={() => handleEditProduct(product)}
                          variant="outline" 
                          size="sm"
                          className="border-gray-200"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button 
                          onClick={() => handleDeleteProduct(product.id)}
                          variant="outline" 
                          size="sm" 
                          className="text-red-600 hover:text-red-700 border-gray-200"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
              {filteredProducts.length === 0 && (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-gray-500">
                    لا توجد منتجات مطابقة للبحث
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <AddProductDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onAddProduct={handleAddProduct}
        categories={categories}
        onManageCategories={() => setShowCategoryManager(true)}
      />

      <EditProductDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onEditProduct={handleUpdateProduct}
        product={selectedProduct}
        categories={categories}
        onManageCategories={() => setShowCategoryManager(true)}
      />

      <CategoryManager
        open={showCategoryManager}
        onOpenChange={setShowCategoryManager}
        categories={categories}
        onUpdateCategories={handleUpdateCategories}
      />

      <InventoryAdjustmentDialog
        open={showAdjustmentDialog}
        onOpenChange={setShowAdjustmentDialog}
        product={selectedProduct}
        onAdjustment={handleAdjustmentComplete}
      />

      <PurchasesDialog
        open={showPurchasesDialog}
        onOpenChange={setShowPurchasesDialog}
        products={products}
        categories={categories}
        onPurchaseComplete={handlePurchaseComplete}
      />

      <InventoryCountDialog
        open={showCountDialog}
        onOpenChange={setShowCountDialog}
        products={products}
        onCountComplete={handleCountComplete}
      />
    </div>
  );
};

export default InventoryPage;
