@echo off
chcp 65001 >nul 2>&1
echo ========================================
echo    System Requirements Check
echo ========================================
echo.

echo [1/5] Checking Operating System...
ver
echo.

echo [2/5] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js installed:
    node --version
) else (
    echo ❌ Node.js NOT installed!
    echo Please download from: https://nodejs.org
    echo Recommended: LTS version
)
echo.

echo [3/5] Checking npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm available:
    npm --version
) else (
    echo ❌ npm NOT available!
)
echo.

echo [4/5] Checking Git (optional)...
git --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Git installed:
    git --version
) else (
    echo ⚠️ Git not installed (optional)
)
echo.

echo [5/5] Checking disk space...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set free_space=%%a
echo المساحة المتاحة: %free_space% bytes
echo.

echo ========================================
echo تحقق من ملفات المشروع...
echo ========================================

if exist "package.json" (
    echo ✅ package.json موجود
) else (
    echo ❌ package.json مفقود!
)

if exist "main.js" (
    echo ✅ main.js موجود
) else (
    echo ❌ main.js مفقود!
)

if exist "webapp" (
    echo ✅ مجلد webapp موجود
    if exist "webapp\index.html" (
        echo ✅ index.html موجود في webapp
    ) else (
        echo ⚠️ index.html مفقود في webapp
    )
) else (
    echo ⚠️ مجلد webapp مفقود
)

if exist "assets" (
    echo ✅ مجلد assets موجود
    if exist "assets\icon.png" (
        echo ✅ icon.png موجود
    ) else (
        echo ⚠️ icon.png مفقود في assets
    )
    if exist "assets\icon.ico" (
        echo ✅ icon.ico موجود
    ) else (
        echo ⚠️ icon.ico مفقود في assets
    )
) else (
    echo ⚠️ مجلد assets مفقود
)

echo.
echo ========================================
echo ملخص الحالة:
echo ========================================

set ready=1

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ يجب تثبيت Node.js أولاً
    set ready=0
)

if not exist "package.json" (
    echo ❌ ملفات المشروع مفقودة
    set ready=0
)

if %ready% equ 1 (
    echo ✅ النظام جاهز للاستخدام!
    echo يمكنك تشغيل:
    echo   - setup.bat للإعداد الأولي
    echo   - copy-webapp.bat لنسخ ملفات التطبيق
    echo   - start.bat لتشغيل التطبيق
    echo   - build.bat لبناء التطبيق
) else (
    echo ❌ النظام غير جاهز
    echo يرجى إصلاح المشاكل المذكورة أعلاه
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
