
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface SettingsState {
  // General Settings
  storeName: string;
  storeAddress: string;
  storePhone: string;
  storeEmail: string;
  taxNumber: string;
  language: 'ar' | 'ku' | 'en';
  timezone: string;
  enableRTL: boolean;
  autoBackup: boolean;
  lowStockAlert: boolean;
  lowStockThreshold: number;

  // Currency Settings
  currency: string;
  currencySymbol: string;
  currencyPosition: 'before' | 'after';
  decimalPlaces: number;
  thousandSeparator: string;
  decimalSeparator: string;
  taxEnabled: boolean;
  taxRate: number;
  taxName: string;
  showTaxInPrice: boolean;
  roundingMethod: 'round' | 'floor' | 'ceil';

  // Printer Settings
  printerName: string;
  paperSize: '58mm' | '80mm' | 'A4';
  autoPrint: boolean;
  printLogo: boolean;
  printQR: boolean;
  receiptFooter: string;
  copies: number;
  fontSize: 'small' | 'medium' | 'large';

  // Receipt Design Settings
  receiptTemplate: 'classic' | 'modern' | 'minimal' | 'colorful';

  // Security Settings
  sessionTimeout: number;
  requirePasswordChange: boolean;
  passwordMinLength: number;
  enableTwoFactor: boolean;
  loginAttempts: number;
  lockoutDuration: number;

  // Notification Settings
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  lowStockNotifications: boolean;
  salesNotifications: boolean;
  systemNotifications: boolean;
}

interface SettingsContextType {
  settings: SettingsState;
  updateSettings: (newSettings: Partial<SettingsState>) => void;
  formatCurrency: (amount: number) => string;
  t: (key: string) => string;
}

const defaultSettings: SettingsState = {
  // General Settings
  storeName: 'أريدوو',
  storeAddress: 'بغداد، العراق',
  storePhone: '+*********** 4567',
  storeEmail: '<EMAIL>',
  taxNumber: '123456789012345',
  language: 'ar',
  timezone: 'Asia/Baghdad',
  enableRTL: true,
  autoBackup: true,
  lowStockAlert: true,
  lowStockThreshold: 10,

  // Currency Settings
  currency: 'IQD',
  currencySymbol: 'د.ع',
  currencyPosition: 'after',
  decimalPlaces: 0,
  thousandSeparator: ',',
  decimalSeparator: '.',
  taxEnabled: false,
  taxRate: 0,
  taxName: 'ضريبة القيمة المضافة',
  showTaxInPrice: true,
  roundingMethod: 'round',

  // Printer Settings
  printerName: 'Epson TM-T20',
  paperSize: '80mm',
  autoPrint: true,
  printLogo: true,
  printQR: true,
  receiptFooter: 'شكراً لزيارتكم - أريدوو',
  copies: 1,
  fontSize: 'medium',

  // Receipt Design Settings
  receiptTemplate: 'classic',

  // Security Settings
  sessionTimeout: 30,
  requirePasswordChange: false,
  passwordMinLength: 6,
  enableTwoFactor: false,
  loginAttempts: 5,
  lockoutDuration: 15,

  // Notification Settings
  emailNotifications: true,
  smsNotifications: false,
  pushNotifications: true,
  lowStockNotifications: true,
  salesNotifications: true,
  systemNotifications: true,
};

// Translation object with complete translations
const translations = {
  ar: {
    // Navigation
    dashboard: 'الرئيسية',
    sales: 'المبيعات',
    inventory: 'المخزون',
    customers: 'العملاء',
    reports: 'التقارير',
    settings: 'الإعدادات',
    
    // Common terms
    total_sales: 'إجمالي المبيعات',
    products: 'المنتجات',
    low_stock: 'مخزون منخفض',
    recent_orders: 'الطلبات الأخيرة',
    save: 'حفظ',
    cancel: 'إلغاء',
    add: 'إضافة',
    edit: 'تعديل',
    delete: 'حذف',
    search: 'بحث',
    name: 'الاسم',
    price: 'السعر',
    quantity: 'الكمية',
    total: 'الإجمالي',
    
    // Settings page translations
    settings_title: 'الإعدادات',
    settings_description: 'إدارة إعدادات النظام والتخصيصات',
    general: 'عام',
    currency: 'العملة',
    printer: 'الطابعة',
    receipt_design: 'تصميم الفاتورة',
    security: 'الأمان',
    notifications: 'الإشعارات',
    
    // General settings
    store_information: 'معلومات المتجر',
    store_information_desc: 'تحديث معلومات المتجر الأساسية',
    store_name: 'اسم المتجر',
    phone_number: 'رقم الهاتف',
    address: 'العنوان',
    email: 'البريد الإلكتروني',
    tax_number: 'الرقم الضريبي',
    system_settings: 'إعدادات النظام',
    system_settings_desc: 'تخصيص سلوك النظام والواجهة',
    system_language: 'لغة النظام',
    auto_backup: 'النسخ الاحتياطي التلقائي',
    auto_backup_desc: 'إنشاء نسخة احتياطية من البيانات يومياً',
    low_stock_alert: 'تنبيه نفاد المخزون',
    low_stock_alert_desc: 'إرسال تنبيه عند انخفاض كمية المنتج',
    stock_alert_threshold: 'حد التنبيه للمخزون',
    save_settings: 'حفظ الإعدادات',
    saved: 'تم الحفظ',
    general_settings_saved: 'تم حفظ الإعدادات العامة بنجاح',
    applied: 'تم التطبيق',
    changes_applied: 'تم تطبيق التغييرات على كامل التطبيق',
    
    // Sales page
    point_of_sale: 'نقطة البيع',
    product_count: 'عدد المنتجات',
    load_draft: 'تحميل مسودة',
    search_placeholder: 'البحث بالاسم أو الباركود...',
    available: 'المتوفر',
    out_of_stock: 'نفد المخزون',
    shopping_cart: 'سلة المشتريات',
    empty_cart: 'السلة فارغة',
    subtotal: 'المجموع الجزئي',
    complete_sale: 'إتمام البيع',
    save_draft: 'حفظ كمسودة',
    cancel_operation: 'إلغاء العملية',
    
    // Security settings
    security_settings: 'إعدادات الأمان',
    security_settings_desc: 'تكوين إعدادات الأمان وكلمات المرور',
    session_timeout: 'مهلة الجلسة (دقيقة)',
    password_min_length: 'الحد الأدنى لطول كلمة المرور',
    login_attempts: 'عدد محاولات تسجيل الدخول',
    lockout_duration: 'مدة القفل (دقيقة)',
    two_factor_auth: 'المصادقة الثنائية',
    two_factor_desc: 'تفعيل المصادقة الثنائية لحماية إضافية',
    require_password_change: 'إجبار تغيير كلمة المرور',
    require_password_desc: 'إجبار المستخدمين على تغيير كلمة المرور دورياً',

    // Additional settings translations
    backup: 'النسخ الاحتياطية',
    license: 'الترخيص',
    user_management: 'إدارة المستخدمين',
    account_management: 'تعديل الحسابات',
    security_logs: 'سجلات الأمان',
    license_generator: 'مولد التراخيص',

    // Dashboard translations
    welcome: 'مرحباً',
    today_sales: 'مبيعات اليوم',
    total_revenue: 'إجمالي الإيرادات',
    orders_today: 'طلبات اليوم',
    top_products: 'أفضل المنتجات',
    recent_transactions: 'المعاملات الأخيرة',
    quick_actions: 'إجراءات سريعة',
    view_all: 'عرض الكل',
    no_data: 'لا توجد بيانات',
    loading: 'جاري التحميل...',

    // Inventory translations
    add_product: 'إضافة منتج',
    edit_product: 'تعديل منتج',
    delete_product: 'حذف منتج',
    product_name: 'اسم المنتج',
    product_code: 'كود المنتج',
    barcode: 'الباركود',
    category: 'الفئة',
    cost_price: 'سعر التكلفة',
    selling_price: 'سعر البيع',
    stock_quantity: 'كمية المخزون',
    minimum_stock: 'الحد الأدنى للمخزون',
    description: 'الوصف',
    image: 'الصورة',
    status: 'الحالة',
    active: 'نشط',
    inactive: 'غير نشط',

    // Customer translations
    add_customer: 'إضافة عميل',
    edit_customer: 'تعديل عميل',
    delete_customer: 'حذف عميل',
    customer_name: 'اسم العميل',
    customer_phone: 'هاتف العميل',
    customer_email: 'بريد العميل',
    customer_address: 'عنوان العميل',
    customer_notes: 'ملاحظات العميل',

    // Reports translations
    sales_report: 'تقرير المبيعات',
    inventory_report: 'تقرير المخزون',
    customer_report: 'تقرير العملاء',
    financial_report: 'التقرير المالي',
    daily_report: 'التقرير اليومي',
    weekly_report: 'التقرير الأسبوعي',
    monthly_report: 'التقرير الشهري',
    yearly_report: 'التقرير السنوي',
    from_date: 'من تاريخ',
    to_date: 'إلى تاريخ',
    generate_report: 'إنشاء تقرير',
    export_pdf: 'تصدير PDF',
    export_excel: 'تصدير Excel',
    print_report: 'طباعة التقرير',

    // Common UI translations
    confirm: 'تأكيد',
    yes: 'نعم',
    no: 'لا',
    ok: 'موافق',
    close: 'إغلاق',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    finish: 'إنهاء',
    submit: 'إرسال',
    reset: 'إعادة تعيين',
    clear: 'مسح',
    refresh: 'تحديث',
    update: 'تحديث',
    create: 'إنشاء',
    remove: 'إزالة',
    select: 'اختيار',
    upload: 'رفع',
    download: 'تحميل',

    // Form validation translations
    required_field: 'هذا الحقل مطلوب',
    invalid_email: 'البريد الإلكتروني غير صحيح',
    invalid_phone: 'رقم الهاتف غير صحيح',
    password_too_short: 'كلمة المرور قصيرة جداً',
    passwords_not_match: 'كلمات المرور غير متطابقة',
    invalid_number: 'رقم غير صحيح',
    min_value: 'القيمة الدنيا',
    max_value: 'القيمة العليا',

    // Success/Error messages
    success: 'نجح',
    error: 'خطأ',
    warning: 'تحذير',
    info: 'معلومات',
    operation_successful: 'تمت العملية بنجاح',
    operation_failed: 'فشلت العملية',
    data_saved: 'تم حفظ البيانات',
    data_deleted: 'تم حذف البيانات',
    confirm_delete: 'هل أنت متأكد من الحذف؟',
    cannot_undo: 'لا يمكن التراجع عن هذا الإجراء',

    // Date/Time translations
    today: 'اليوم',
    yesterday: 'أمس',
    tomorrow: 'غداً',
    this_week: 'هذا الأسبوع',
    this_month: 'هذا الشهر',
    this_year: 'هذا العام',
    date: 'التاريخ',
    time: 'الوقت',

    // Currency and numbers
    currency: 'العملة',
    amount: 'المبلغ',
    discount: 'الخصم',
    tax: 'الضريبة',
    profit: 'الربح',
    loss: 'الخسارة',

    // Additional UI translations
    pos_system: 'نظام نقطة البيع المتقدم',
    admin: 'مدير النظام',
    cashier: 'كاشير',
    supervisor: 'مشرف',
    user: 'مستخدم',
    logout: 'تسجيل الخروج',
    notifications: 'التنبيهات والإشعارات',
    low_stock: 'مخزون منخفض',
    products: 'المنتجات',

    // Reports specific translations
    period: 'الفترة',
    today: 'اليوم',
    this_week: 'هذا الأسبوع',
    this_month: 'هذا الشهر',
    this_year: 'هذا العام',
    all_time: 'كل الأوقات',
    total_revenue: 'إجمالي الإيرادات',
    total_transactions: 'إجمالي المعاملات',
    total_cost: 'إجمالي التكلفة',
    total_profit: 'إجمالي الربح',
    profit_margin: 'هامش الربح',
    average_transaction: 'متوسط المعاملة',
    pending_debts: 'الديون المعلقة',
    active_installments: 'الأقساط النشطة',
    top_selling_products: 'أفضل المنتجات مبيعاً',
    top_customers: 'أفضل العملاء',
    category_breakdown: 'تفصيل حسب الفئة',
    inventory_analysis: 'تحليل المخزون',
    recent_transactions: 'المعاملات الأخيرة',
    search_invoice: 'البحث برقم الفاتورة',
    invoice_number: 'رقم الفاتورة',
    search: 'بحث',
    view_details: 'عرض التفاصيل',
    export_report: 'تصدير التقرير',
    generate_report: 'إنشاء تقرير',
    sales_chart: 'مخطط المبيعات',
    profit_chart: 'مخطط الأرباح',
    category_chart: 'مخطط الفئات',
    stock_status: 'حالة المخزون',
    out_of_stock: 'نفاد المخزون',
    low_stock: 'مخزون منخفض',
    good_stock: 'مخزون جيد',
    over_stock: 'مخزون زائد',
    customer_name: 'اسم العميل',
    phone: 'الهاتف',
    total_purchases: 'إجمالي المشتريات',
    last_visit: 'آخر زيارة',
    transaction_count: 'عدد المعاملات',
    pending_amount: 'المبلغ المعلق',
    payment_method: 'طريقة الدفع',
    cash: 'نقداً',
    card: 'بطاقة',
    debt: 'دين',
    installment: 'قسط',
    completed: 'مكتمل',
    pending: 'معلق',
    cancelled: 'ملغي',
    high_priority: 'أولوية عالية',
    medium_priority: 'أولوية متوسطة',
    low_priority: 'أولوية منخفضة',
    days_pending: 'أيام الانتظار',
    payment_progress: 'تقدم الدفع',
    months_remaining: 'الأشهر المتبقية',
    next_payment: 'الدفعة التالية',
    monthly_payment: 'الدفعة الشهرية',
    down_payment: 'الدفعة المقدمة',
    remaining_amount: 'المبلغ المتبقي',
    total_paid: 'إجمالي المدفوع',
    notes: 'ملاحظات',
    created_at: 'تم الإنشاء في',
    updated_at: 'تم التحديث في',

    // Additional missing translations
    no_data_available: 'لا توجد بيانات متاحة',
    undefined: 'غير محدد',
    unknown: 'غير معروف',
    not_specified: 'غير محدد',
    good: 'جيد',
    excellent: 'ممتاز',
    poor: 'ضعيف',
    priority: 'الأولوية',
    status: 'الحالة',
    type: 'النوع',
    category: 'الفئة',
    item: 'عنصر',
    items: 'عناصر',
    unit_price: 'سعر الوحدة',
    total_price: 'السعر الإجمالي',
    quantity: 'الكمية',
    subtotal: 'المجموع الفرعي',
    discount: 'الخصم',
    tax: 'الضريبة',
    grand_total: 'المجموع الكلي',
    payment_status: 'حالة الدفع',
    transaction_id: 'معرف المعاملة',
    reference: 'رقم المرجع'
  },
  ku: {
    // Navigation
    dashboard: 'سەرەکی',
    sales: 'فرۆشتن',
    inventory: 'کۆگا',
    customers: 'کڕیارەکان',
    reports: 'راپۆرتەکان',
    settings: 'ڕێکخستنەکان',
    
    // Common terms
    total_sales: 'کۆی فرۆشتن',
    products: 'بەرهەمەکان',
    low_stock: 'کۆگای کەم',
    recent_orders: 'داواکارییە نوێیەکان',
    save: 'پاشەکەوتکردن',
    cancel: 'هەڵوەشاندنەوە',
    add: 'زیادکردن',
    edit: 'دەستکاریکردن',
    delete: 'سڕینەوە',
    search: 'گەڕان',
    name: 'ناو',
    price: 'نرخ',
    quantity: 'بڕ',
    total: 'کۆ',
    
    // Settings page translations
    settings_title: 'ڕێکخستنەکان',
    settings_description: 'بەڕێوەبردنی ڕێکخستنەکانی سیستەم و دەستکاریکردنەکان',
    general: 'گشتی',
    currency: 'دراو',
    printer: 'چاپکەر',
    receipt_design: 'دیزاینی وەسڵ',
    security: 'ئاسایش',
    notifications: 'ئاگاداریەکان',
    
    // General settings
    store_information: 'زانیاری فرۆشگا',
    store_information_desc: 'نوێکردنەوەی زانیاری بنەڕەتی فرۆشگا',
    store_name: 'ناوی فرۆشگا',
    phone_number: 'ژمارەی تەلەفۆن',
    address: 'ناونیشان',
    email: 'ئیمەیڵ',
    tax_number: 'ژمارەی باج',
    system_settings: 'ڕێکخستنی سیستەم',
    system_settings_desc: 'دەستکاری کردنی ڕەفتاری سیستەم و ڕووکار',
    system_language: 'زمانی سیستەم',
    auto_backup: 'پاشەکەوتکردنی خۆکار',
    auto_backup_desc: 'دروستکردنی پاشەکەوتی ڕۆژانە',
    low_stock_alert: 'ئاگاداری کۆگای کەم',
    low_stock_alert_desc: 'ناردنی ئاگاداری کاتی کەمبوونەوەی کاڵا',
    stock_alert_threshold: 'سنووری ئاگاداری کۆگا',
    save_settings: 'پاشەکەوتکردنی ڕێکخستنەکان',
    saved: 'پاشەکەوت کرا',
    general_settings_saved: 'ڕێکخستنە گشتییەکان بە سەرکەوتوویی پاشەکەوت کران',
    applied: 'جێبەجێ کرا',
    changes_applied: 'گۆڕانکارییەکان لە هەموو بەرنامەکەدا جێبەجێ کران',
    
    // Sales page
    point_of_sale: 'خاڵی فرۆشتن',
    product_count: 'ژمارەی بەرهەمەکان',
    load_draft: 'بارکردنی ڕەشنووس',
    search_placeholder: 'گەڕان بە ناو یان بارکۆد...',
    available: 'بەردەست',
    out_of_stock: 'کۆگا نەماوە',
    shopping_cart: 'سەبەتەی کڕین',
    empty_cart: 'سەبەتە بەتاڵە',
    subtotal: 'کۆی بەشی',
    complete_sale: 'تەواوکردنی فرۆشتن',
    save_draft: 'پاشەکەوتکردن وەک ڕەشنووس',
    cancel_operation: 'هەڵوەشاندنەوەی کردار',
    
    // Security settings
    security_settings: 'ڕێکخستنی ئاسایش',
    security_settings_desc: 'ڕێکخستنی ئاسایش و وشەی نهێنی',
    session_timeout: 'کاتی کۆتایی دانیشتن (خولەک)',
    password_min_length: 'کەمترین درێژی وشەی نهێنی',
    login_attempts: 'ژمارەی هەوڵدانی چوونەژوورەوە',
    lockout_duration: 'ماوەی قوفڵکردن (خولەک)',
    two_factor_auth: 'پشتڕاستکردنەوەی دووقۆناغی',
    two_factor_desc: 'چالاککردنی پشتڕاستکردنەوەی دووقۆناغی بۆ پارێزگاری زیاتر',
    require_password_change: 'پێویستکردنی گۆڕینی وشەی نهێنی',
    require_password_desc: 'پێویستکردنی بەکارهێنەران بۆ گۆڕینی وشەی نهێنی بە بەردەوامی',

    // Additional settings translations
    backup: 'پاشەکەوتکردن',
    license: 'مۆڵەت',
    user_management: 'بەڕێوەبردنی بەکارهێنەران',
    account_management: 'بەڕێوەبردنی هەژمارەکان',
    security_logs: 'تۆمارەکانی ئاسایش',
    license_generator: 'دروستکەری مۆڵەت',

    // Dashboard translations
    welcome: 'بەخێربێیت',
    today_sales: 'فرۆشتنی ئەمڕۆ',
    total_revenue: 'کۆی داهات',
    orders_today: 'داواکارییەکانی ئەمڕۆ',
    top_products: 'باشترین بەرهەمەکان',
    recent_transactions: 'دوایین مامەڵەکان',
    quick_actions: 'کردارە خێراکان',
    view_all: 'بینینی هەموو',
    no_data: 'هیچ زانیارییەک نییە',
    loading: 'بارکردن...',

    // Inventory translations
    add_product: 'زیادکردنی بەرهەم',
    edit_product: 'دەستکاریکردنی بەرهەم',
    delete_product: 'سڕینەوەی بەرهەم',
    product_name: 'ناوی بەرهەم',
    product_code: 'کۆدی بەرهەم',
    barcode: 'بارکۆد',
    category: 'پۆل',
    cost_price: 'نرخی تێچوون',
    selling_price: 'نرخی فرۆشتن',
    stock_quantity: 'بڕی کۆگا',
    minimum_stock: 'کەمترین کۆگا',
    description: 'وەسف',
    image: 'وێنە',
    status: 'دۆخ',
    active: 'چالاک',
    inactive: 'ناچالاک',

    // Customer translations
    add_customer: 'زیادکردنی کڕیار',
    edit_customer: 'دەستکاریکردنی کڕیار',
    delete_customer: 'سڕینەوەی کڕیار',
    customer_name: 'ناوی کڕیار',
    customer_phone: 'تەلەفۆنی کڕیار',
    customer_email: 'ئیمەیڵی کڕیار',
    customer_address: 'ناونیشانی کڕیار',
    customer_notes: 'تێبینییەکانی کڕیار',

    // Reports translations
    sales_report: 'راپۆرتی فرۆشتن',
    inventory_report: 'راپۆرتی کۆگا',
    customer_report: 'راپۆرتی کڕیارەکان',
    financial_report: 'راپۆرتی دارایی',
    daily_report: 'راپۆرتی ڕۆژانە',
    weekly_report: 'راپۆرتی هەفتانە',
    monthly_report: 'راپۆرتی مانگانە',
    yearly_report: 'راپۆرتی ساڵانە',
    from_date: 'لە بەرواری',
    to_date: 'بۆ بەرواری',
    generate_report: 'دروستکردنی راپۆرت',
    export_pdf: 'هەناردەکردن وەک PDF',
    export_excel: 'هەناردەکردن وەک Excel',
    print_report: 'چاپکردنی راپۆرت',

    // Common UI translations
    confirm: 'پشتڕاستکردنەوە',
    yes: 'بەڵێ',
    no: 'نەخێر',
    ok: 'باشە',
    close: 'داخستن',
    back: 'گەڕانەوە',
    next: 'دواتر',
    previous: 'پێشتر',
    finish: 'تەواوکردن',
    submit: 'ناردن',
    reset: 'ڕێکخستنەوە',
    clear: 'پاککردنەوە',
    refresh: 'نوێکردنەوە',
    update: 'نوێکردنەوە',
    create: 'دروستکردن',
    remove: 'لابردن',
    select: 'هەڵبژاردن',
    upload: 'بارکردن',
    download: 'داگرتن',

    // Form validation translations
    required_field: 'ئەم خانەیە پێویستە',
    invalid_email: 'ئیمەیڵ نادروستە',
    invalid_phone: 'ژمارەی تەلەفۆن نادروستە',
    password_too_short: 'وشەی نهێنی زۆر کورتە',
    passwords_not_match: 'وشەکانی نهێنی یەکناگرنەوە',
    invalid_number: 'ژمارە نادروستە',
    min_value: 'کەمترین بەها',
    max_value: 'زۆرترین بەها',

    // Success/Error messages
    success: 'سەرکەوتوو',
    error: 'هەڵە',
    warning: 'ئاگاداری',
    info: 'زانیاری',
    operation_successful: 'کردارەکە بە سەرکەوتوویی ئەنجام درا',
    operation_failed: 'کردارەکە سەرکەوتوو نەبوو',
    data_saved: 'زانیارییەکان پاشەکەوت کران',
    data_deleted: 'زانیارییەکان سڕانەوە',
    confirm_delete: 'دڵنیایت لە سڕینەوە؟',
    cannot_undo: 'ناتوانیت ئەمە بگەڕێنیتەوە',

    // Date/Time translations
    today: 'ئەمڕۆ',
    yesterday: 'دوێنێ',
    tomorrow: 'بەیانی',
    this_week: 'ئەم هەفتەیە',
    this_month: 'ئەم مانگە',
    this_year: 'ئەم ساڵە',
    date: 'بەروار',
    time: 'کات',

    // Currency and numbers
    currency: 'دراو',
    amount: 'بڕ',
    discount: 'داشکاندن',
    tax: 'باج',
    profit: 'قازانج',
    loss: 'زیان',

    // Additional UI translations
    pos_system: 'سیستەمی پێشکەوتووی فرۆشتن',
    admin: 'بەڕێوەبەری سیستەم',
    cashier: 'کاشێر',
    supervisor: 'سەرپەرشتیار',
    user: 'بەکارهێنەر',
    logout: 'چوونەدەرەوە',
    notifications: 'ئاگاداریەکان',
    low_stock: 'کۆگای کەم',
    products: 'بەرهەمەکان',

    // Reports specific translations
    period: 'ماوە',
    today: 'ئەمڕۆ',
    this_week: 'ئەم هەفتەیە',
    this_month: 'ئەم مانگە',
    this_year: 'ئەم ساڵە',
    all_time: 'هەموو کاتەکان',
    total_revenue: 'کۆی داهات',
    total_transactions: 'کۆی مامەڵەکان',
    total_cost: 'کۆی تێچوون',
    total_profit: 'کۆی قازانج',
    profit_margin: 'ڕێژەی قازانج',
    average_transaction: 'تێکڕای مامەڵە',
    pending_debts: 'قەرزە چاوەڕوانەکان',
    active_installments: 'قیستە چالاکەکان',
    top_selling_products: 'باشترین بەرهەمە فرۆشراوەکان',
    top_customers: 'باشترین کڕیارەکان',
    category_breakdown: 'دابەشکردن بەپێی پۆل',
    inventory_analysis: 'شیکردنەوەی کۆگا',
    recent_transactions: 'دوایین مامەڵەکان',
    search_invoice: 'گەڕان بە ژمارەی فاتورە',
    invoice_number: 'ژمارەی فاتورە',
    search: 'گەڕان',
    view_details: 'بینینی وردەکارییەکان',
    export_report: 'هەناردەکردنی راپۆرت',
    generate_report: 'دروستکردنی راپۆرت',
    sales_chart: 'هێڵکاری فرۆشتن',
    profit_chart: 'هێڵکاری قازانج',
    category_chart: 'هێڵکاری پۆلەکان',
    stock_status: 'دۆخی کۆگا',
    out_of_stock: 'کۆگا نەماوە',
    low_stock: 'کۆگای کەم',
    good_stock: 'کۆگای باش',
    over_stock: 'کۆگای زۆر',
    customer_name: 'ناوی کڕیار',
    phone: 'تەلەفۆن',
    total_purchases: 'کۆی کڕینەکان',
    last_visit: 'دوایین سەردان',
    transaction_count: 'ژمارەی مامەڵەکان',
    pending_amount: 'بڕی چاوەڕوان',
    payment_method: 'شێوازی پارەدان',
    cash: 'نەقد',
    card: 'کارت',
    debt: 'قەرز',
    installment: 'قیست',
    completed: 'تەواو',
    pending: 'چاوەڕوان',
    cancelled: 'هەڵوەشاندراوە',
    high_priority: 'گرنگی بەرز',
    medium_priority: 'گرنگی ناوەند',
    low_priority: 'گرنگی کەم',
    days_pending: 'ڕۆژی چاوەڕوان',
    payment_progress: 'پێشکەوتنی پارەدان',
    months_remaining: 'مانگی ماوە',
    next_payment: 'پارەدانی داهاتوو',
    monthly_payment: 'پارەدانی مانگانە',
    down_payment: 'پارەی پێشەکی',
    remaining_amount: 'بڕی ماوە',
    total_paid: 'کۆی پارەدراو',
    notes: 'تێبینییەکان',
    created_at: 'دروستکراوە لە',
    updated_at: 'نوێکراوەتەوە لە',

    // Additional missing translations
    no_data_available: 'هیچ زانیارییەک بەردەست نییە',
    undefined: 'دیارینەکراو',
    unknown: 'نەزانراو',
    not_specified: 'دیارینەکراو',
    good: 'باش',
    excellent: 'نایاب',
    poor: 'خراپ',
    priority: 'گرنگی',
    status: 'دۆخ',
    type: 'جۆر',
    category: 'پۆل',
    item: 'بڕگە',
    items: 'بڕگەکان',
    unit_price: 'نرخی یەکە',
    total_price: 'کۆی نرخ',
    quantity: 'بڕ',
    subtotal: 'کۆی بەشی',
    discount: 'داشکاندن',
    tax: 'باج',
    grand_total: 'کۆی گشتی',
    payment_status: 'دۆخی پارەدان',
    transaction_id: 'ناسنامەی مامەڵە',
    reference: 'ژمارەی ئاماژە'
  },
  en: {
    // Navigation
    dashboard: 'Dashboard',
    sales: 'Sales',
    inventory: 'Inventory',
    customers: 'Customers',
    reports: 'Reports',
    settings: 'Settings',
    
    // Common terms
    total_sales: 'Total Sales',
    products: 'Products',
    low_stock: 'Low Stock',
    recent_orders: 'Recent Orders',
    save: 'Save',
    cancel: 'Cancel',
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    search: 'Search',
    name: 'Name',
    price: 'Price',
    quantity: 'Quantity',
    total: 'Total',
    
    // Settings page translations
    settings_title: 'Settings',
    settings_description: 'Manage system settings and customizations',
    general: 'General',
    currency: 'Currency',
    printer: 'Printer',
    receipt_design: 'Receipt Design',
    security: 'Security',
    notifications: 'Notifications',
    
    // General settings
    store_information: 'Store Information',
    store_information_desc: 'Update basic store information',
    store_name: 'Store Name',
    phone_number: 'Phone Number',
    address: 'Address',
    email: 'Email',
    tax_number: 'Tax Number',
    system_settings: 'System Settings',
    system_settings_desc: 'Customize system behavior and interface',
    system_language: 'System Language',
    auto_backup: 'Auto Backup',
    auto_backup_desc: 'Create daily data backup',
    low_stock_alert: 'Low Stock Alert',
    low_stock_alert_desc: 'Send alert when product quantity is low',
    stock_alert_threshold: 'Stock Alert Threshold',
    save_settings: 'Save Settings',
    saved: 'Saved',
    general_settings_saved: 'General settings saved successfully',
    applied: 'Applied',
    changes_applied: 'Changes applied throughout the application',
    
    // Sales page
    point_of_sale: 'Point of Sale',
    product_count: 'Product Count',
    load_draft: 'Load Draft',
    search_placeholder: 'Search by name or barcode...',
    available: 'Available',
    out_of_stock: 'Out of Stock',
    shopping_cart: 'Shopping Cart',
    empty_cart: 'Cart is Empty',
    subtotal: 'Subtotal',
    complete_sale: 'Complete Sale',
    save_draft: 'Save as Draft',
    cancel_operation: 'Cancel Operation',
    
    // Security settings
    security_settings: 'Security Settings',
    security_settings_desc: 'Configure security settings and passwords',
    session_timeout: 'Session Timeout (minutes)',
    password_min_length: 'Minimum Password Length',
    login_attempts: 'Login Attempts',
    lockout_duration: 'Lockout Duration (minutes)',
    two_factor_auth: 'Two-Factor Authentication',
    two_factor_desc: 'Enable two-factor authentication for additional security',
    require_password_change: 'Require Password Change',
    require_password_desc: 'Force users to change passwords periodically',

    // Additional settings translations
    backup: 'Backup',
    license: 'License',
    user_management: 'User Management',
    account_management: 'Account Management',
    security_logs: 'Security Logs',
    license_generator: 'License Generator',

    // Dashboard translations
    welcome: 'Welcome',
    today_sales: 'Today\'s Sales',
    total_revenue: 'Total Revenue',
    orders_today: 'Orders Today',
    top_products: 'Top Products',
    recent_transactions: 'Recent Transactions',
    quick_actions: 'Quick Actions',
    view_all: 'View All',
    no_data: 'No Data Available',
    loading: 'Loading...',

    // Inventory translations
    add_product: 'Add Product',
    edit_product: 'Edit Product',
    delete_product: 'Delete Product',
    product_name: 'Product Name',
    product_code: 'Product Code',
    barcode: 'Barcode',
    category: 'Category',
    cost_price: 'Cost Price',
    selling_price: 'Selling Price',
    stock_quantity: 'Stock Quantity',
    minimum_stock: 'Minimum Stock',
    description: 'Description',
    image: 'Image',
    status: 'Status',
    active: 'Active',
    inactive: 'Inactive',

    // Customer translations
    add_customer: 'Add Customer',
    edit_customer: 'Edit Customer',
    delete_customer: 'Delete Customer',
    customer_name: 'Customer Name',
    customer_phone: 'Customer Phone',
    customer_email: 'Customer Email',
    customer_address: 'Customer Address',
    customer_notes: 'Customer Notes',

    // Reports translations
    sales_report: 'Sales Report',
    inventory_report: 'Inventory Report',
    customer_report: 'Customer Report',
    financial_report: 'Financial Report',
    daily_report: 'Daily Report',
    weekly_report: 'Weekly Report',
    monthly_report: 'Monthly Report',
    yearly_report: 'Yearly Report',
    from_date: 'From Date',
    to_date: 'To Date',
    generate_report: 'Generate Report',
    export_pdf: 'Export PDF',
    export_excel: 'Export Excel',
    print_report: 'Print Report',

    // Common UI translations
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    finish: 'Finish',
    submit: 'Submit',
    reset: 'Reset',
    clear: 'Clear',
    refresh: 'Refresh',
    update: 'Update',
    create: 'Create',
    remove: 'Remove',
    select: 'Select',
    upload: 'Upload',
    download: 'Download',

    // Form validation translations
    required_field: 'This field is required',
    invalid_email: 'Invalid email address',
    invalid_phone: 'Invalid phone number',
    password_too_short: 'Password is too short',
    passwords_not_match: 'Passwords do not match',
    invalid_number: 'Invalid number',
    min_value: 'Minimum value',
    max_value: 'Maximum value',

    // Success/Error messages
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information',
    operation_successful: 'Operation completed successfully',
    operation_failed: 'Operation failed',
    data_saved: 'Data saved successfully',
    data_deleted: 'Data deleted successfully',
    confirm_delete: 'Are you sure you want to delete?',
    cannot_undo: 'This action cannot be undone',

    // Date/Time translations
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    this_week: 'This Week',
    this_month: 'This Month',
    this_year: 'This Year',
    date: 'Date',
    time: 'Time',

    // Currency and numbers
    currency: 'Currency',
    amount: 'Amount',
    discount: 'Discount',
    tax: 'Tax',
    profit: 'Profit',
    loss: 'Loss',

    // Additional UI translations
    pos_system: 'Advanced POS System',
    admin: 'Admin',
    cashier: 'Cashier',
    supervisor: 'Supervisor',
    user: 'User',
    logout: 'Logout',
    notifications: 'Notifications',
    low_stock: 'Low Stock',
    products: 'Products',

    // Reports specific translations
    period: 'Period',
    today: 'Today',
    this_week: 'This Week',
    this_month: 'This Month',
    this_year: 'This Year',
    all_time: 'All Time',
    total_revenue: 'Total Revenue',
    total_transactions: 'Total Transactions',
    total_cost: 'Total Cost',
    total_profit: 'Total Profit',
    profit_margin: 'Profit Margin',
    average_transaction: 'Average Transaction',
    pending_debts: 'Pending Debts',
    active_installments: 'Active Installments',
    top_selling_products: 'Top Selling Products',
    top_customers: 'Top Customers',
    category_breakdown: 'Category Breakdown',
    inventory_analysis: 'Inventory Analysis',
    recent_transactions: 'Recent Transactions',
    search_invoice: 'Search by Invoice Number',
    invoice_number: 'Invoice Number',
    search: 'Search',
    view_details: 'View Details',
    export_report: 'Export Report',
    generate_report: 'Generate Report',
    sales_chart: 'Sales Chart',
    profit_chart: 'Profit Chart',
    category_chart: 'Category Chart',
    stock_status: 'Stock Status',
    out_of_stock: 'Out of Stock',
    low_stock: 'Low Stock',
    good_stock: 'Good Stock',
    over_stock: 'Over Stock',
    customer_name: 'Customer Name',
    phone: 'Phone',
    total_purchases: 'Total Purchases',
    last_visit: 'Last Visit',
    transaction_count: 'Transaction Count',
    pending_amount: 'Pending Amount',
    payment_method: 'Payment Method',
    cash: 'Cash',
    card: 'Card',
    debt: 'Debt',
    installment: 'Installment',
    completed: 'Completed',
    pending: 'Pending',
    cancelled: 'Cancelled',
    high_priority: 'High Priority',
    medium_priority: 'Medium Priority',
    low_priority: 'Low Priority',
    days_pending: 'Days Pending',
    payment_progress: 'Payment Progress',
    months_remaining: 'Months Remaining',
    next_payment: 'Next Payment',
    monthly_payment: 'Monthly Payment',
    down_payment: 'Down Payment',
    remaining_amount: 'Remaining Amount',
    total_paid: 'Total Paid',
    notes: 'Notes',
    created_at: 'Created At',
    updated_at: 'Updated At',

    // Additional missing translations
    no_data_available: 'No Data Available',
    undefined: 'Undefined',
    unknown: 'Unknown',
    not_specified: 'Not Specified',
    good: 'Good',
    excellent: 'Excellent',
    poor: 'Poor',
    priority: 'Priority',
    status: 'Status',
    type: 'Type',
    category: 'Category',
    item: 'Item',
    items: 'Items',
    unit_price: 'Unit Price',
    total_price: 'Total Price',
    quantity: 'Quantity',
    subtotal: 'Subtotal',
    discount: 'Discount',
    tax: 'Tax',
    grand_total: 'Grand Total',
    payment_status: 'Payment Status',
    transaction_id: 'Transaction ID',
    reference: 'Reference'
  },
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider = ({ children }: { children: ReactNode }) => {
  const [settings, setSettings] = useState<SettingsState>(() => {
    const saved = localStorage.getItem('aridoo-settings');
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
  });

  // Apply settings immediately when they change
  useEffect(() => {
    localStorage.setItem('aridoo-settings', JSON.stringify(settings));
    
    // Apply language direction and font to document
    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    if (settings.language === 'en') {
      htmlElement.dir = 'ltr';
      htmlElement.lang = 'en';
      htmlElement.className = 'ltr';
      bodyElement.className = bodyElement.className.replace(/kurdish-text|arabic-text/g, '').trim();
    } else if (settings.language === 'ku') {
      htmlElement.dir = 'rtl';
      htmlElement.lang = 'ku';
      htmlElement.className = 'rtl';
      bodyElement.className = bodyElement.className.replace(/arabic-text/g, '').trim();
      if (!bodyElement.className.includes('kurdish-text')) {
        bodyElement.className += ' kurdish-text';
      }
    } else {
      htmlElement.dir = 'rtl';
      htmlElement.lang = settings.language;
      htmlElement.className = 'rtl';
      bodyElement.className = bodyElement.className.replace(/kurdish-text/g, '').trim();
      if (!bodyElement.className.includes('arabic-text')) {
        bodyElement.className += ' arabic-text';
      }
    }
    
    // Force re-render of all components by triggering a storage event
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'aridoo-settings',
      newValue: JSON.stringify(settings)
    }));
    
    // Additional custom event for immediate updates
    window.dispatchEvent(new CustomEvent('settings-changed', { 
      detail: settings 
    }));
  }, [settings]);

  const updateSettings = (newSettings: Partial<SettingsState>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings };
      
      // Immediate save to localStorage
      localStorage.setItem('aridoo-settings', JSON.stringify(updated));
      
      // Apply language changes immediately
      if (newSettings.language) {
        const htmlElement = document.documentElement;
        const bodyElement = document.body;

        if (newSettings.language === 'en') {
          htmlElement.dir = 'ltr';
          htmlElement.lang = 'en';
          htmlElement.className = 'ltr';
          bodyElement.className = bodyElement.className.replace(/kurdish-text|arabic-text/g, '').trim();
        } else if (newSettings.language === 'ku') {
          htmlElement.dir = 'rtl';
          htmlElement.lang = 'ku';
          htmlElement.className = 'rtl';
          bodyElement.className = bodyElement.className.replace(/arabic-text/g, '').trim();
          if (!bodyElement.className.includes('kurdish-text')) {
            bodyElement.className += ' kurdish-text';
          }
        } else {
          htmlElement.dir = 'rtl';
          htmlElement.lang = newSettings.language;
          htmlElement.className = 'rtl';
          bodyElement.className = bodyElement.className.replace(/kurdish-text/g, '').trim();
          if (!bodyElement.className.includes('arabic-text')) {
            bodyElement.className += ' arabic-text';
          }
        }
      }
      
      return updated;
    });
  };

  const formatCurrency = (amount: number): string => {
    let finalAmount = amount;
    
    // Apply rounding method
    switch (settings.roundingMethod) {
      case 'floor':
        finalAmount = Math.floor(amount);
        break;
      case 'ceil':
        finalAmount = Math.ceil(amount);
        break;
      default:
        finalAmount = Math.round(amount * Math.pow(10, settings.decimalPlaces)) / Math.pow(10, settings.decimalPlaces);
    }

    const formatted = finalAmount.toFixed(settings.decimalPlaces);
    const parts = formatted.split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, settings.thousandSeparator);
    const finalAmountStr = parts.join(settings.decimalSeparator);
    
    return settings.currencyPosition === 'before' 
      ? `${settings.currencySymbol} ${finalAmountStr}`
      : `${finalAmountStr} ${settings.currencySymbol}`;
  };

  const t = (key: string): string => {
    return translations[settings.language]?.[key] || key;
  };

  return (
    <SettingsContext.Provider value={{ settings, updateSettings, formatCurrency, t }}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};
