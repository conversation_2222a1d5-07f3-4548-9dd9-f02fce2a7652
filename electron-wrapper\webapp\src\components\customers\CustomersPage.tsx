import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  Phone, 
  Mail, 
  Star, 
  Eye, 
  Users,
  Bell,
  CreditCard
} from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import { getCustomers, deleteCustomer, type Customer } from '@/utils/database';
import AddCustomerDialog from './AddCustomerDialog';
import EditCustomerDialog from './EditCustomerDialog';
import CustomerDetailsDialog from './CustomerDetailsDialog';
import PaymentDialog from './PaymentDialog';
import PaymentReminders from './PaymentReminders';

const CustomersPage = () => {
  const { toast } = useToast();
  const { formatCurrency } = useSettings();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [selectedCustomerForPayment, setSelectedCustomerForPayment] = useState<Customer | null>(null);
  const [activeTab, setActiveTab] = useState('customers');

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      setLoading(true);
      const data = await getCustomers();
      setCustomers(data);
    } catch (error) {
      console.error('Error loading customers:', error);
      toast({
        title: "خطأ في تحميل البيانات",
        description: "حدث خطأ أثناء تحميل بيانات العملاء",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddCustomer = () => {
    loadCustomers();
    toast({
      title: "تم إضافة العميل",
      description: "تم إضافة العميل بنجاح",
    });
  };

  const handleUpdateCustomer = () => {
    loadCustomers();
    toast({
      title: "تم تحديث العميل",
      description: "تم تحديث بيانات العميل بنجاح",
    });
  };

  const handleDeleteCustomer = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        await deleteCustomer(id);
        setCustomers(customers.filter(c => c.id !== id));
        toast({
          title: "تم حذف العميل",
          description: "تم حذف العميل بنجاح",
        });
      } catch (error) {
        console.error('Error deleting customer:', error);
        toast({
          title: "خطأ في حذف العميل",
          description: "حدث خطأ أثناء حذف العميل",
          variant: "destructive"
        });
      }
    }
  };

  const openEditDialog = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowEditDialog(true);
  };

  const openCustomerDetails = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowDetailsDialog(true);
  };

  const handlePaymentComplete = () => {
    setShowPaymentDialog(false);
    setSelectedCustomerForPayment(null);
    loadCustomers();
  };

  const openPaymentDialog = (customer: Customer) => {
    setSelectedCustomerForPayment(customer);
    setShowPaymentDialog(true);
  };

  const filteredCustomers = customers.filter(customer => {
    const searchLower = searchTerm.toLowerCase();
    return (
      customer.name.toLowerCase().includes(searchLower) ||
      (customer.phone && customer.phone.includes(searchTerm)) ||
      (customer.email && customer.email.toLowerCase().includes(searchLower))
    );
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent mx-auto"></div>
          <div className="text-xl font-semibold text-gray-700">جاري تحميل بيانات العملاء...</div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 animate-fade-in">
                <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-primary to-primary/60 flex items-center justify-center shadow-lg hover-glow">
                  <Users className="w-8 h-8 text-primary-foreground" />
                </div>
                <div>
                  <h1 className="text-3xl font-extrabold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    إدارة العملاء
                  </h1>
                  <p className="text-base text-muted-foreground font-semibold">إدارة بيانات العملاء والديون والأقساط</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <Badge variant="secondary" className="text-lg px-4 py-2 bg-blue-100 text-blue-800 border-blue-200">
                  <Users className="w-5 h-5 ml-2" />
                  {customers.length} عميل
                </Badge>
                <Button onClick={() => setShowAddDialog(true)} className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                  <Plus className="w-5 h-5 ml-2" />
                  إضافة عميل جديد
                </Button>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="customers" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                العملاء
              </TabsTrigger>
              <TabsTrigger value="reminders" className="flex items-center gap-2">
                <Bell className="w-4 h-4" />
                تذكيرات الدفع
              </TabsTrigger>
            </TabsList>

            <TabsContent value="customers">
              {/* Search Section */}
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm mb-6">
                <CardContent className="p-6">
                  <div className="relative">
                    <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
                    <Input
                      placeholder="البحث بالاسم أو رقم الهاتف أو البريد الإلكتروني..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pr-14 h-14 text-lg border-2 border-gray-200 focus:border-blue-400 rounded-xl bg-white/50"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Customers Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredCustomers.map((customer) => (
                  <Card key={customer.id} className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-0 bg-white/90 backdrop-blur-sm overflow-hidden">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-bold text-lg">
                            {customer.name.charAt(0)}
                          </div>
                          <div>
                            <h3 className="font-bold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                              {customer.name}
                            </h3>
                            <p className="text-sm text-gray-500">
                              عضو منذ {new Date(customer.created_at).toLocaleDateString('ar-IQ')}
                            </p>
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-blue-50">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem onClick={() => openCustomerDetails(customer)}>
                              <Eye className="w-4 h-4 ml-2" />
                              عرض التفاصيل
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openEditDialog(customer)}>
                              <Edit className="w-4 h-4 ml-2" />
                              تعديل العميل
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openPaymentDialog(customer)}>
                              <CreditCard className="w-4 h-4 ml-2" />
                              دفع ديون/أقساط
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteCustomer(customer.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="w-4 h-4 ml-2" />
                              حذف العميل
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="space-y-3">
                        {customer.phone && (
                          <div className="flex items-center gap-2 text-gray-600">
                            <Phone className="w-4 h-4" />
                            <span className="text-sm">{customer.phone}</span>
                          </div>
                        )}
                        
                        {customer.email && (
                          <div className="flex items-center gap-2 text-gray-600">
                            <Mail className="w-4 h-4" />
                            <span className="text-sm">{customer.email}</span>
                          </div>
                        )}

                        <div className="flex items-center gap-2 text-gray-600">
                          <Star className="w-4 h-4" />
                          <span className="text-sm">{customer.points} نقطة</span>
                        </div>

                        <div className="grid grid-cols-2 gap-4 pt-3 border-t border-gray-100">
                          <div className="text-center">
                            <p className="text-xs text-gray-500">إجمالي المشتريات</p>
                            <p className="font-bold text-green-600">
                              {formatCurrency(customer.total_purchases)}
                            </p>
                          </div>
                          <div className="text-center">
                            <p className="text-xs text-gray-500">إجمالي الديون</p>
                            <p className={`font-bold ${
                              (customer.total_debt || 0) > 0 ? 'text-red-600' : 'text-gray-400'
                            }`}>
                              {formatCurrency(customer.total_debt || 0)}
                            </p>
                          </div>
                        </div>

                        {(customer.total_debt || 0) > 0 && (
                          <Button 
                            onClick={() => openPaymentDialog(customer)}
                            className="w-full mt-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                            size="sm"
                          >
                            <CreditCard className="w-4 h-4 ml-2" />
                            دفع الديون والأقساط
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredCustomers.length === 0 && searchTerm && (
                <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                  <CardContent className="text-center py-12">
                    <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد نتائج</h3>
                    <p className="text-gray-500">لم يتم العثور على عملاء يطابقون البحث "{searchTerm}"</p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="reminders">
              <PaymentReminders />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <AddCustomerDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        onAddCustomer={handleAddCustomer}
      />

      <EditCustomerDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        customer={selectedCustomer}
        onUpdateCustomer={handleUpdateCustomer}
      />

      <CustomerDetailsDialog
        open={showDetailsDialog}
        onOpenChange={setShowDetailsDialog}
        customer={selectedCustomer}
      />

      <PaymentDialog
        open={showPaymentDialog}
        onOpenChange={setShowPaymentDialog}
        customer={selectedCustomerForPayment}
        onPaymentComplete={handlePaymentComplete}
      />
    </>
  );
};

export default CustomersPage;
