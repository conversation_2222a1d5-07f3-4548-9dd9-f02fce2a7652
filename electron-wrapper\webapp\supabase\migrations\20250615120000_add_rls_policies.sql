
-- Enable RLS on all tables
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sale_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.debt_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.installments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.installment_payments ENABLE ROW LEVEL SECURITY;

-- Categories policies - allow all operations for authenticated users
CREATE POLICY "Categories: Allow all operations for authenticated users" ON public.categories
  FOR ALL USING (auth.role() = 'authenticated');

-- Products policies - allow all operations for authenticated users
CREATE POLICY "Products: Allow all operations for authenticated users" ON public.products
  FOR ALL USING (auth.role() = 'authenticated');

-- Customers policies - allow all operations for authenticated users
CREATE POLICY "Customers: Allow all operations for authenticated users" ON public.customers
  FOR ALL USING (auth.role() = 'authenticated');

-- Sales policies - allow all operations for authenticated users
CREATE POLICY "Sales: Allow all operations for authenticated users" ON public.sales
  FOR ALL USING (auth.role() = 'authenticated');

-- Sale items policies - allow all operations for authenticated users
CREATE POLICY "Sale items: Allow all operations for authenticated users" ON public.sale_items
  FOR ALL USING (auth.role() = 'authenticated');

-- Debts policies - allow all operations for authenticated users
CREATE POLICY "Debts: Allow all operations for authenticated users" ON public.debts
  FOR ALL USING (auth.role() = 'authenticated');

-- Debt payments policies - allow all operations for authenticated users
CREATE POLICY "Debt payments: Allow all operations for authenticated users" ON public.debt_payments
  FOR ALL USING (auth.role() = 'authenticated');

-- Installments policies - allow all operations for authenticated users
CREATE POLICY "Installments: Allow all operations for authenticated users" ON public.installments
  FOR ALL USING (auth.role() = 'authenticated');

-- Installment payments policies - allow all operations for authenticated users
CREATE POLICY "Installment payments: Allow all operations for authenticated users" ON public.installment_payments
  FOR ALL USING (auth.role() = 'authenticated');
