// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://kcqbeoogqvpbuhstcspw.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtjcWJlb29ncXZwYnVoc3Rjc3B3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MzQxMTUsImV4cCI6MjA2NTUxMDExNX0.xGuEfrrc2z2m5qbZANMVdwMP5CBy6PS-ncQZ6zumqR8";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);