
import React from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface WhatsAppSenderProps {
  phoneNumber: string;
  customerName: string;
  amount: number;
  paymentType: 'debt' | 'installment' | 'reminder';
  installmentDetails?: {
    downPayment: number;
    monthlyPayment: number;
    months: number;
  };
  reminderDetails?: {
    dueDate: string;
    overdueCount?: number;
  };
}

const WhatsAppSender: React.FC<WhatsAppSenderProps> = ({
  phoneNumber,
  customerName,
  amount,
  paymentType,
  installmentDetails,
  reminderDetails
}) => {
  const { toast } = useToast();

  const sendWhatsAppMessage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    console.log('WhatsApp button clicked - preventing all default behaviors');
    
    try {
      let cleanPhone = phoneNumber.replace(/[^\d+]/g, '');
      
      if (!cleanPhone.startsWith('+') && !cleanPhone.startsWith('964')) {
        if (cleanPhone.startsWith('0')) {
          cleanPhone = '964' + cleanPhone.substring(1);
        } else {
          cleanPhone = '964' + cleanPhone;
        }
      }
      
      cleanPhone = cleanPhone.replace('+', '');
      
      let message = '';
      
      if (paymentType === 'debt') {
        message = `مرحباً ${customerName}،

تم تسجيل عملية شراء بالدين بمبلغ ${amount.toLocaleString()} د.ع

يرجى المرور لتسديد المبلغ في أقرب وقت ممكن.

شكراً لثقتكم بنا
متجر الإلكترونيات`;
      } else if (paymentType === 'installment' && installmentDetails) {
        message = `مرحباً ${customerName}،

تم تسجيل عملية شراء بالأقساط:

• المبلغ الإجمالي: ${amount.toLocaleString()} د.ع
• المقدم: ${installmentDetails.downPayment.toLocaleString()} د.ع
• القسط الشهري: ${installmentDetails.monthlyPayment.toLocaleString()} د.ع
• عدد الأقساط: ${installmentDetails.months} شهر

يرجى تسديد الأقساط في مواعيدها المحددة.

شكراً لثقتكم بنا
متجر الإلكترونيات`;
      } else if (paymentType === 'reminder' && reminderDetails) {
        const isOverdue = reminderDetails.overdueCount && reminderDetails.overdueCount > 0;
        const dueDate = new Date(reminderDetails.dueDate).toLocaleDateString('ar-IQ');
        
        message = `${isOverdue ? '🔴 تذكير مهم' : '⏰ تذكير ودي'} - ${customerName}

${isOverdue 
  ? `لديك مستحقات متأخرة ${reminderDetails.overdueCount} يوم`
  : 'تذكير بموعد استحقاق الدفع'
}

💰 المبلغ المستحق: ${amount.toLocaleString()} د.ع
📅 تاريخ الاستحقاق: ${dueDate}

${isOverdue 
  ? 'يرجى المرور لتسديد المبلغ المتأخر في أقرب وقت ممكن لتجنب أي تأخير إضافي.'
  : 'نذكركم بموعد تسديد المستحقات المالية.'
}

للاستفسار أو تحديد موعد الدفع، يرجى التواصل معنا.

شكراً لتفهمكم وتعاونكم 🙏
متجر الإلكترونيات`;
      }

      const whatsappUrl = `https://wa.me/${cleanPhone}?text=${encodeURIComponent(message)}`;
      
      console.log('Opening WhatsApp with URL:', whatsappUrl);
      
      setTimeout(() => {
        const tempLink = document.createElement('a');
        tempLink.href = whatsappUrl;
        tempLink.target = '_blank';
        tempLink.rel = 'noopener noreferrer';
        
        document.body.appendChild(tempLink);
        tempLink.click();
        document.body.removeChild(tempLink);
        
        console.log('WhatsApp link clicked successfully');
        
        const messageTypeArabic = paymentType === 'debt' ? 'دين' : 
                                 paymentType === 'installment' ? 'قسط' : 'تذكير';
        
        toast({
          title: "تم فتح الواتساب",
          description: `تم فتح الواتساب لإرسال رسالة ${messageTypeArabic} للعميل`,
        });
      }, 100);
      
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      toast({
        title: "خطأ في إرسال الرسالة",
        description: "حدث خطأ أثناء فتح الواتساب",
        variant: "destructive"
      });
    }
  };

  if (!phoneNumber) {
    return null;
  }

  const getButtonText = () => {
    switch (paymentType) {
      case 'debt': return 'إرسال رسالة دين';
      case 'installment': return 'إرسال رسالة قسط';
      case 'reminder': return 'إرسال تذكير دفع';
      default: return 'إرسال رسالة واتساب';
    }
  };

  const getButtonColor = () => {
    switch (paymentType) {
      case 'reminder': return 'bg-orange-500 hover:bg-orange-600';
      default: return 'bg-green-500 hover:bg-green-600';
    }
  };

  return (
    <Button
      onClick={sendWhatsAppMessage}
      className={`w-full ${getButtonColor()} text-white`}
      type="button"
    >
      <svg className="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24">
        <path d="M17.05 12.68c-.28-.15-1.66-.82-1.92-.91-.25-.1-.44-.15-.62.15-.19.29-.73.91-.9 1.1-.16.18-.33.21-.6.07-.28-.15-1.17-.43-2.23-1.37-.82-.74-1.38-1.65-1.54-1.93-.16-.29-.02-.44.13-.58.13-.13.28-.33.42-.5.15-.16.2-.28.29-.47.1-.18.05-.34-.02-.47-.08-.13-.62-1.5-.85-2.05-.22-.53-.45-.46-.62-.47-.16 0-.34-.02-.52-.02-.19 0-.49.07-.75.34-.25.28-.97.95-.97 2.31s.99 2.68 1.13 2.86c.15.19 2.11 3.22 5.11 4.52.71.31 1.27.49 1.71.63.72.23 1.37.2 1.88.12.57-.09 1.76-.72 2.01-1.42.25-.7.25-1.3.17-1.42-.07-.13-.26-.2-.54-.35z"/>
        <path d="M12 2C6.48 2 2 6.48 2 12c0 1.78.46 3.45 1.27 4.9L2 22l5.1-1.27C8.55 21.54 10.22 22 12 22c5.52 0 10-4.48 10-10S17.52 2 12 2zm0 18c-1.54 0-3.04-.4-4.35-1.15L7 19l-.85.21C5.46 18.04 4 15.17 4 12c0-4.42 3.58-8 8-8s8 3.58 8 8-3.58 8-8 8z"/>
      </svg>
      {getButtonText()}
    </Button>
  );
};

export default WhatsAppSender;
