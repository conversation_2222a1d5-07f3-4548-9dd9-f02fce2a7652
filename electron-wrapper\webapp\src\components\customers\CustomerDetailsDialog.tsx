import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Phone, Mail, MapPin, Calendar, CreditCard, ShoppingBag, FileText, Clock, Package } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  getCustomerPurchaseHistory, 
  getCustomerPaymentHistory, 
  getCustomerInstallmentHistory, 
  getCustomerStats, 
  getCustomerFinancialSummary,
  type CustomerPurchaseHistory,
  type CustomerPaymentHistory,
  type CustomerInstallmentHistory,
  type CustomerStats
} from '@/utils/customerData';

interface CustomerDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer: any;
}

const CustomerDetailsDialog = ({ open, onOpenChange, customer }: CustomerDetailsDialogProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [purchaseHistory, setPurchaseHistory] = useState<CustomerPurchaseHistory[]>([]);
  const [paymentHistory, setPaymentHistory] = useState<CustomerPaymentHistory[]>([]);
  const [installmentHistory, setInstallmentHistory] = useState<CustomerInstallmentHistory[]>([]);
  const [customerStats, setCustomerStats] = useState<CustomerStats | null>(null);
  const [financialSummary, setFinancialSummary] = useState({
    totalPurchases: 0,
    totalDebt: 0,
    totalInstallments: 0,
    lastPurchase: 'لا يوجد'
  });

  useEffect(() => {
    if (customer && open) {
      console.log('Dialog opened for customer:', customer);
      loadCustomerData();
    }
  }, [customer, open]);

  const loadCustomerData = async () => {
    if (!customer?.id) {
      console.log('No customer ID provided');
      return;
    }
    
    try {
      setLoading(true);
      console.log('Starting to load all customer data for ID:', customer.id);
      
      // Load all data with proper error handling
      const [purchases, payments, installments, stats, financial] = await Promise.allSettled([
        getCustomerPurchaseHistory(customer.id),
        getCustomerPaymentHistory(customer.id),
        getCustomerInstallmentHistory(customer.id),
        getCustomerStats(customer.id),
        getCustomerFinancialSummary(customer.id)
      ]);

      // Handle purchases
      if (purchases.status === 'fulfilled') {
        console.log('Purchase history loaded:', purchases.value.length, 'items');
        setPurchaseHistory(purchases.value);
      } else {
        console.error('Failed to load purchase history:', purchases.reason);
        setPurchaseHistory([]);
      }

      // Handle payments
      if (payments.status === 'fulfilled') {
        console.log('Payment history loaded:', payments.value.length, 'items');
        setPaymentHistory(payments.value);
      } else {
        console.error('Failed to load payment history:', payments.reason);
        setPaymentHistory([]);
      }

      // Handle installments
      if (installments.status === 'fulfilled') {
        console.log('Installment history loaded:', installments.value.length, 'items');
        setInstallmentHistory(installments.value);
      } else {
        console.error('Failed to load installment history:', installments.reason);
        setInstallmentHistory([]);
      }

      // Handle stats
      if (stats.status === 'fulfilled') {
        console.log('Customer stats loaded successfully');
        setCustomerStats(stats.value);
      } else {
        console.error('Failed to load customer stats:', stats.reason);
        setCustomerStats({ totalInvoices: 0, paidInvoices: 0, pendingInvoices: 0, averageInvoice: 0 });
      }

      // Handle financial summary
      if (financial.status === 'fulfilled') {
        console.log('Financial summary loaded successfully');
        setFinancialSummary(financial.value);
      } else {
        console.error('Failed to load financial summary:', financial.reason);
        setFinancialSummary({ totalPurchases: 0, totalDebt: 0, totalInstallments: 0, lastPurchase: 'لا يوجد' });
      }

      console.log('All customer data loading completed');
    } catch (error) {
      console.error('Unexpected error loading customer data:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل بيانات العميل",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  if (!customer) return null;

  const getCustomerStatus = (debt: number, installments: number) => {
    const totalOutstanding = debt + installments;
    if (totalOutstanding > 500000) return { label: 'متأخر', color: 'bg-red-100 text-red-800' };
    if (totalOutstanding > 0) return { label: 'مديون', color: 'bg-yellow-100 text-yellow-800' };
    return { label: 'نشط', color: 'bg-green-100 text-green-800' };
  };

  const status = getCustomerStatus(financialSummary.totalDebt, financialSummary.totalInstallments);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-5xl max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <User className="w-6 h-6" />
            الملف الشخصي الكامل للعميل - {customer.name}
          </DialogTitle>
        </DialogHeader>
        
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-lg">جاري تحميل البيانات...</div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* معلومات العميل الأساسية */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  المعلومات الأساسية
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold">{customer.name}</h3>
                  <Badge className={status.color}>
                    {status.label}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">رقم الهاتف</p>
                      <p className="font-medium">{customer.phone || 'غير محدد'}</p>
                    </div>
                  </div>
                  
                  {customer.email && (
                    <div className="flex items-center gap-3">
                      <Mail className="w-5 h-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">البريد الإلكتروني</p>
                        <p className="font-medium">{customer.email}</p>
                      </div>
                    </div>
                  )}
                  
                  {customer.address && (
                    <div className="flex items-center gap-3">
                      <MapPin className="w-5 h-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">العنوان</p>
                        <p className="font-medium">{customer.address}</p>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-3">
                    <Calendar className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">تاريخ الانضمام</p>
                      <p className="font-medium">{new Date(customer.created_at).toLocaleDateString('ar-IQ')}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* الملخص المالي */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <ShoppingBag className="w-6 h-6 text-blue-500" />
                  </div>
                  <p className="text-sm text-gray-600">إجمالي المشتريات المباشرة</p>
                  <p className="text-2xl font-bold text-blue-600">{financialSummary.totalPurchases.toLocaleString()} د.ع</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <CreditCard className="w-6 h-6 text-red-500" />
                  </div>
                  <p className="text-sm text-gray-600">الديون المعلقة</p>
                  <p className={`text-2xl font-bold ${financialSummary.totalDebt > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {financialSummary.totalDebt.toLocaleString()} د.ع
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Calendar className="w-6 h-6 text-purple-500" />
                  </div>
                  <p className="text-sm text-gray-600">الأقساط المتبقية</p>
                  <p className="text-2xl font-bold text-purple-600">{financialSummary.totalInstallments.toLocaleString()} د.ع</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Clock className="w-6 h-6 text-green-500" />
                  </div>
                  <p className="text-sm text-gray-600">آخر شراء مباشر</p>
                  <p className="text-lg font-medium text-gray-900">
                    {financialSummary.lastPurchase}
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Customer Statistics */}
            {customerStats && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    إحصائيات العميل
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600">{customerStats.totalInvoices}</p>
                      <p className="text-sm text-gray-600">إجمالي الفواتير</p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">{customerStats.paidInvoices}</p>
                      <p className="text-sm text-gray-600">فواتير مدفوعة</p>
                    </div>
                    <div className="p-3 bg-yellow-50 rounded-lg">
                      <p className="text-2xl font-bold text-yellow-600">{customerStats.pendingInvoices}</p>
                      <p className="text-sm text-gray-600">فواتير معلقة</p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <p className="text-2xl font-bold text-purple-600">{customerStats.averageInvoice.toLocaleString()}</p>
                      <p className="text-sm text-gray-600">متوسط الفاتورة</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Purchase History with Enhanced Details */}
            {purchaseHistory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingBag className="w-5 h-5" />
                    سجل المشتريات المباشرة ({purchaseHistory.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {purchaseHistory.map((purchase) => (
                      <div key={purchase.id} className="border rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <Package className="w-5 h-5 text-blue-500" />
                            <span className="font-medium">فاتورة #{purchase.id.slice(-8)}</span>
                            <Badge className={purchase.status === 'مدفوع' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                              {purchase.status}
                            </Badge>
                          </div>
                          <span className="text-lg font-bold text-green-600">{purchase.amount.toLocaleString()} د.ع</span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4 text-gray-500" />
                            <div>
                              <span className="text-sm text-gray-600">تاريخ الشراء:</span>
                              <p className="font-medium">{purchase.date}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <CreditCard className="w-4 h-4 text-gray-500" />
                            <div>
                              <span className="text-sm text-gray-600">طريقة الدفع:</span>
                              <p className="font-medium">{purchase.paymentMethod}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Package className="w-4 h-4 text-gray-500" />
                            <div>
                              <span className="text-sm text-gray-600">عدد الأصناف:</span>
                              <p className="font-medium">{purchase.items.length} صنف</p>
                            </div>
                          </div>
                        </div>

                        {/* Product Details */}
                        {purchase.items && purchase.items.length > 0 && (
                          <div className="bg-gray-50 rounded-lg p-3">
                            <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-2">
                              <Package className="w-4 h-4" />
                              تفاصيل المنتجات:
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                              {purchase.items.map((item, index) => (
                                <div key={index} className="text-sm bg-white rounded px-3 py-2">
                                  <span className="font-medium text-blue-600">• {item}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Payment History */}
            {paymentHistory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    تاريخ المدفوعات ({paymentHistory.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {paymentHistory.map((payment) => (
                      <div key={payment.id} className="border rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <span className="font-medium">دفع #{payment.id.slice(-8)}</span>
                            <Badge className="bg-blue-100 text-blue-800">
                              {payment.method}
                            </Badge>
                          </div>
                          <span className="text-lg font-bold text-green-600">+{payment.amount.toLocaleString()} د.ع</span>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">التاريخ:</span> {payment.date}
                          </div>
                          <div>
                            <span className="font-medium">الملاحظة:</span> {payment.note}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Enhanced Installment Details */}
            {installmentHistory.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    تفاصيل الأقساط ({installmentHistory.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {installmentHistory.map((installment) => (
                      <div key={installment.id} className="border rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <Calendar className="w-5 h-5 text-purple-500" />
                            <span className="font-medium">قسط #{installment.id.slice(-8)}</span>
                            <Badge className={installment.status === 'مكتمل' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}>
                              {installment.status}
                            </Badge>
                          </div>
                          <span className="text-lg font-bold">{installment.totalAmount.toLocaleString()} د.ع</span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-gray-600">الدفعة المقدمة:</span>
                              <span className="font-medium">{installment.downPayment.toLocaleString()} د.ع</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">القسط الشهري:</span>
                              <span className="font-medium">{installment.monthlyPayment.toLocaleString()} د.ع</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">الأقساط المدفوعة:</span>
                              <span className="font-medium">{installment.paidInstallments} من {installment.totalInstallments}</span>
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-gray-600">المبلغ المتبقي:</span>
                              <span className="font-medium text-red-600">{installment.remainingAmount.toLocaleString()} د.ع</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">الدفعة القادمة:</span>
                              <span className="font-medium">{installment.nextPaymentDate}</span>
                            </div>
                          </div>
                        </div>

                        {/* Enhanced Product Details for Installments */}
                        {installment.items && installment.items.length > 0 && (
                          <div className="mt-3 bg-purple-50 rounded-lg p-3">
                            <h4 className="font-medium text-gray-700 mb-2 flex items-center gap-2">
                              <Package className="w-4 h-4" />
                              منتجات القسط:
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                              {installment.items.map((item, index) => (
                                <div key={index} className="text-sm bg-white rounded px-3 py-2">
                                  <span className="font-medium text-purple-600">• {item}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Progress bar for installments */}
                        <div className="mt-3">
                          <div className="flex justify-between text-xs text-gray-600 mb-1">
                            <span>التقدم</span>
                            <span>{Math.round((installment.paidInstallments / installment.totalInstallments) * 100)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${(installment.paidInstallments / installment.totalInstallments) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* حالة فارغة */}
            {!loading && purchaseHistory.length === 0 && paymentHistory.length === 0 && installmentHistory.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">لا توجد معاملات لهذا العميل حتى الآن</p>
                  <p className="text-sm text-gray-400 mt-2">سيتم عرض تاريخ المعاملات وتفاصيل المنتجات هنا عند إضافة فواتير أو مدفوعات</p>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)} className="w-full md:w-auto">
            إغلاق
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CustomerDetailsDialog;
