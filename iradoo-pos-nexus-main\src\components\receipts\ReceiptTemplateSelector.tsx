
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface Template {
  id: string;
  name: string;
  description: string;
  preview: string;
  features: string[];
}

interface ReceiptTemplateSelectorProps {
  selectedTemplate: string;
  onTemplateSelect: (templateId: string) => void;
}

const ReceiptTemplateSelector = ({ selectedTemplate, onTemplateSelect }: ReceiptTemplateSelectorProps) => {
  const templates: Template[] = [
    {
      id: 'classic',
      name: 'كلاسيكي',
      description: 'تصميم تقليدي بسيط ومناسب لجميع الأعمال',
      preview: '📄',
      features: ['خطوط واضحة', 'تنظيم تقليدي', 'سهل القراءة']
    },
    {
      id: 'modern',
      name: 'عصري',
      description: 'تصميم حديث مع ألوان جذابة ومظهر احترافي',
      preview: '✨',
      features: ['تدرجات زرقاء', 'رأس ملون', 'مظهر حديث']
    },
    {
      id: 'minimal',
      name: 'مبسط',
      description: 'تصميم نظيف ومبسط يركز على المحتوى',
      preview: '📋',
      features: ['تصميم نظيف', 'بدون ألوان زائدة', 'توفير في الحبر']
    },
    {
      id: 'colorful',
      name: 'ملون',
      description: 'تصميم حيوي مع ألوان متدرجة ومبهجة',
      preview: '🌈',
      features: ['ألوان متدرجة', 'مظهر حيوي', 'جذاب للعين']
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {templates.map((template) => (
        <Card 
          key={template.id} 
          className={`cursor-pointer transition-all hover:shadow-lg ${
            selectedTemplate === template.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
          }`}
          onClick={() => onTemplateSelect(template.id)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <span className="text-2xl">{template.preview}</span>
                {template.name}
              </CardTitle>
              {selectedTemplate === template.id && (
                <Badge variant="default">مختار</Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-3">{template.description}</p>
            <div className="flex flex-wrap gap-1">
              {template.features.map((feature, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {feature}
                </Badge>
              ))}
            </div>
            <Button 
              variant={selectedTemplate === template.id ? "default" : "outline"}
              size="sm" 
              className="w-full mt-3"
              onClick={(e) => {
                e.stopPropagation();
                onTemplateSelect(template.id);
              }}
            >
              {selectedTemplate === template.id ? 'مختار' : 'اختيار'}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ReceiptTemplateSelector;
