
import React, { useState } from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Trash2, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface ResetCustomersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onResetComplete: () => void;
}

const ResetCustomersDialog: React.FC<ResetCustomersDialogProps> = ({
  open,
  onOpenChange,
  onResetComplete
}) => {
  const { toast } = useToast();
  const [isResetting, setIsResetting] = useState(false);

  const handleResetCustomers = async () => {
    try {
      setIsResetting(true);
      console.log('Starting customer data reset...');

      // Use the updated database function that handles the proper DELETE with WHERE clauses
      const { error } = await supabase.rpc('reset_all_data');

      if (error) {
        console.error('Error resetting data:', error);
        throw error;
      }

      console.log('Customer data reset completed successfully');

      toast({
        title: "تم تصفير البيانات",
        description: "تم حذف جميع بيانات العملاء والمبيعات والديون بنجاح"
      });

      onResetComplete();
      onOpenChange(false);

    } catch (error) {
      console.error('Error resetting customer data:', error);
      toast({
        title: "خطأ",
        description: "فشل في تصفير بيانات العملاء",
        variant: "destructive"
      });
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-6 h-6 text-red-600" />
            <AlertDialogTitle className="text-red-600">تصفير جميع بيانات العملاء</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-right">
            <div className="space-y-2">
              <p className="font-semibold">تحذير: هذا الإجراء لا يمكن التراجع عنه!</p>
              <p>سيتم حذف جميع البيانات التالية:</p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>جميع العملاء</li>
                <li>جميع المبيعات</li>
                <li>جميع الديون</li>
                <li>جميع الأقساط</li>
                <li>جميع المدفوعات</li>
              </ul>
              <p className="text-red-600 font-semibold mt-3">هل أنت متأكد من المتابعة؟</p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex gap-2">
          <AlertDialogCancel disabled={isResetting}>
            إلغاء
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleResetCustomers}
            disabled={isResetting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isResetting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                جاري الحذف...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Trash2 className="w-4 h-4" />
                تصفير البيانات
              </div>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ResetCustomersDialog;
