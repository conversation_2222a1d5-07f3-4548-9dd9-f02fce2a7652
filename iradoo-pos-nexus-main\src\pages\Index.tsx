
import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Dashboard from '../components/Dashboard';
import LoginPage from '../components/auth/LoginPage';
import SalesPage from '../components/sales/SalesPage';
import InventoryPage from '../components/inventory/InventoryPage';
import CustomersPage from '../components/customers/CustomersPage';
import ReportsPage from '../components/reports/ReportsPage';
import SettingsPage from '../components/settings/SettingsPage';
import Sidebar from '../components/layout/Sidebar';
import Header from '../components/layout/Header';
import LicenseGuard from '../components/auth/LicenseGuard';
import { useAuth } from '../hooks/useAuth';
import { useSettings } from '../contexts/SettingsContext';
import { useLocalAuth } from "@/hooks/useLocalAuth";
import LocalLoginPage from "@/components/auth/LocalLoginPage";

const Index = () => {
  const { isAuthenticated, isLoading } = useLocalAuth();
  const { settings } = useSettings();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LocalLoginPage />;
  }

  return (
    <LicenseGuard>
      <div 
        className="min-h-screen bg-background flex w-full" 
        dir={settings.language === 'en' ? 'ltr' : 'rtl'}
      >
        <Sidebar />
        <div className="flex-1 flex flex-col min-w-0">
          <Header />
          <main className="flex-1 p-6 overflow-auto">
            <div className="max-w-7xl mx-auto">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/sales" element={<SalesPage />} />
                <Route path="/inventory" element={<InventoryPage />} />
                <Route path="/customers" element={<CustomersPage />} />
                <Route path="/reports" element={<ReportsPage />} />
                <Route path="/settings" element={<SettingsPage />} />
              </Routes>
            </div>
          </main>
        </div>
      </div>
    </LicenseGuard>
  );
};

export default Index;
