
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Settings, Image, RefreshCw } from 'lucide-react';
import { updateProduct, generateBarcode, type Product, type Category } from '@/utils/database';

interface EditProductDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEditProduct: () => void;
  product: Product | null;
  categories: Category[];
  onManageCategories: () => void;
}

const EditProductDialog = ({ 
  open, 
  onOpenChange, 
  onEditProduct, 
  product, 
  categories, 
  onManageCategories
}: EditProductDialogProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    category_id: '',
    price: '',
    stock: '',
    min_stock: '',
    barcode: '',
    image: ''
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        category_id: product.category_id || '',
        price: product.price.toString(),
        stock: product.stock.toString(),
        min_stock: product.min_stock.toString(),
        barcode: product.barcode || '',
        image: product.image || ''
      });
    }
  }, [product]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.category_id || !formData.price || !formData.stock || !formData.min_stock || !formData.barcode) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    const price = parseFloat(formData.price);
    const stock = parseInt(formData.stock);
    const minStock = parseInt(formData.min_stock);

    if (price <= 0 || stock < 0 || minStock < 0) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال قيم صحيحة للأسعار والكميات",
        variant: "destructive"
      });
      return;
    }

    if (formData.barcode.length < 8) {
      toast({
        title: "خطأ",
        description: "يجب أن يكون الباركود 8 أرقام على الأقل",
        variant: "destructive"
      });
      return;
    }

    if (!product) return;

    try {
      setLoading(true);
      await updateProduct(product.id, {
        name: formData.name,
        category_id: formData.category_id,
        price: price,
        stock: stock,
        min_stock: minStock,
        barcode: formData.barcode,
        image: formData.image || undefined
      });

      toast({
        title: "تم تحديث المنتج",
        description: `تم تحديث ${formData.name} بنجاح`,
      });
      
      onEditProduct();
    } catch (error: any) {
      console.error('Error updating product:', error);
      toast({
        title: "خطأ",
        description: error.message?.includes('duplicate') ? 
          "الباركود موجود مسبقاً، يرجى استخدام باركود آخر" : 
          "حدث خطأ أثناء تحديث المنتج",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleGenerateBarcode = async () => {
    try {
      const newBarcode = await generateBarcode();
      handleInputChange('barcode', newBarcode);
      toast({
        title: "تم توليد الباركود",
        description: `الباركود الجديد: ${newBarcode}`,
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء توليد الباركود",
        variant: "destructive"
      });
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageUrl = event.target?.result as string;
        handleInputChange('image', imageUrl);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md" dir="rtl">
        <DialogHeader>
          <DialogTitle>تعديل المنتج</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="edit-name">اسم المنتج *</Label>
            <Input
              id="edit-name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="أدخل اسم المنتج"
              className="text-right"
              required
            />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="edit-category">الفئة *</Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onManageCategories}
                className="text-blue-600 hover:text-blue-700"
              >
                <Settings className="w-4 h-4 ml-1" />
                إدارة الفئات
              </Button>
            </div>
            <Select value={formData.category_id} onValueChange={(value) => handleInputChange('category_id', value)}>
              <SelectTrigger className="text-right">
                <SelectValue placeholder="اختر فئة المنتج" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-barcode">الباركود *</Label>
            <div className="flex gap-2">
              <Input
                id="edit-barcode"
                value={formData.barcode}
                onChange={(e) => handleInputChange('barcode', e.target.value.replace(/\D/g, ''))}
                placeholder="أدخل الباركود أو اضغط توليد"
                className="text-right font-mono"
                required
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleGenerateBarcode}
                className="px-3"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
            <p className="text-xs text-gray-500">يجب أن يكون 8 أرقام على الأقل</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-image">صورة المنتج</Label>
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Input
                  id="edit-image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="text-right"
                />
                <Image className="w-5 h-5 text-gray-400" />
              </div>
              {formData.image && (
                <div className="mt-2">
                  <img
                    src={formData.image}
                    alt="معاينة الصورة"
                    className="w-20 h-20 object-cover rounded border"
                  />
                </div>
              )}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="edit-price">السعر (د.ع) *</Label>
            <Input
              id="edit-price"
              type="number"
              min="0"
              step="0.01"
              value={formData.price}
              onChange={(e) => handleInputChange('price', e.target.value)}
              placeholder="0.00"
              className="text-right"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-stock">الكمية الحالية *</Label>
              <Input
                id="edit-stock"
                type="number"
                min="0"
                value={formData.stock}
                onChange={(e) => handleInputChange('stock', e.target.value)}
                placeholder="0"
                className="text-right"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-minStock">الحد الأدنى *</Label>
              <Input
                id="edit-minStock"
                type="number"
                min="0"
                value={formData.min_stock}
                onChange={(e) => handleInputChange('min_stock', e.target.value)}
                placeholder="0"
                className="text-right"
                required
              />
            </div>
          </div>
          
          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              إلغاء
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700" disabled={loading}>
              {loading ? 'جاري التحديث...' : 'حفظ التغييرات'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditProductDialog;
