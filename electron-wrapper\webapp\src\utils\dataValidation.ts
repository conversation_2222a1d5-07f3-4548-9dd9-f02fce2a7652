
import { Product, Customer, Sale } from './database';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateProduct = (product: Partial<Product>): ValidationResult => {
  const errors: string[] = [];

  if (!product.name || product.name.trim().length === 0) {
    errors.push('اسم المنتج مطلوب');
  }

  if (product.name && product.name.length > 255) {
    errors.push('اسم المنتج يجب أن يكون أقل من 255 حرف');
  }

  if (!product.category_id) {
    errors.push('فئة المنتج مطلوبة');
  }

  if (product.price !== undefined && product.price < 0) {
    errors.push('سعر المنتج يجب أن يكون أكبر من أو يساوي الصفر');
  }

  if (product.stock !== undefined && product.stock < 0) {
    errors.push('كمية المخزون يجب أن تكون أكبر من أو تساوي الصفر');
  }

  if (product.min_stock !== undefined && product.min_stock < 0) {
    errors.push('الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي الصفر');
  }

  if (product.barcode && product.barcode.length > 50) {
    errors.push('الباركود يجب أن يكون أقل من 50 حرف');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateCustomer = (customer: Partial<Customer>): ValidationResult => {
  const errors: string[] = [];

  if (!customer.name || customer.name.trim().length === 0) {
    errors.push('اسم العميل مطلوب');
  }

  if (customer.name && customer.name.length > 255) {
    errors.push('اسم العميل يجب أن يكون أقل من 255 حرف');
  }

  if (customer.phone && !/^[0-9+\-\s()]+$/.test(customer.phone)) {
    errors.push('رقم الهاتف يحتوي على أحرف غير صالحة');
  }

  if (customer.phone && customer.phone.length > 20) {
    errors.push('رقم الهاتف يجب أن يكون أقل من 20 رقم');
  }

  if (customer.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customer.email)) {
    errors.push('عنوان البريد الإلكتروني غير صحيح');
  }

  if (customer.email && customer.email.length > 255) {
    errors.push('البريد الإلكتروني يجب أن يكون أقل من 255 حرف');
  }

  if (customer.address && customer.address.length > 500) {
    errors.push('العنوان يجب أن يكون أقل من 500 حرف');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateSale = (sale: Partial<Sale>, saleItems: any[]): ValidationResult => {
  const errors: string[] = [];

  if (!saleItems || saleItems.length === 0) {
    errors.push('يجب إضافة منتج واحد على الأقل للبيع');
  }

  if (sale.total_amount !== undefined && sale.total_amount < 0) {
    errors.push('المبلغ الإجمالي يجب أن يكون أكبر من الصفر');
  }

  if (sale.discount !== undefined && sale.discount < 0) {
    errors.push('قيمة الخصم يجب أن تكون أكبر من أو تساوي الصفر');
  }

  if (sale.tax !== undefined && sale.tax < 0) {
    errors.push('قيمة الضريبة يجب أن تكون أكبر من أو تساوي الصفر');
  }

  if (sale.final_amount !== undefined && sale.final_amount < 0) {
    errors.push('المبلغ النهائي يجب أن يكون أكبر من الصفر');
  }

  // Validate sale items
  saleItems.forEach((item, index) => {
    if (!item.product_id) {
      errors.push(`المنتج رقم ${index + 1}: معرف المنتج مطلوب`);
    }

    if (item.quantity <= 0) {
      errors.push(`المنتج رقم ${index + 1}: الكمية يجب أن تكون أكبر من الصفر`);
    }

    if (item.unit_price < 0) {
      errors.push(`المنتج رقم ${index + 1}: سعر الوحدة يجب أن يكون أكبر من أو يساوي الصفر`);
    }

    if (item.total_price < 0) {
      errors.push(`المنتج رقم ${index + 1}: المبلغ الإجمالي يجب أن يكون أكبر من أو يساوي الصفر`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/[\x00-\x1F\x7F]/g, ''); // Remove control characters
};

export const formatCurrency = (amount: number, currency: string = 'IQD'): string => {
  try {
    if (isNaN(amount)) return `0 ${currency}`;
    
    const formatter = new Intl.NumberFormat('ar-IQ', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
    
    return `${formatter.format(amount)} ${currency}`;
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${amount} ${currency}`;
  }
};

export const validateBarcode = (barcode: string): boolean => {
  if (!barcode || barcode.length === 0) return false;
  
  // Basic barcode validation - digits only and reasonable length
  return /^\d{8,13}$/.test(barcode);
};

export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const isValidDate = (date: any): boolean => {
  return date instanceof Date && !isNaN(date.getTime());
};

export const formatDate = (date: Date | string, locale: string = 'ar-IQ'): string => {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!isValidDate(dateObj)) return '';
    
    return dateObj.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};
