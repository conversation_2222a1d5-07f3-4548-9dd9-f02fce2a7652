
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Mail, Shield } from 'lucide-react';
import { licenseManager } from '@/utils/licenseManager';

interface EmailLicenseLoginProps {
  onSuccess: () => void;
}

const EmailLicenseLogin: React.FC<EmailLicenseLoginProps> = ({ onSuccess }) => {
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال البريد الإلكتروني',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = licenseManager.loginWithEmail(email.trim());
      
      if (result.success && result.licenseRecord) {
        toast({
          title: 'تم تسجيل الدخول بنجاح',
          description: `مرحباً ${result.licenseRecord.customerName}`,
        });
        onSuccess();
      } else {
        toast({
          title: 'فشل تسجيل الدخول',
          description: result.error || 'لا يوجد ترخيص صالح لهذا البريد الإلكتروني',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Email login error:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
          <Mail className="w-6 h-6 text-green-600" />
        </div>
        <CardTitle className="text-xl">تسجيل الدخول بالبريد الإلكتروني</CardTitle>
        <CardDescription>
          إذا كان لديك ترخيص مرتبط ببريدك الإلكتروني
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleEmailLogin} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">البريد الإلكتروني</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="text-right"
              required
            />
          </div>
          
          <Button
            type="submit"
            className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                جاري التحقق...
              </>
            ) : (
              <>
                <Shield className="w-4 h-4 mr-2" />
                تسجيل الدخول
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default EmailLicenseLogin;
