import React, { useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import { Printer, Download, Share2, X, MessageCircle } from 'lucide-react';
import ReceiptPreview from '../receipts/ReceiptPreview';

interface ReceiptDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  saleData: {
    id: string;
    cart: Array<{
      product: {
        id: string;
        name: string;
        price: number;
      };
      quantity: number;
      price: number;
    }>;
    selectedCustomer?: {
      id: string;
      name: string;
      phone?: string;
      address?: string;
    };
    total: number;
    subtotal: number;
    discount: number;
    tax: number;
    paymentMethod: string;
    timestamp: string;
  } | null;
  autoPrint?: boolean;
}

const ReceiptDialog = ({ open, onOpenChange, saleData, autoPrint = false }: ReceiptDialogProps) => {
  const { toast } = useToast();
  const { settings, formatCurrency } = useSettings();
  const printRef = useRef<HTMLDivElement>(null);

  // طباعة تلقائية عند فتح الحوار
  React.useEffect(() => {
    if (open && autoPrint && saleData) {
      setTimeout(() => {
        handlePrint();
      }, 1000);
    }
  }, [open, autoPrint, saleData]);

  if (!saleData) return null;

  // تحويل بيانات السلة إلى تنسيق الفاتورة
  const receiptItems = saleData.cart.map(item => ({
    id: item.product.id,
    name: item.product.name,
    price: item.price,
    quantity: item.quantity,
    total: item.price * item.quantity
  }));

  const handlePrint = () => {
    // بدلاً من استخدام innerHTML، سنقوم بإنشاء محتوى HTML مخصص للطباعة
    const template = settings.receiptTemplate || 'classic';

    // إنشاء محتوى الفاتورة للطباعة
    const createPrintContent = () => {
      const customerSection = saleData.selectedCustomer ? `
        <div class="customer-info">
          <h4>معلومات العميل</h4>
          <p><strong>الاسم:</strong> ${saleData.selectedCustomer.name}</p>
          ${saleData.selectedCustomer.phone ? `<p><strong>الهاتف:</strong> ${saleData.selectedCustomer.phone}</p>` : ''}
          ${saleData.selectedCustomer.address ? `<p><strong>العنوان:</strong> ${saleData.selectedCustomer.address}</p>` : ''}
        </div>
      ` : '';

      const isA4 = settings.paperSize === 'A4';
      const itemsRows = receiptItems.map((item, index) => `
        <tr>
          <td style="text-align: right; word-wrap: break-word;">${item.name}</td>
          <td style="text-align: center;">${item.quantity}</td>
          <td style="text-align: center;">${formatCurrency(item.price)}</td>
          <td style="text-align: left; font-weight: bold;">${formatCurrency(item.total)}</td>
        </tr>
      `).join('');

      const discountRow = saleData.discount > 0 ? `
        <div class="total-row" style="color: #dc2626;">
          <span>الخصم (${saleData.discount}%):</span>
          <span>-${formatCurrency((saleData.subtotal * saleData.discount) / 100)}</span>
        </div>
      ` : '';

      const taxRow = saleData.tax > 0 ? `
        <div class="total-row" style="color: #2563eb;">
          <span>الضريبة (${saleData.tax}%):</span>
          <span>+${formatCurrency(((saleData.subtotal - (saleData.subtotal * saleData.discount) / 100) * saleData.tax) / 100)}</span>
        </div>
      ` : '';

      return `
        <div class="receipt-container">
          <div class="receipt-header">
            <div class="store-name">${settings.storeName || 'متجر إرادو'}</div>
            <div class="receipt-number">فاتورة رقم: ${settings.receiptPrefix || 'INV'}-${saleData.id.slice(-6)}</div>
            <div style="font-size: 14px; margin-top: 8px;">
              ${new Date(saleData.timestamp).toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              })}
            </div>
            ${settings.storeAddress ? `<p style="font-size: 14px; margin: 5px 0;">${settings.storeAddress}</p>` : ''}
            ${settings.storePhone ? `<p style="font-size: 14px; margin: 5px 0;">${settings.storePhone}</p>` : ''}
            ${settings.taxNumber ? `<p style="font-size: 12px; opacity: 0.8;">الرقم الضريبي: ${settings.taxNumber}</p>` : ''}
          </div>

          <div class="receipt-body">
            ${customerSection}

            <table class="items-table">
              <thead>
                <tr>
                  <th style="text-align: right; width: 40%;">المنتج</th>
                  <th style="text-align: center; width: 15%;">الكمية</th>
                  <th style="text-align: center; width: 22%;">السعر</th>
                  <th style="text-align: left; width: 23%;">المجموع</th>
                </tr>
              </thead>
              <tbody>
                ${itemsRows}
              </tbody>
            </table>

            <div class="totals">
              <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span>${formatCurrency(saleData.subtotal)}</span>
              </div>
              ${discountRow}
              ${taxRow}
              <div class="total-row final-total">
                <span>المجموع النهائي:</span>
                <span>${formatCurrency(saleData.total)}</span>
              </div>
              <div class="total-row" style="font-size: 14px; margin-top: 8px;">
                <span>طريقة الدفع:</span>
                <span>${saleData.paymentMethod}</span>
              </div>
            </div>

            <div class="receipt-footer">
              ${settings.receiptFooter ? `<p style="margin-bottom: 8px;">${settings.receiptFooter}</p>` : ''}
              <p>شكراً لزيارتكم</p>
              <p style="font-size: 10px; margin-top: 8px;">
                ${settings.storeName} - ${settings.storeAddress || ''}
              </p>
              ${settings.storePhone ? `<p style="font-size: 10px;">هاتف: ${settings.storePhone}</p>` : ''}
            </div>
          </div>
        </div>
      `;
    };

    // تحديد أنماط CSS محسنة للطباعة حسب حجم الورق
    const getTemplateCSS = () => {
      const isA4 = settings.paperSize === 'A4';

      const baseCSS = `
        * {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }

        html, body {
          width: 100%;
          height: 100%;
          margin: 0;
          padding: 0;
        }

        @page {
          ${isA4 ?
            'size: A4 portrait; margin: 15mm 10mm;' :
            'size: 80mm auto; margin: 2mm;'
          }
        }

        body {
          font-family: 'Arial', 'Tahoma', sans-serif;
          direction: rtl;
          text-align: right;
          background: white;
          color: #000;
          font-size: ${isA4 ? '14px' : '11px'};
          line-height: 1.4;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        .receipt-container {
          width: 100%;
          max-width: ${isA4 ? '180mm' : '76mm'};
          margin: 0 auto;
          background: white;
          padding: ${isA4 ? '5mm' : '2mm'};
          box-sizing: border-box;
        }

        .receipt-header {
          padding: ${isA4 ? '10px' : '8px'};
          text-align: center;
          border-bottom: 2px solid #000;
          background: white;
          margin-bottom: ${isA4 ? '10px' : '8px'};
        }

        .store-name {
          font-size: ${isA4 ? '24px' : '18px'};
          font-weight: bold;
          margin-bottom: ${isA4 ? '8px' : '6px'};
          color: #000;
        }

        .receipt-number {
          font-size: ${isA4 ? '16px' : '12px'};
          font-weight: bold;
          margin-bottom: ${isA4 ? '8px' : '6px'};
          color: #000;
        }

        .receipt-body {
          padding: ${isA4 ? '10px' : '8px'};
        }

        .customer-info {
          background: ${isA4 ? '#f8f8f8' : 'transparent'};
          padding: ${isA4 ? '12px' : '8px'};
          margin-bottom: ${isA4 ? '15px' : '10px'};
          border: 1px solid #000;
          border-radius: 0;
        }

        .customer-info h4 {
          font-weight: bold;
          margin-bottom: ${isA4 ? '8px' : '6px'};
          font-size: ${isA4 ? '16px' : '12px'};
          color: #000;
        }

        .customer-info p {
          margin: ${isA4 ? '4px 0' : '3px 0'};
          font-size: ${isA4 ? '14px' : '11px'};
          color: #333;
        }

        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: ${isA4 ? '15px' : '10px'};
          border: 2px solid #000;
          font-size: ${isA4 ? '14px' : '11px'};
        }

        .items-table th {
          padding: ${isA4 ? '10px 8px' : '6px 4px'};
          text-align: center;
          border-bottom: 2px solid #000;
          background: ${isA4 ? '#f0f0f0' : 'transparent'};
          font-weight: bold;
          font-size: ${isA4 ? '14px' : '11px'};
          color: #000;
        }

        .items-table td {
          padding: ${isA4 ? '8px 6px' : '5px 3px'};
          text-align: center;
          border-bottom: 1px solid #ddd;
          font-size: ${isA4 ? '13px' : '10px'};
          color: #000;
        }

        .items-table td:first-child {
          text-align: right;
          word-wrap: break-word;
          padding-right: ${isA4 ? '8px' : '4px'};
        }

        .items-table td:last-child {
          text-align: left;
          font-weight: bold;
          padding-left: ${isA4 ? '8px' : '4px'};
        }

        .items-table tbody tr:last-child td {
          border-bottom: 2px solid #000;
        }

        .totals {
          border-top: 2px solid #000;
          padding-top: ${isA4 ? '15px' : '10px'};
          margin-top: ${isA4 ? '15px' : '10px'};
        }

        .total-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: ${isA4 ? '8px' : '6px'};
          font-size: ${isA4 ? '14px' : '11px'};
          padding: ${isA4 ? '3px 0' : '2px 0'};
          color: #000;
        }

        .final-total {
          font-size: ${isA4 ? '18px' : '14px'};
          font-weight: bold;
          border-top: 2px solid #000;
          padding-top: ${isA4 ? '10px' : '8px'};
          margin-top: ${isA4 ? '10px' : '8px'};
          color: #000;
        }

        .receipt-footer {
          text-align: center;
          margin-top: ${isA4 ? '15px' : '10px'};
          padding-top: ${isA4 ? '10px' : '8px'};
          border-top: 2px solid #000;
          font-size: ${isA4 ? '12px' : '10px'};
          color: #666;
        }

        .receipt-footer p {
          margin: 2px 0;
          line-height: 1.3;
        }

        /* تحسينات خاصة بالطباعة */
        @media print {
          @page {
            ${isA4 ?
              'size: A4 portrait !important; margin: 10mm !important;' :
              'size: 80mm auto !important; margin: 2mm !important;'
            }
          }

          * {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }

          html, body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: auto !important;
            background: white !important;
            font-size: ${isA4 ? '12px' : '10px'} !important;
          }

          .receipt-container {
            width: 100% !important;
            max-width: ${isA4 ? '170mm' : '76mm'} !important;
            margin: 0 auto !important;
            padding: ${isA4 ? '5mm' : '2mm'} !important;
            page-break-inside: avoid !important;
            background: white !important;
            box-sizing: border-box !important;
            border: none !important;
          }

          .receipt-header {
            border-bottom: 2px solid #000 !important;
            background: white !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            padding: ${isA4 ? '8px' : '6px'} !important;
            margin-bottom: ${isA4 ? '8px' : '6px'} !important;
          }

          .store-name {
            font-size: ${isA4 ? '20px' : '16px'} !important;
            margin-bottom: ${isA4 ? '6px' : '4px'} !important;
          }

          .receipt-number {
            font-size: ${isA4 ? '14px' : '11px'} !important;
            margin-bottom: ${isA4 ? '6px' : '4px'} !important;
          }

          .receipt-body {
            padding: ${isA4 ? '8px' : '6px'} !important;
          }

          .items-table {
            border: 2px solid #000 !important;
            font-size: ${isA4 ? '12px' : '10px'} !important;
            margin-bottom: ${isA4 ? '12px' : '8px'} !important;
            width: 100% !important;
          }

          .items-table th {
            border-bottom: 2px solid #000 !important;
            background: ${isA4 ? '#f0f0f0' : 'transparent'} !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            padding: ${isA4 ? '8px 6px' : '5px 3px'} !important;
            font-size: ${isA4 ? '12px' : '10px'} !important;
          }

          .items-table td {
            border-bottom: 1px solid #ddd !important;
            padding: ${isA4 ? '6px 4px' : '4px 2px'} !important;
            font-size: ${isA4 ? '11px' : '9px'} !important;
          }

          .totals {
            border-top: 2px solid #000 !important;
            padding-top: ${isA4 ? '12px' : '8px'} !important;
            margin-top: ${isA4 ? '12px' : '8px'} !important;
          }

          .total-row {
            margin-bottom: ${isA4 ? '6px' : '4px'} !important;
            font-size: ${isA4 ? '12px' : '10px'} !important;
          }

          .final-total {
            font-size: ${isA4 ? '16px' : '13px'} !important;
            border-top: 2px solid #000 !important;
            padding-top: ${isA4 ? '8px' : '6px'} !important;
            margin-top: ${isA4 ? '8px' : '6px'} !important;
          }

          .receipt-footer {
            border-top: 2px solid #000 !important;
            margin-top: ${isA4 ? '12px' : '8px'} !important;
            padding-top: ${isA4 ? '8px' : '6px'} !important;
            font-size: ${isA4 ? '10px' : '8px'} !important;
          }

          .items-table tbody tr:last-child td {
            border-bottom: 2px solid #000 !important;
          }

          .totals {
            border-top: 3px solid #000 !important;
          }

          .final-total {
            border-top: 2px solid #000 !important;
          }

          .receipt-footer {
            border-top: 2px solid #000 !important;
          }

          .customer-info {
            background: #f8f8f8 !important;
            border: 1px solid #000 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
          }

          /* إخفاء عناصر غير ضرورية */
          @page {
            margin: 10mm !important;
          }
        }
      `;

      switch (template) {
        case 'modern':
          return baseCSS + `
            .receipt-header {
              background: #2563eb !important;
              color: white !important;
              border-bottom: 3px solid #2563eb !important;
            }
            .store-name, .receipt-number {
              color: white !important;
            }
            .totals {
              border-top: 3px solid #2563eb !important;
            }
            .final-total {
              color: #2563eb !important;
              border-top: 2px solid #2563eb !important;
            }
            .items-table th {
              background: #dbeafe !important;
              color: #1e40af !important;
            }
            @media print {
              .receipt-header {
                background: #2563eb !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
              .store-name, .receipt-number {
                color: white !important;
              }
              .items-table th {
                background: #dbeafe !important;
                color: #1e40af !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
              .totals {
                border-top: 3px solid #2563eb !important;
              }
              .final-total {
                color: #2563eb !important;
                border-top: 2px solid #2563eb !important;
              }
            }
          `;
        case 'minimal':
          return baseCSS + `
            .receipt-container {
              border: 2px solid #000 !important;
            }
            .receipt-header {
              background: white !important;
              border-bottom: 3px solid #000 !important;
            }
            .items-table th {
              background: white !important;
              border-bottom: 3px solid #000 !important;
            }
            .totals {
              border-top: 3px solid #000 !important;
            }
            .final-total {
              border-top: 3px solid #000 !important;
            }
            @media print {
              .receipt-container {
                border: 2px solid #000 !important;
              }
              .receipt-header {
                background: white !important;
                border-bottom: 3px solid #000 !important;
              }
              .items-table th {
                background: white !important;
                border-bottom: 3px solid #000 !important;
              }
            }
          `;
        case 'colorful':
          return baseCSS + `
            .receipt-header {
              background: #dc2626 !important;
              color: white !important;
              border-bottom: 3px solid #dc2626 !important;
            }
            .store-name, .receipt-number {
              color: white !important;
            }
            .customer-info {
              background: #fef2f2 !important;
              border: 2px solid #dc2626 !important;
            }
            .items-table th {
              background: #fee2e2 !important;
              color: #991b1b !important;
            }
            .totals {
              border-top: 3px solid #dc2626 !important;
            }
            .final-total {
              color: #dc2626 !important;
              border-top: 2px solid #dc2626 !important;
            }
            @media print {
              .receipt-header {
                background: #dc2626 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
              .store-name, .receipt-number {
                color: white !important;
              }
              .customer-info {
                background: #fef2f2 !important;
                border: 2px solid #dc2626 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
              .items-table th {
                background: #fee2e2 !important;
                color: #991b1b !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
              .totals {
                border-top: 3px solid #dc2626 !important;
              }
              .final-total {
                color: #dc2626 !important;
                border-top: 2px solid #dc2626 !important;
              }
            }
          `;
        default: // classic
          return baseCSS;
      }
    };

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <title>فاتورة رقم ${settings.receiptPrefix || 'INV'}-${saleData.id.slice(-6)}</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta name="format-detection" content="telephone=no">
          <style>
            ${getTemplateCSS()}

            /* إضافات لضمان ظهور الفاتورة كاملة */
            @media print {
              body {
                overflow: visible !important;
                height: auto !important;
              }

              .receipt-container {
                overflow: visible !important;
                height: auto !important;
                page-break-inside: avoid !important;
              }

              /* منع قطع العناصر */
              .receipt-header,
              .customer-info,
              .items-table,
              .totals,
              .receipt-footer {
                page-break-inside: avoid !important;
              }

              /* ضمان ظهور الحدود */
              * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
              }
            }
          </style>
        </head>
        <body onload="setTimeout(() => { window.print(); }, 500);">
          ${createPrintContent()}
        </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.focus();

      // إظهار رسالة النجاح
      toast({
        title: "تم فتح نافذة الطباعة",
        description: `جاري طباعة فاتورة رقم ${settings.receiptPrefix || 'INV'}-${saleData.id.slice(-6)}`,
      });
    }
  };

  const handleSave = () => {
    // استخدام نفس منطق إنشاء المحتوى المستخدم في الطباعة
    const template = settings.receiptTemplate || 'classic';

    // إنشاء محتوى الفاتورة للحفظ (نفس المحتوى المستخدم في الطباعة)
    const createSaveContent = () => {
      const customerSection = saleData.selectedCustomer ? `
        <div class="customer-info">
          <h4>معلومات العميل</h4>
          <p><strong>الاسم:</strong> ${saleData.selectedCustomer.name}</p>
          ${saleData.selectedCustomer.phone ? `<p><strong>الهاتف:</strong> ${saleData.selectedCustomer.phone}</p>` : ''}
          ${saleData.selectedCustomer.address ? `<p><strong>العنوان:</strong> ${saleData.selectedCustomer.address}</p>` : ''}
        </div>
      ` : '';

      const itemsRows = receiptItems.map(item => `
        <tr>
          <td>${item.name}</td>
          <td style="text-align: center;">${item.quantity}</td>
          <td style="text-align: center;">${formatCurrency(item.price)}</td>
          <td style="text-align: left;">${formatCurrency(item.total)}</td>
        </tr>
      `).join('');

      const discountRow = saleData.discount > 0 ? `
        <div class="total-row" style="color: #dc2626;">
          <span>الخصم (${saleData.discount}%):</span>
          <span>-${formatCurrency((saleData.subtotal * saleData.discount) / 100)}</span>
        </div>
      ` : '';

      const taxRow = saleData.tax > 0 ? `
        <div class="total-row" style="color: #2563eb;">
          <span>الضريبة (${saleData.tax}%):</span>
          <span>+${formatCurrency(((saleData.subtotal - (saleData.subtotal * saleData.discount) / 100) * saleData.tax) / 100)}</span>
        </div>
      ` : '';

      return `
        <div class="receipt-container">
          <div class="receipt-header">
            <div class="store-name">${settings.storeName || 'متجر إرادو'}</div>
            <div class="receipt-number">فاتورة رقم: ${settings.receiptPrefix || 'INV'}-${saleData.id.slice(-6)}</div>
            <div style="font-size: 14px; margin-top: 8px;">
              ${new Date(saleData.timestamp).toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              })}
            </div>
            ${settings.storeAddress ? `<p style="font-size: 14px; margin: 5px 0;">${settings.storeAddress}</p>` : ''}
            ${settings.storePhone ? `<p style="font-size: 14px; margin: 5px 0;">${settings.storePhone}</p>` : ''}
            ${settings.taxNumber ? `<p style="font-size: 12px; opacity: 0.8;">الرقم الضريبي: ${settings.taxNumber}</p>` : ''}
          </div>

          <div class="receipt-body">
            ${customerSection}

            <table class="items-table">
              <thead>
                <tr>
                  <th>المنتج</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>المجموع</th>
                </tr>
              </thead>
              <tbody>
                ${itemsRows}
              </tbody>
            </table>

            <div class="totals">
              <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span>${formatCurrency(saleData.subtotal)}</span>
              </div>
              ${discountRow}
              ${taxRow}
              <div class="total-row final-total">
                <span>المجموع النهائي:</span>
                <span>${formatCurrency(saleData.total)}</span>
              </div>
              <div class="total-row" style="font-size: 14px; margin-top: 8px;">
                <span>طريقة الدفع:</span>
                <span>${saleData.paymentMethod}</span>
              </div>
            </div>

            <div class="receipt-footer">
              ${settings.receiptFooter ? `<p style="margin-bottom: 8px;">${settings.receiptFooter}</p>` : ''}
              <p>شكراً لزيارتكم</p>
              <p style="font-size: 10px; margin-top: 8px;">
                ${settings.storeName} - ${settings.storeAddress || ''}
              </p>
              ${settings.storePhone ? `<p style="font-size: 10px;">هاتف: ${settings.storePhone}</p>` : ''}
            </div>
          </div>
        </div>
      `;
    };

    // استخدام نفس CSS المستخدم في الطباعة
    const getTemplateCSS = () => {
      const baseCSS = `
        body {
          font-family: 'Arial', 'Tahoma', sans-serif;
          direction: rtl;
          text-align: right;
          margin: 0;
          padding: 20px;
          background: white;
          color: #000;
          font-size: 14px;
          line-height: 1.4;
        }
        .receipt-container {
          max-width: 400px;
          margin: 0 auto;
          background: white;
          border: 2px solid #333;
          padding: 0;
        }
        .receipt-header {
          padding: 20px;
          text-align: center;
          border-bottom: 2px solid #333;
        }
        .store-name {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 8px;
        }
        .receipt-number {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .receipt-body {
          padding: 20px;
        }
        .customer-info {
          background: #f5f5f5;
          padding: 15px;
          margin-bottom: 20px;
          border: 1px solid #ddd;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          border: 2px solid #333;
        }
        .items-table th,
        .items-table td {
          padding: 10px 8px;
          text-align: right;
          border-bottom: 1px solid #333;
          font-size: 14px;
        }
        .items-table th {
          background: #f0f0f0;
          font-weight: bold;
          border-bottom: 2px solid #333;
        }
        .totals {
          border-top: 3px solid #333;
          padding-top: 15px;
          margin-top: 20px;
        }
        .total-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 14px;
          padding: 2px 0;
        }
        .final-total {
          font-size: 18px;
          font-weight: bold;
          border-top: 2px solid #333;
          padding-top: 10px;
          margin-top: 10px;
        }
        .receipt-footer {
          text-align: center;
          margin-top: 20px;
          padding-top: 15px;
          border-top: 2px solid #333;
          font-size: 12px;
        }
      `;

      switch (template) {
        case 'modern':
          return baseCSS + `
            .receipt-header {
              background: #4f46e5;
              color: white;
              border-bottom: 3px solid #4f46e5;
            }
            .totals {
              border-top: 3px solid #4f46e5;
            }
            .final-total {
              color: #4f46e5;
              border-top: 2px solid #4f46e5;
            }
          `;
        case 'minimal':
          return baseCSS + `
            .receipt-container {
              border: 1px solid #666;
            }
            .receipt-header {
              background: white;
              border-bottom: 3px solid #000;
            }
            .items-table th {
              background: white;
              border-bottom: 3px solid #000;
            }
          `;
        case 'colorful':
          return baseCSS + `
            .receipt-header {
              background: #ec4899;
              color: white;
              border-bottom: 3px solid #ec4899;
            }
            .customer-info {
              background: #fce7f3;
              border: 2px solid #ec4899;
            }
            .totals {
              border-top: 3px solid #ec4899;
            }
            .final-total {
              color: #ec4899;
              border-top: 2px solid #ec4899;
            }
          `;
        default: // classic
          return baseCSS;
      }
    };

    const blob = new Blob([`
      <!DOCTYPE html>
      <html>
      <head>
        <title>فاتورة رقم ${settings.receiptPrefix || 'INV'}-${saleData.id.slice(-6)}</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          ${getTemplateCSS()}
        </style>
      </head>
      <body>
        ${createSaveContent()}
      </body>
      </html>
    `], { type: 'text/html' });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `فاتورة-${settings.receiptPrefix || 'INV'}-${saleData.id.slice(-6)}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "تم حفظ الفاتورة",
      description: `تم حفظ فاتورة رقم ${settings.receiptPrefix || 'INV'}-${saleData.id.slice(-6)} بنجاح`,
    });
  };

  const handleShare = () => {
    const shareText = `فاتورة رقم ${saleData.id.slice(-6)} - ${formatCurrency(saleData.total)}`;
    if (navigator.share) {
      navigator.share({
        title: `فاتورة رقم ${saleData.id.slice(-6)}`,
        text: shareText,
        url: window.location.href
      }).then(() => {
        toast({
          title: "تم مشاركة الفاتورة",
          description: "تم مشاركة تفاصيل الفاتورة بنجاح",
        });
      }).catch(() => {
        navigator.clipboard.writeText(shareText);
        toast({
          title: "تم نسخ تفاصيل الفاتورة",
          description: "تم نسخ تفاصيل الفاتورة إلى الحافظة",
        });
      });
    } else {
      navigator.clipboard.writeText(shareText);
      toast({
        title: "تم نسخ تفاصيل الفاتورة",
        description: "تم نسخ تفاصيل الفاتورة إلى الحافظة",
      });
    }
  };

  const handleWhatsAppShare = () => {
    if (!saleData.selectedCustomer?.phone) {
      toast({
        title: "لا يوجد رقم هاتف",
        description: "لا يوجد رقم هاتف للعميل لإرسال الفاتورة",
        variant: "destructive"
      });
      return;
    }

    const receiptText = `
🧾 *فاتورة من ${settings.storeName}*

📋 رقم الفاتورة: ${settings.receiptPrefix || 'INV'}-${saleData.id.slice(-6)}
📅 التاريخ: ${new Date(saleData.timestamp).toLocaleDateString('ar-SA')}
👤 العميل: ${saleData.selectedCustomer.name}

📦 *المنتجات:*
${receiptItems.map(item => 
  `• ${item.name} - الكمية: ${item.quantity} - ${formatCurrency(item.total)}`
).join('\n')}

💰 *المجموع الفرعي:* ${formatCurrency(saleData.subtotal)}
${saleData.discount > 0 ? `🏷️ *الخصم:* ${saleData.discount}%\n` : ''}
${saleData.tax > 0 ? `📊 *الضريبة:* ${saleData.tax}%\n` : ''}
💳 *المجموع النهائي:* ${formatCurrency(saleData.total)}
💵 *طريقة الدفع:* ${saleData.paymentMethod}

${settings.receiptFooter ? `\n${settings.receiptFooter}` : ''}

شكراً لزيارتكم 🙏
    `.trim();

    const phoneNumber = saleData.selectedCustomer.phone.replace(/[^\d]/g, '');
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(receiptText)}`;
    
    window.open(whatsappUrl, '_blank');
    
    toast({
      title: "تم فتح WhatsApp",
      description: "تم فتح WhatsApp لإرسال الفاتورة للعميل",
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Printer className="w-5 h-5" />
              فاتورة رقم {settings.receiptPrefix || 'INV'}-{saleData.id.slice(-6)}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div ref={printRef}>
            <ReceiptPreview
              items={receiptItems}
              total={saleData.total}
              template={settings.receiptTemplate as any}
              showLogo={settings.printLogo}
              showQR={settings.printQR}
              customerInfo={saleData.selectedCustomer ? {
                name: saleData.selectedCustomer.name,
                phone: saleData.selectedCustomer.phone
              } : undefined}
              saleData={{
                id: saleData.id,
                timestamp: saleData.timestamp,
                subtotal: saleData.subtotal,
                discount: saleData.discount,
                tax: saleData.tax,
                paymentMethod: saleData.paymentMethod
              }}
            />
          </div>

          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={handlePrint} className="flex-1 bg-blue-600 hover:bg-blue-700">
              <Printer className="w-4 h-4 ml-2" />
              طباعة
            </Button>
            <Button onClick={handleSave} variant="outline" className="flex-1">
              <Download className="w-4 h-4 ml-2" />
              حفظ
            </Button>
            {saleData.selectedCustomer?.phone && (
              <Button onClick={handleWhatsAppShare} variant="outline" className="flex-1 bg-green-50 hover:bg-green-100 text-green-700">
                <MessageCircle className="w-4 h-4 ml-2" />
                WhatsApp
              </Button>
            )}
            <Button onClick={handleShare} variant="outline" className="flex-1">
              <Share2 className="w-4 h-4 ml-2" />
              مشاركة
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReceiptDialog;
