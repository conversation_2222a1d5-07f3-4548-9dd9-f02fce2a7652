
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { Edit, Save, X, UserCog } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { localUserManager, LocalUser } from '@/utils/localUserManager';
import { useLocalAuth } from '@/hooks/useLocalAuth';

const AccountManagement = () => {
  const { toast } = useToast();
  const { user: currentUser, canManageUsers } = useLocalAuth();
  const [users, setUsers] = useState<LocalUser[]>([]);
  const [editingUser, setEditingUser] = useState<LocalUser | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    email: '',
    role: 'cashier' as 'admin' | 'cashier' | 'supervisor',
    changePassword: false,
    newPassword: ''
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = () => {
    const allUsers = localUserManager.getAllUsers();
    setUsers(allUsers);
  };

  const handleEditUser = (user: LocalUser) => {
    setEditingUser(user);
    setEditForm({
      name: user.name,
      email: user.email,
      role: user.role,
      changePassword: false,
      newPassword: ''
    });
    setShowEditDialog(true);
  };

  const handleSaveEdit = () => {
    if (!editingUser) return;

    const updates: Partial<LocalUser> = {
      name: editForm.name,
      email: editForm.email,
      role: editForm.role
    };

    if (editForm.changePassword && editForm.newPassword) {
      updates.password = editForm.newPassword;
    }

    try {
      const updatedUser = localUserManager.updateUser(editingUser.id, updates);
      if (updatedUser) {
        fetchUsers();
        setShowEditDialog(false);
        setEditingUser(null);
        toast({
          title: "تم التحديث",
          description: "تم تحديث بيانات المستخدم بنجاح",
        });
      }
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث بيانات المستخدم",
        variant: "destructive"
      });
    }
  };

  const roleLabels = {
    admin: 'مدير النظام',
    supervisor: 'مشرف',
    cashier: 'كاشير'
  };

  const roleColors = {
    admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    supervisor: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    cashier: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  };

  if (!canManageUsers && currentUser?.role !== 'admin') {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <UserCog className="mx-auto h-12 w-12 mb-4" />
            <p>ليس لديك صلاحية لتعديل الحسابات</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCog className="w-5 h-5" />
            إدارة وتعديل الحسابات
          </CardTitle>
          <CardDescription>
            تعديل بيانات المستخدمين الحاليين وصلاحياتهم
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg bg-card">
                <div className="flex items-center gap-4">
                  <Avatar className="w-12 h-12">
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                      {user.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-foreground">{user.name}</h3>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={roleColors[user.role]}>
                        {roleLabels[user.role]}
                      </Badge>
                      <Badge variant={user.active ? "default" : "secondary"}>
                        {user.active ? 'نشط' : 'غير نشط'}
                      </Badge>
                      {currentUser?.id === user.id && (
                        <Badge variant="outline" className="text-xs">
                          الحساب الحالي
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditUser(user)}
                    className="flex items-center gap-1"
                  >
                    <Edit className="w-4 h-4" />
                    تعديل
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>تعديل بيانات المستخدم</DialogTitle>
            <DialogDescription>
              قم بتعديل المعلومات الأساسية للمستخدم
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="editName">الاسم</Label>
              <Input
                id="editName"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="editEmail">البريد الإلكتروني</Label>
              <Input
                id="editEmail"
                type="email"
                value={editForm.email}
                onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                className="text-right"
                dir="ltr"
              />
            </div>
            <div className="space-y-2">
              <Label>الصلاحية</Label>
              <Select 
                value={editForm.role} 
                onValueChange={(value: 'admin' | 'cashier' | 'supervisor') => 
                  setEditForm({ ...editForm, role: value })
                }
                disabled={currentUser?.id === editingUser?.id}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cashier">كاشير</SelectItem>
                  <SelectItem value="supervisor">مشرف</SelectItem>
                  <SelectItem value="admin">مدير النظام</SelectItem>
                </SelectContent>
              </Select>
              {currentUser?.id === editingUser?.id && (
                <p className="text-xs text-muted-foreground">
                  لا يمكن تغيير صلاحية حسابك الحالي
                </p>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="changePassword"
                  checked={editForm.changePassword}
                  onChange={(e) => setEditForm({ ...editForm, changePassword: e.target.checked, newPassword: '' })}
                  className="rounded"
                />
                <Label htmlFor="changePassword" className="text-sm">
                  تغيير كلمة المرور
                </Label>
              </div>
              {editForm.changePassword && (
                <Input
                  type="password"
                  placeholder="كلمة المرور الجديدة"
                  value={editForm.newPassword}
                  onChange={(e) => setEditForm({ ...editForm, newPassword: e.target.value })}
                  className="text-right"
                />
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              <X className="w-4 h-4 ml-2" />
              إلغاء
            </Button>
            <Button onClick={handleSaveEdit}>
              <Save className="w-4 h-4 ml-2" />
              حفظ التغييرات
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AccountManagement;
