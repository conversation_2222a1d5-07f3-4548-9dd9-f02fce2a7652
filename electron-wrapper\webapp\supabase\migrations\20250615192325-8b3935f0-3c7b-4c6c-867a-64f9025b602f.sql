
-- إضافة جدول تذكيرات الدفع
CREATE TABLE IF NOT EXISTS public.payment_reminders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid REFERENCES public.customers(id) ON DELETE CASCADE,
  reference_type text NOT NULL, -- 'debt' أو 'installment'
  reference_id uuid NOT NULL, -- debt_id أو installment_id
  due_date date NOT NULL,
  amount numeric NOT NULL DEFAULT 0,
  status text NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'paid'
  reminder_sent_at timestamp with time zone,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_payment_reminders_customer_id ON public.payment_reminders(customer_id);
CREATE INDEX IF NOT EXISTS idx_payment_reminders_due_date ON public.payment_reminders(due_date);
CREATE INDEX IF NOT EXISTS idx_payment_reminders_status ON public.payment_reminders(status);

-- إ<PERSON><PERSON><PERSON><PERSON> trigger لتحديث updated_at
CREATE TRIGGER update_payment_reminders_updated_at 
  BEFORE UPDATE ON public.payment_reminders
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إضافة عمود total_debt للعملاء لحساب إجمالي الديون
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS total_debt numeric DEFAULT 0;

-- دالة لحساب إجمالي ديون العميل
CREATE OR REPLACE FUNCTION calculate_customer_total_debt(customer_uuid uuid)
RETURNS numeric AS $$
DECLARE
  total_debts numeric := 0;
  total_installments numeric := 0;
BEGIN
  -- حساب الديون المتبقية
  SELECT COALESCE(SUM(remaining_amount), 0) INTO total_debts
  FROM public.debts 
  WHERE customer_id = customer_uuid AND status = 'pending';
  
  -- حساب الأقساط المتبقية
  SELECT COALESCE(SUM(remaining_amount), 0) INTO total_installments
  FROM public.installments 
  WHERE customer_id = customer_uuid AND status = 'active';
  
  RETURN total_debts + total_installments;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث إجمالي ديون العميل
CREATE OR REPLACE FUNCTION update_customer_total_debt()
RETURNS trigger AS $$
BEGIN
  -- تحديث العميل القديم إذا كان موجوداً
  IF OLD.customer_id IS NOT NULL THEN
    UPDATE public.customers 
    SET total_debt = calculate_customer_total_debt(OLD.customer_id)
    WHERE id = OLD.customer_id;
  END IF;
  
  -- تحديث العميل الجديد إذا كان موجوداً
  IF NEW.customer_id IS NOT NULL THEN
    UPDATE public.customers 
    SET total_debt = calculate_customer_total_debt(NEW.customer_id)
    WHERE id = NEW.customer_id;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers لتحديث إجمالي الديون عند تغيير الديون أو الأقساط
CREATE TRIGGER update_customer_debt_on_debt_change
  AFTER INSERT OR UPDATE OR DELETE ON public.debts
  FOR EACH ROW EXECUTE FUNCTION update_customer_total_debt();

CREATE TRIGGER update_customer_debt_on_installment_change
  AFTER INSERT OR UPDATE OR DELETE ON public.installments
  FOR EACH ROW EXECUTE FUNCTION update_customer_total_debt();

-- تحديث إجمالي الديون للعملاء الحاليين
UPDATE public.customers 
SET total_debt = calculate_customer_total_debt(id);
