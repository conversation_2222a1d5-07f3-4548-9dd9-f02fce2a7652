
interface DraftData {
  type: 'sale' | 'product' | 'customer';
  data: any;
  timestamp: string;
  id: string;
}

interface CacheData {
  data: any;
  expiration: number;
}

export class StorageManager {
  private static instance: StorageManager;
  private readonly prefix = 'aridoo_';

  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  // Local Storage Methods
  setItem(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(this.prefix + key, serializedValue);
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  getItem<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(this.prefix + key);
      if (item === null) return defaultValue || null;
      return JSON.parse(item);
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return defaultValue || null;
    }
  }

  removeItem(key: string): void {
    try {
      localStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  }

  // Type guard for DraftData
  private isDraftData(data: any): data is DraftData {
    return data && 
           typeof data === 'object' && 
           typeof data.type === 'string' && 
           typeof data.timestamp === 'string' && 
           typeof data.id === 'string' &&
           data.data !== undefined;
  }

  // Draft Management
  saveDraft(type: 'sale' | 'product' | 'customer', data: any): void {
    const key = `draft_${type}_${Date.now()}`;
    const draftData: DraftData = {
      type,
      data,
      timestamp: new Date().toISOString(),
      id: key
    };
    this.setItem(key, draftData);
  }

  getDrafts(type?: 'sale' | 'product' | 'customer'): DraftData[] {
    try {
      const drafts: DraftData[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(this.prefix + 'draft_')) {
          const draft = this.getItem(key.replace(this.prefix, ''));
          if (this.isDraftData(draft) && (!type || draft.type === type)) {
            drafts.push(draft);
          }
        }
      }
      return drafts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    } catch (error) {
      console.error('Error getting drafts:', error);
      return [];
    }
  }

  deleteDraft(draftId: string): void {
    this.removeItem(draftId);
  }

  // Cache Management
  setCache(key: string, data: any, expirationMinutes: number = 30): void {
    const cacheData: CacheData = {
      data,
      expiration: Date.now() + (expirationMinutes * 60 * 1000)
    };
    this.setItem(`cache_${key}`, cacheData);
  }

  getCache<T>(key: string): T | null {
    const cached = this.getItem<CacheData>(`cache_${key}`);
    if (!cached || typeof cached !== 'object' || !cached.data || typeof cached.expiration !== 'number') {
      return null;
    }
    
    if (Date.now() > cached.expiration) {
      this.removeItem(`cache_${key}`);
      return null;
    }
    
    return cached.data;
  }

  clearCache(): void {
    try {
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(this.prefix + 'cache_')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  // Settings Management
  saveSettings(settings: any): void {
    this.setItem('settings', settings);
  }

  getSettings(): any {
    return this.getItem('settings', {
      language: 'ar',
      currency: 'IQD',
      storeName: 'أريدوو',
      theme: 'light'
    });
  }

  // Backup Management
  exportData(): string {
    try {
      const data: any = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(this.prefix)) {
          data[key] = localStorage.getItem(key);
        }
      }
      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('Error exporting data:', error);
      throw new Error('فشل في تصدير البيانات');
    }
  }

  importData(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);
      Object.entries(data).forEach(([key, value]) => {
        if (key.startsWith(this.prefix)) {
          localStorage.setItem(key, value as string);
        }
      });
    } catch (error) {
      console.error('Error importing data:', error);
      throw new Error('فشل في استيراد البيانات - تأكد من صحة الملف');
    }
  }

  // Cleanup old data
  cleanup(): void {
    try {
      const now = Date.now();
      const keysToRemove = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(this.prefix)) {
          // Remove old drafts (older than 7 days)
          if (key.includes('draft_')) {
            const draft = this.getItem(key.replace(this.prefix, ''));
            if (this.isDraftData(draft)) {
              const draftTime = new Date(draft.timestamp).getTime();
              if (now - draftTime > 7 * 24 * 60 * 60 * 1000) {
                keysToRemove.push(key);
              }
            }
          }
        }
      }
      
      keysToRemove.forEach(key => localStorage.removeItem(key));
      console.log(`Cleaned up ${keysToRemove.length} old items`);
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }
}

export const storage = StorageManager.getInstance();
