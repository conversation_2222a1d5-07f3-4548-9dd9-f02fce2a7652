import React from 'react';
import { useSettings } from '@/contexts/SettingsContext';

interface ReceiptItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  total: number;
}

interface ReceiptPreviewProps {
  items: ReceiptItem[];
  total: number;
  template?: 'classic' | 'modern' | 'minimal' | 'colorful';
  showLogo?: boolean;
  showQR?: boolean;
  customerInfo?: {
    name?: string;
    phone?: string;
  };
  saleData?: {
    id?: string;
    timestamp?: string;
    subtotal?: number;
    discount?: number;
    tax?: number;
    paymentMethod?: string;
  };
}

const ReceiptPreview = ({ 
  items, 
  total, 
  template = 'classic', 
  showLogo = true, 
  showQR = true,
  customerInfo,
  saleData
}: ReceiptPreviewProps) => {
  const { settings, formatCurrency } = useSettings();



  // إنشاء أنماط CSS مباشرة لتطابق تصميم الطباعة
  const getInlineStyles = () => {
    const baseStyles = {
      container: {
        maxWidth: '500px',
        margin: '0 auto',
        backgroundColor: 'white',
        border: '2px solid #333',
        fontFamily: 'Arial, Tahoma, sans-serif',
        fontSize: '14px',
        lineHeight: '1.4',
        direction: 'rtl' as const,
        textAlign: 'right' as const
      },
      header: {
        padding: '20px',
        textAlign: 'center' as const,
        borderBottom: '2px solid #333'
      },
      storeName: {
        fontSize: '28px',
        fontWeight: 'bold',
        marginBottom: '10px'
      },
      receiptNumber: {
        fontSize: '18px',
        fontWeight: 'bold',
        marginBottom: '8px'
      },
      body: {
        padding: '20px'
      },
      customerInfo: {
        backgroundColor: '#f5f5f5',
        padding: '15px',
        marginBottom: '20px',
        border: '1px solid #ddd'
      },
      table: {
        width: '100%',
        borderCollapse: 'collapse' as const,
        marginBottom: '20px',
        border: '2px solid #333'
      },
      th: {
        padding: '10px 8px',
        textAlign: 'right' as const,
        borderBottom: '2px solid #333',
        backgroundColor: '#f0f0f0',
        fontWeight: 'bold',
        fontSize: '14px'
      },
      td: {
        padding: '10px 8px',
        textAlign: 'right' as const,
        borderBottom: '1px solid #333',
        fontSize: '14px'
      },
      totals: {
        borderTop: '3px solid #333',
        paddingTop: '15px',
        marginTop: '20px'
      },
      totalRow: {
        display: 'flex',
        justifyContent: 'space-between',
        marginBottom: '8px',
        fontSize: '14px',
        padding: '2px 0'
      },
      finalTotal: {
        fontSize: '18px',
        fontWeight: 'bold',
        borderTop: '2px solid #333',
        paddingTop: '10px',
        marginTop: '10px'
      },
      footer: {
        textAlign: 'center' as const,
        marginTop: '20px',
        paddingTop: '15px',
        borderTop: '2px solid #333',
        fontSize: '12px'
      }
    };

    // تطبيق أنماط التصميم المختار
    switch (template) {
      case 'modern':
        return {
          ...baseStyles,
          header: {
            ...baseStyles.header,
            background: '#4f46e5',
            color: 'white',
            borderBottom: '3px solid #4f46e5'
          },
          totals: {
            ...baseStyles.totals,
            borderTop: '3px solid #4f46e5'
          },
          finalTotal: {
            ...baseStyles.finalTotal,
            color: '#4f46e5',
            borderTop: '2px solid #4f46e5'
          }
        };
      case 'minimal':
        return {
          ...baseStyles,
          container: {
            ...baseStyles.container,
            border: '1px solid #666'
          },
          header: {
            ...baseStyles.header,
            background: 'white',
            borderBottom: '3px solid #000'
          },
          th: {
            ...baseStyles.th,
            background: 'white',
            borderBottom: '3px solid #000'
          }
        };
      case 'colorful':
        return {
          ...baseStyles,
          header: {
            ...baseStyles.header,
            background: '#ec4899',
            color: 'white',
            borderBottom: '3px solid #ec4899'
          },
          customerInfo: {
            ...baseStyles.customerInfo,
            background: '#fce7f3',
            border: '2px solid #ec4899'
          },
          totals: {
            ...baseStyles.totals,
            borderTop: '3px solid #ec4899'
          },
          finalTotal: {
            ...baseStyles.finalTotal,
            color: '#ec4899',
            borderTop: '2px solid #ec4899'
          }
        };
      default: // classic
        return baseStyles;
    }
  };

  const inlineStyles = getInlineStyles();

  return (
    <div style={inlineStyles.container}>
      <div style={inlineStyles.header}>
        {showLogo && (
          <div style={{ width: '64px', height: '64px', backgroundColor: '#e5e7eb', borderRadius: '50%', margin: '0 auto 12px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <span style={{ color: '#6b7280', fontSize: '12px' }}>شعار</span>
          </div>
        )}
        <h2 style={inlineStyles.storeName}>{settings.storeName || 'متجر إرادو'}</h2>
        <p style={{ fontSize: '14px', opacity: 0.9, margin: '5px 0' }}>{settings.storeAddress}</p>
        <p style={{ fontSize: '14px', opacity: 0.9, margin: '5px 0' }}>{settings.storePhone}</p>
        {settings.taxNumber && (
          <p style={{ fontSize: '12px', opacity: 0.8, margin: '5px 0' }}>الرقم الضريبي: {settings.taxNumber}</p>
        )}
      </div>

      <div style={inlineStyles.body}>
        <div style={{ marginBottom: '16px', textAlign: 'center' }}>
          <h3 style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: '8px' }}>فاتورة بيع</h3>
          <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
            <span>التاريخ: {saleData?.timestamp ?
              new Date(saleData.timestamp).toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                calendar: 'gregory'
              }) :
              new Date().toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                calendar: 'gregory'
              })
            }</span>
            <span>الوقت: {saleData?.timestamp ?
              new Date(saleData.timestamp).toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              }) :
              new Date().toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              })
            }</span>
          </div>
          <div style={{ fontSize: '12px', marginTop: '4px' }}>
            <span style={inlineStyles.receiptNumber}>رقم الفاتورة: {settings.receiptPrefix || 'INV'}-{saleData?.id ? saleData.id.slice(-6) : Date.now().toString().slice(-6)}</span>
          </div>
        </div>

        {customerInfo && (customerInfo.name || customerInfo.phone) && (
          <div style={inlineStyles.customerInfo}>
            <h4 style={{ fontWeight: 'bold', marginBottom: '8px' }}>معلومات العميل</h4>
            {customerInfo.name && <div style={{ margin: '4px 0' }}>العميل: {customerInfo.name}</div>}
            {customerInfo.phone && <div style={{ margin: '4px 0' }}>الهاتف: {customerInfo.phone}</div>}
          </div>
        )}

        <div style={{ marginBottom: '16px' }}>
          <table style={inlineStyles.table}>
            <thead>
              <tr>
                <th style={inlineStyles.th}>الصنف</th>
                <th style={inlineStyles.th}>الكمية</th>
                <th style={inlineStyles.th}>السعر</th>
                <th style={inlineStyles.th}>المجموع</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item) => (
                <tr key={item.id}>
                  <td style={inlineStyles.td}>{item.name}</td>
                  <td style={{...inlineStyles.td, textAlign: 'center'}}>{item.quantity}</td>
                  <td style={{...inlineStyles.td, textAlign: 'center'}}>{formatCurrency(item.price)}</td>
                  <td style={{...inlineStyles.td, textAlign: 'left'}}>{formatCurrency(item.total)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div style={inlineStyles.totals}>
          {saleData && (
            <div style={{ marginBottom: '16px' }}>
              <div style={inlineStyles.totalRow}>
                <span>المجموع الفرعي:</span>
                <span>{formatCurrency(saleData.subtotal || total)}</span>
              </div>
              {saleData.discount && saleData.discount > 0 && (
                <div style={{...inlineStyles.totalRow, color: '#dc2626'}}>
                  <span>الخصم ({saleData.discount}%):</span>
                  <span>-{formatCurrency(((saleData.subtotal || total) * saleData.discount) / 100)}</span>
                </div>
              )}
              {saleData.tax && saleData.tax > 0 && (
                <div style={{...inlineStyles.totalRow, color: '#2563eb'}}>
                  <span>الضريبة ({saleData.tax}%):</span>
                  <span>+{formatCurrency((((saleData.subtotal || total) - ((saleData.subtotal || total) * (saleData.discount || 0)) / 100) * saleData.tax) / 100)}</span>
                </div>
              )}
            </div>
          )}
          <div style={{...inlineStyles.totalRow, ...inlineStyles.finalTotal}}>
            <span>المجموع النهائي:</span>
            <span>{formatCurrency(total)}</span>
          </div>
          {saleData?.paymentMethod && (
            <div style={{...inlineStyles.totalRow, fontSize: '14px', marginTop: '8px'}}>
              <span>طريقة الدفع:</span>
              <span>{saleData.paymentMethod}</span>
            </div>
          )}
          {settings.taxEnabled && settings.taxRate > 0 && !saleData && (
            <div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.75 }}>
              شامل {settings.taxName} ({settings.taxRate}%)
            </div>
          )}
        </div>

        {showQR && (
          <div style={{ textAlign: 'center', marginTop: '16px' }}>
            <div style={{ width: '64px', height: '64px', backgroundColor: '#e5e7eb', margin: '0 auto', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>
              QR
            </div>
            <p style={{ fontSize: '12px', marginTop: '4px', opacity: 0.75 }}>امسح للتحقق</p>
          </div>
        )}

        <div style={inlineStyles.footer}>
          {settings.receiptFooter && (
            <p style={{ marginBottom: '8px' }}>{settings.receiptFooter}</p>
          )}
          <p>شكراً لزيارتكم</p>
          <p style={{ fontSize: '10px', marginTop: '8px' }}>
            {settings.storeName} - {settings.storeAddress || ''}
          </p>
          {settings.storePhone && (
            <p style={{ fontSize: '10px' }}>هاتف: {settings.storePhone}</p>
          )}
          <p style={{ fontSize: '10px', marginTop: '4px' }}>
            طُبع في: {new Date().toLocaleString('ar-SA', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
              calendar: 'gregory'
            })}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ReceiptPreview;
