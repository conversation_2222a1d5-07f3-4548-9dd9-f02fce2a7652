@echo off
title Iradoo POS Desktop - إدارة التطبيق
color 0A

:menu
cls
echo ========================================
echo    🏪 Iradoo POS Desktop Manager
echo ========================================
echo.
echo اختر العملية المطلوبة:
echo.
echo [1] فحص النظام والمتطلبات
echo [2] الإعداد الأولي (تثبيت التبعيات)
echo [3] نسخ ملفات تطبيق الويب
echo [4] تشغيل التطبيق للاختبار
echo [5] بناء التطبيق للتوزيع
echo [6] إنشاء أيقونة تجريبية
echo [7] فتح مجلد المشروع
echo [8] فتح مجلد التوزيع
echo [9] عرض معلومات المشروع
echo [0] خروج
echo.
echo ========================================
set /p choice=أدخل اختيارك (0-9): 

if "%choice%"=="1" goto check_system
if "%choice%"=="2" goto setup
if "%choice%"=="3" goto copy_webapp
if "%choice%"=="4" goto start_app
if "%choice%"=="5" goto build_app
if "%choice%"=="6" goto create_icon
if "%choice%"=="7" goto open_project
if "%choice%"=="8" goto open_dist
if "%choice%"=="9" goto show_info
if "%choice%"=="0" goto exit
goto invalid_choice

:check_system
cls
echo تشغيل فحص النظام...
call check-system.bat
goto pause_and_menu

:setup
cls
echo تشغيل الإعداد الأولي...
call setup.bat
goto pause_and_menu

:copy_webapp
cls
echo تشغيل نسخ ملفات التطبيق...
call copy-webapp.bat
goto pause_and_menu

:start_app
cls
echo تشغيل التطبيق...
call start.bat
goto pause_and_menu

:build_app
cls
echo بناء التطبيق للتوزيع...
call build.bat
goto pause_and_menu

:create_icon
cls
echo فتح أداة إنشاء الأيقونة...
if exist "create-icon.html" (
    start create-icon.html
    echo تم فتح أداة إنشاء الأيقونة في المتصفح
) else (
    echo خطأ: ملف create-icon.html غير موجود!
)
goto pause_and_menu

:open_project
cls
echo فتح مجلد المشروع...
start explorer .
echo تم فتح مجلد المشروع
goto pause_and_menu

:open_dist
cls
if exist "dist" (
    echo فتح مجلد التوزيع...
    start explorer dist
    echo تم فتح مجلد التوزيع
) else (
    echo مجلد التوزيع غير موجود!
    echo يرجى بناء التطبيق أولاً (الخيار 5)
)
goto pause_and_menu

:show_info
cls
echo ========================================
echo    معلومات المشروع
echo ========================================
echo.
echo اسم المشروع: Iradoo POS Desktop
echo الإصدار: 1.0.0
echo النوع: Electron Desktop App
echo.
echo الملفات الرئيسية:
if exist "package.json" (echo ✅ package.json) else (echo ❌ package.json)
if exist "main.js" (echo ✅ main.js) else (echo ❌ main.js)
if exist "webapp\index.html" (echo ✅ webapp\index.html) else (echo ❌ webapp\index.html)
if exist "assets\icon.png" (echo ✅ assets\icon.png) else (echo ❌ assets\icon.png)
echo.
echo حالة التبعيات:
if exist "node_modules" (echo ✅ مثبتة) else (echo ❌ غير مثبتة)
echo.
echo حالة البناء:
if exist "dist" (echo ✅ تم البناء) else (echo ❌ لم يتم البناء)
echo.
goto pause_and_menu

:invalid_choice
cls
echo خيار غير صحيح! يرجى اختيار رقم من 0 إلى 9
goto pause_and_menu

:pause_and_menu
echo.
echo اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto menu

:exit
cls
echo شكراً لاستخدام Iradoo POS Desktop Manager!
echo.
timeout /t 2 >nul
exit
