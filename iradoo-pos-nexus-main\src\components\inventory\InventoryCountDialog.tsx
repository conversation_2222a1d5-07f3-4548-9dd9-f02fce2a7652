import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { updateProduct, type Product } from '@/utils/database';
import { ClipboardCheck, Search, AlertTriangle, CheckCircle, Package } from 'lucide-react';

interface CountItem {
  product: Product;
  systemCount: number;
  physicalCount: string;
  difference: number;
}

interface InventoryCountDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  products: Product[];
  onCountComplete: () => void;
}

const InventoryCountDialog: React.FC<InventoryCountDialogProps> = ({
  open,
  onOpenChange,
  products,
  onCountComplete
}) => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [countItems, setCountItems] = useState<CountItem[]>([]);
  const [loading, setLoading] = useState(false);

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.barcode && product.barcode.includes(searchTerm))
  );

  const addToCount = (product: Product) => {
    if (countItems.find(item => item.product.id === product.id)) return;

    const newItem: CountItem = {
      product,
      systemCount: product.stock,
      physicalCount: '',
      difference: 0
    };

    setCountItems([...countItems, newItem]);
  };

  const updatePhysicalCount = (productId: string, count: string) => {
    setCountItems(countItems.map(item => {
      if (item.product.id === productId) {
        const physicalCount = parseInt(count) || 0;
        return {
          ...item,
          physicalCount: count,
          difference: physicalCount - item.systemCount
        };
      }
      return item;
    }));
  };

  const removeFromCount = (productId: string) => {
    setCountItems(countItems.filter(item => item.product.id !== productId));
  };

  const getDiscrepancySummary = () => {
    const totalItems = countItems.length;
    const itemsWithDifferences = countItems.filter(item => item.difference !== 0).length;
    const positiveAdjustments = countItems.filter(item => item.difference > 0).length;
    const negativeAdjustments = countItems.filter(item => item.difference < 0).length;

    return {
      totalItems,
      itemsWithDifferences,
      positiveAdjustments,
      negativeAdjustments
    };
  };

  const handleSubmit = async () => {
    const itemsToUpdate = countItems.filter(item => 
      item.physicalCount !== '' && item.difference !== 0
    );

    if (itemsToUpdate.length === 0) {
      toast({
        title: "لا توجد تعديلات",
        description: "لا توجد اختلافات في الجرد",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      for (const item of itemsToUpdate) {
        const newStock = parseInt(item.physicalCount);
        await updateProduct(item.product.id, { stock: newStock });
      }

      toast({
        title: "تم تحديث الجرد",
        description: `تم تحديث ${itemsToUpdate.length} منتج بناءً على الجرد الفعلي`,
      });

      onCountComplete();
      onOpenChange(false);
      setCountItems([]);
      setSearchTerm('');
    } catch (error) {
      console.error('Error updating inventory count:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تحديث الجرد",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const summary = getDiscrepancySummary();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ClipboardCheck className="w-5 h-5" />
            جرد المخزون
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Search Products */}
          <Card>
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="البحث في المنتجات أو الباركود..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>

                {searchTerm && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-h-40 overflow-y-auto">
                    {filteredProducts.slice(0, 9).map((product) => (
                      <Button
                        key={product.id}
                        variant="outline"
                        onClick={() => addToCount(product)}
                        disabled={countItems.some(item => item.product.id === product.id)}
                        className="justify-start h-auto p-3"
                      >
                        <div className="text-left">
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-gray-500">مخزون: {product.stock}</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Count Items */}
          {countItems.length > 0 && (
            <>
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-4">عناصر الجرد</h3>
                  <div className="space-y-3">
                    {countItems.map((item) => (
                      <div key={item.product.id} className="grid grid-cols-1 md:grid-cols-6 gap-4 items-center bg-gray-50 p-4 rounded-lg">
                        <div className="md:col-span-2">
                          <h4 className="font-medium">{item.product.name}</h4>
                          {item.product.barcode && (
                            <p className="text-xs text-gray-500 font-mono">{item.product.barcode}</p>
                          )}
                        </div>

                        <div className="text-center">
                          <Label className="text-xs text-gray-600">المخزون النظام</Label>
                          <p className="font-bold">{item.systemCount}</p>
                        </div>

                        <div>
                          <Label className="text-xs text-gray-600">الجرد الفعلي</Label>
                          <Input
                            type="number"
                            min="0"
                            value={item.physicalCount}
                            onChange={(e) => updatePhysicalCount(item.product.id, e.target.value)}
                            placeholder="0"
                            className="text-center"
                          />
                        </div>

                        <div className="text-center">
                          <Label className="text-xs text-gray-600">الفرق</Label>
                          <div className="flex items-center justify-center gap-1">
                            {item.difference !== 0 && item.physicalCount && (
                              <>
                                {item.difference > 0 ? (
                                  <AlertTriangle className="w-4 h-4 text-green-600" />
                                ) : (
                                  <AlertTriangle className="w-4 h-4 text-red-600" />
                                )}
                                <Badge variant={item.difference > 0 ? "default" : "destructive"}>
                                  {item.difference > 0 ? '+' : ''}{item.difference}
                                </Badge>
                              </>
                            )}
                            {item.difference === 0 && item.physicalCount && (
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            )}
                          </div>
                        </div>

                        <div>
                          <Button
                            onClick={() => removeFromCount(item.product.id)}
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            حذف
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Summary */}
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <Package className="w-5 h-5" />
                    ملخص الجرد
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{summary.totalItems}</div>
                      <div className="text-sm text-gray-600">إجمالي العناصر</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{summary.itemsWithDifferences}</div>
                      <div className="text-sm text-gray-600">عناصر بها اختلافات</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{summary.positiveAdjustments}</div>
                      <div className="text-sm text-gray-600">زيادة في المخزون</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{summary.negativeAdjustments}</div>
                      <div className="text-sm text-gray-600">نقص في المخزون</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Submit Button */}
              <div className="flex gap-4">
                <Button 
                  onClick={handleSubmit}
                  disabled={loading || summary.itemsWithDifferences === 0}
                  className="flex-1 bg-blue-600 hover:bg-blue-700"
                >
                  {loading ? "جاري التحديث..." : "تطبيق الجرد"}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => onOpenChange(false)}
                  disabled={loading}
                >
                  إلغاء
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryCountDialog;
