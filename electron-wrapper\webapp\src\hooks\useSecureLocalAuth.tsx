
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface SecureUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'cashier' | 'supervisor';
  active: boolean;
  lastLogin?: string;
}

interface SecureSession {
  user: SecureUser;
  sessionId: string;
  loginTime: string;
  isActive: boolean;
  expiresAt: string;
}

export const useSecureLocalAuth = () => {
  const [user, setUser] = useState<SecureUser | null>(null);
  const [session, setSession] = useState<SecureSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = () => {
    const sessionData = localStorage.getItem('aridoo-secure-session');
    if (sessionData) {
      try {
        const parsedSession = JSON.parse(sessionData);
        
        // Check if session is expired
        if (new Date(parsedSession.expiresAt) > new Date()) {
          setSession(parsedSession);
          setUser(parsedSession.user);
        } else {
          // Session expired, clear it
          localStorage.removeItem('aridoo-secure-session');
        }
      } catch (error) {
        console.error('Session validation error:', error);
        localStorage.removeItem('aridoo-secure-session');
      }
    }
    setIsLoading(false);
  };

  const secureLogin = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // Use Supabase Auth for secure authentication
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        // Get user profile from secure database
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (profile) {
          // Ensure role is one of the allowed values
          const validRole = ['admin', 'cashier', 'supervisor'].includes(profile.role) 
            ? profile.role as 'admin' | 'cashier' | 'supervisor'
            : 'cashier';

          const secureUser: SecureUser = {
            id: profile.id,
            name: profile.name,
            email: profile.email,
            role: validRole,
            active: true,
            lastLogin: new Date().toISOString()
          };

          const newSession: SecureSession = {
            user: secureUser,
            sessionId: crypto.randomUUID(),
            loginTime: new Date().toISOString(),
            isActive: true,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
          };

          localStorage.setItem('aridoo-secure-session', JSON.stringify(newSession));
          setUser(secureUser);
          setSession(newSession);

          return { success: true };
        }
      }

      return { success: false, error: 'User profile not found' };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed' };
    }
  };

  const logout = async () => {
    await supabase.auth.signOut();
    localStorage.removeItem('aridoo-secure-session');
    setUser(null);
    setSession(null);
  };

  const isLoggedIn = (): boolean => {
    return !!session && !!user && new Date(session.expiresAt) > new Date();
  };

  const hasRole = (role: 'admin' | 'cashier' | 'supervisor'): boolean => {
    return user?.role === role || false;
  };

  const isAdmin = (): boolean => {
    return hasRole('admin');
  };

  return {
    user,
    session,
    isLoading,
    isLoggedIn: isLoggedIn(),
    isAdmin: isAdmin(),
    canManageUsers: isAdmin(),
    canDeleteProducts: isAdmin(),
    canModifyCategories: isAdmin(),
    secureLogin,
    logout,
    hasRole
  };
};
