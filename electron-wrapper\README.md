# 🏪 Iradoo POS Desktop Wrapper

تطبيق سطح مكتب لتغليف تطبيق الويب Iradoo POS باستخدام Electron.js

## ✨ المميزات

- ✅ متوافق مع Windows 7, 8, 10, 11
- ✅ يعمل بدون إنترنت (للملفات المحلية)
- ✅ ملف تثبيت EXE + نسخة محمولة
- ✅ أيقونة قابلة للتخصيص
- ✅ قائمة عربية
- ✅ أمان محسن
- ✅ سهولة التحديث

## 📁 هيكل المشروع

```
electron-wrapper/
├── main.js              # الملف الرئيسي لـ Electron
├── preload.js           # ملف الأمان والـ APIs
├── package.json         # إعدادات المشروع والبناء
├── config.json          # إعدادات التطبيق
├── setup.bat           # ملف الإعداد الأولي
├── build.bat           # ملف البناء السريع
├── assets/             # الأيقونات والأصول
│   ├── icon.ico        # أيقونة Windows (ضعها هنا)
│   └── icon.png        # أيقونة عامة (ضعها هنا)
└── webapp/             # ملفات تطبيق الويب
    ├── index.html      # الملف الرئيسي
    ├── css/           # ملفات التنسيق
    ├── js/            # ملفات JavaScript
    └── ...            # باقي ملفات التطبيق
```

## 🚀 البدء السريع

### 1. الإعداد الأولي

```bash
# تشغيل ملف الإعداد (Windows)
setup.bat

# أو يدوياً
npm install
```

### 2. إضافة ملفات تطبيقك

**الطريقة الأولى: ملفات محلية**
1. ابني تطبيق الويب الخاص بك: `npm run build`
2. انسخ جميع ملفات البناء إلى مجلد `webapp/`
3. تأكد من وجود `index.html` في `webapp/`

**الطريقة الثانية: رابط أونلاين**
1. عدّل `config.json`
2. غيّر `"mode": "local"` إلى `"mode": "url"`
3. ضع رابط تطبيقك في `"url"`

### 3. إضافة الأيقونة

1. ضع ملف `icon.png` (512x512) في مجلد `assets/`
2. ضع ملف `icon.ico` في مجلد `assets/`

### 4. اختبار التطبيق

```bash
npm start
```

### 5. بناء التطبيق للتوزيع

```bash
# استخدام ملف البناء السريع
build.bat

# أو يدوياً
npm run build
```

## ⚙️ التخصيص

### تغيير اسم التطبيق والمعلومات

عدّل `package.json`:

```json
{
  "name": "your-app-name",
  "productName": "اسم تطبيقك",
  "description": "وصف تطبيقك",
  "author": "اسمك",
  "build": {
    "appId": "com.yourcompany.yourapp",
    "productName": "اسم تطبيقك"
  }
}
```

### تغيير إعدادات النافذة

عدّل `config.json`:

```json
{
  "windowSettings": {
    "width": 1400,
    "height": 900,
    "minWidth": 1000,
    "minHeight": 700
  }
}
```

### تخصيص القائمة

عدّل دالة `createMenu()` في `main.js`

## 📦 أنواع البناء المتاحة

بعد تشغيل `npm run build`، ستجد في مجلد `dist`:

1. **NSIS Installer** - ملف تثبيت كامل
2. **Portable** - نسخة محمولة لا تحتاج تثبيت
3. **Unpacked** - ملفات التطبيق غير مضغوطة

## 🔄 تحديث التطبيق

### تحديث ملفات الويب فقط:
1. ابني تطبيق الويب المحدث
2. استبدل محتويات مجلد `webapp/`
3. شغّل `build.bat`

### تحديث إعدادات Electron:
1. عدّل الملفات المطلوبة
2. شغّل `build.bat`

## 🛠️ استكشاف الأخطاء

### التطبيق لا يبدأ:
- تأكد من وجود `index.html` في `webapp/`
- تحقق من المسارات في ملفات HTML/CSS/JS
- استخدم مسارات نسبية فقط (./css/style.css)

### الأيقونة لا تظهر:
- تأكد من وجود `icon.ico` و `icon.png` في `assets/`
- تأكد من الأحجام الصحيحة (512x512 للـ PNG)

### خطأ في البناء:
- تأكد من تثبيت Node.js
- شغّل `npm install` مرة أخرى
- تحقق من وجود مساحة كافية على القرص

## 📋 متطلبات النظام

### للتطوير:
- Node.js 16+ 
- npm أو yarn
- Windows 7+ (للاختبار)

### للمستخدم النهائي:
- Windows 7/8/10/11
- 100MB مساحة فارغة
- لا يحتاج إنترنت (للملفات المحلية)

## 🔒 الأمان

- تم تعطيل Node.js integration
- Context isolation مفعل
- منع التنقل للمواقع الخارجية
- حماية من XSS attacks

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `README.md` في مجلد `webapp/`
2. تأكد من اتباع التعليمات بدقة
3. تحقق من console في Developer Tools (F12)

## 📄 الترخيص

MIT License - يمكنك استخدام وتعديل الكود بحرية.
