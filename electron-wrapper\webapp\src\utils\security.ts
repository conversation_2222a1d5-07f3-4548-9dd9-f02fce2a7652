
// Re-export enhanced functions for backward compatibility
export { 
  validateEmailEnhanced as validateEmail,
  validatePasswordEnhanced as validatePassword,
  sanitizeInputEnhanced as sanitizeInput,
  isAccountLockedEnhanced as isAccountLocked,
  logFailedLoginAttemptEnhanced as logFailedLoginAttempt,
  getCurrentUserRoleEnhanced as getCurrentUserRole,
  hasRoleEnhanced as hasRole,
  isAdminEnhanced as isAdmin,
  getSecurityAuditLogsEnhanced as getSecurityAuditLogs,
  getUserSessionsEnhanced as getUserSessions,
  terminateSessionEnhanced as terminateSession,
  validatePhoneNumber,
  validateBarcode,
  createUserSession,
  updateSessionActivity
} from './enhancedSecurity';

// Legacy functions for backward compatibility (deprecated)
export const validateEmailLegacy = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePasswordLegacy = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const sanitizeInputLegacy = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};
