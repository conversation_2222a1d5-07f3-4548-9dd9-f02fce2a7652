
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Shield, Key, CheckCircle, XCircle, Info, Copy, RefreshCw } from 'lucide-react';
import { licenseManager, LicenseInfo } from '@/utils/licenseManager';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

const LicenseSettings = () => {
  const { toast } = useToast();
  const [licenseKey, setLicenseKey] = useState('');
  const [currentLicense, setCurrentLicense] = useState<LicenseInfo | null>(null);
  const [deviceInfo, setDeviceInfo] = useState({ macAddress: '', deviceName: '' });
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    loadCurrentLicense();
    loadDeviceInfo();
  }, []);

  const loadCurrentLicense = () => {
    const license = licenseManager.getCurrentLicenseStatus();
    setCurrentLicense(license);
  };

  const loadDeviceInfo = () => {
    const info = licenseManager.getCurrentDeviceInfo();
    setDeviceInfo(info);
  };

  const handleActivateLicense = async () => {
    if (!licenseKey.trim()) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال مفتاح الترخيص',
        variant: 'destructive',
      });
      return;
    }

    setIsValidating(true);
    
    try {
      const success = licenseManager.saveLicense(licenseKey.trim());
      
      if (success) {
        toast({
          title: 'تم تفعيل الترخيص',
          description: 'تم تفعيل مفتاح الترخيص بنجاح',
        });
        setLicenseKey('');
        loadCurrentLicense();
      } else {
        const validation = licenseManager.validateLicense(licenseKey.trim());
        toast({
          title: 'فشل التفعيل',
          description: validation.error || 'مفتاح الترخيص غير صالح',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء تفعيل الترخيص',
        variant: 'destructive',
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleRemoveLicense = () => {
    licenseManager.removeLicense();
    setCurrentLicense(null);
    toast({
      title: 'تم حذف الترخيص',
      description: 'تم حذف مفتاح الترخيص المحفوظ',
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'تم النسخ',
      description: 'تم نسخ النص إلى الحافظة',
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-IQ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      
      {/* حالة الترخيص الحالي */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            حالة الترخيص
          </CardTitle>
          <CardDescription>
            معلومات مفتاح الترخيص الحالي
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {currentLicense ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                {currentLicense.isValid ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
                <Badge variant={currentLicense.isValid ? 'default' : 'destructive'}>
                  {currentLicense.isValid ? 'مفعل' : 'غير صالح'}
                </Badge>
              </div>

              {currentLicense.isValid && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">اسم العميل</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {currentLicense.data.customerName}
                    </p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">الرقم التسلسلي</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {currentLicense.data.serialNumber}
                    </p>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">تاريخ الإصدار</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {formatDate(currentLicense.data.issuedDate)}
                    </p>
                  </div>
                  
                  {currentLicense.data.expirationDate && (
                    <div>
                      <Label className="text-sm font-medium">تاريخ الانتهاء</Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {formatDate(currentLicense.data.expirationDate)}
                        {currentLicense.daysRemaining && (
                          <span className="text-orange-600 mr-2">
                            ({currentLicense.daysRemaining} يوم متبقي)
                          </span>
                        )}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {!currentLicense.isValid && currentLicense.error && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    {currentLicense.error}
                  </AlertDescription>
                </Alert>
              )}

              <Button 
                variant="outline" 
                onClick={handleRemoveLicense}
                className="w-full"
              >
                حذف الترخيص
              </Button>
            </div>
          ) : (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                لا يوجد ترخيص مفعل حالياً. يرجى إدخال مفتاح الترخيص أدناه.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* تفعيل ترخيص جديد */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="w-5 h-5" />
            تفعيل الترخيص
          </CardTitle>
          <CardDescription>
            أدخل مفتاح الترخيص لتفعيل النظام
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="license-key">مفتاح الترخيص</Label>
            <Textarea
              id="license-key"
              placeholder="ARIDOO-..."
              value={licenseKey}
              onChange={(e) => setLicenseKey(e.target.value)}
              className="min-h-[100px] font-mono text-sm"
            />
          </div>
          
          <Button 
            onClick={handleActivateLicense}
            disabled={isValidating}
            className="w-full"
          >
            {isValidating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                جاري التحقق...
              </>
            ) : (
              <>
                <Shield className="w-4 h-4 mr-2" />
                تفعيل الترخيص
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* معلومات الجهاز */}
      <Card>
        <CardHeader>
          <CardTitle>معلومات الجهاز</CardTitle>
          <CardDescription>
            معلومات الجهاز المطلوبة لإنشاء مفتاح الترخيص
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>معرف الجهاز (Device ID)</Label>
            <div className="flex gap-2">
              <Input
                value={deviceInfo.macAddress}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(deviceInfo.macAddress)}
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>اسم الجهاز</Label>
            <div className="flex gap-2">
              <Input
                value={deviceInfo.deviceName}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(deviceInfo.deviceName)}
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              قم بإرسال هذه المعلومات للحصول على مفتاح ترخيص مخصص لهذا الجهاز.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};

export default LicenseSettings;
