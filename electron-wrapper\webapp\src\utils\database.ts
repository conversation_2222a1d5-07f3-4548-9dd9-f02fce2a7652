import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://kcqbeoogqvpbuhstcspw.supabase.co';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtjcWJlb29ncXZwYnVoc3Rjc3B3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MzQxMTUsImV4cCI6MjA2NTUxMDExNX0.xGuEfrrc2z2m5qbZANMVdwMP5CBy6PS-ncQZ6zumqR8';
const supabase = createClient(supabaseUrl, supabaseKey);

export interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  notes?: string;
  total_purchases: number;
  total_debt: number;
  points: number;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  cost_price: number;
  wholesale_price: number;
  stock: number;
  min_stock: number;
  barcode?: string;
  image?: string;
  image_url?: string;
  category_id?: string;
  category?: Category;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  customer_id: string;
  total_amount: number;
  status: 'pending' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface Debt {
  id: string;
  customer_id: string;
  total_amount: number;
  remaining_amount: number;
  status: 'pending' | 'paid';
  notes?: string;
  created_at: string;
  updated_at: string;
  items?: any[];
}

export interface Installment {
  id: string;
  customer_id: string;
  total_amount: number;
  remaining_amount: number;
  monthly_payment: number;
  months: number;
  paid_months: number;
  down_payment: number;
  status: 'active' | 'completed';
  notes?: string;
  created_at: string;
  updated_at: string;
  items?: any[];
}

export interface Payment {
  id: string;
  customer_id: string;
  amount: number;
  payment_method: string;
  payment_date: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentReminder {
  id: string;
  customer_id: string;
  reference_id: string;
  reference_type: 'debt' | 'installment' | 'reminder';
  amount: number;
  due_date: string;
  status: string;
  notes?: string;
  reminder_sent_at?: string;
  created_at: string;
  updated_at: string;
  customer?: Customer;
}

export interface Sale {
  id: string;
  customer_id?: string;
  customer?: Customer;
  total_amount: number;
  discount: number;
  tax: number;
  final_amount: number;
  payment_method: string;
  created_at: string;
  items?: SaleItem[];
  sale_items?: SaleItem[];
}

export interface SaleItem {
  id: string;
  sale_id: string;
  product_id: string;
  product?: Product;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: string;
}

// Customers
export const getCustomers = async (): Promise<Customer[]> => {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching customers:', error);
    throw error;
  }

  return data || [];
};

export const getCustomer = async (id: string): Promise<Customer | null> => {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching customer:', error);
    return null;
  }

  return data;
};

export const addCustomer = async (customer: Omit<Customer, 'id' | 'created_at' | 'updated_at' | 'total_purchases' | 'points' | 'total_debt'>): Promise<Customer> => {
  const { data, error } = await supabase
    .from('customers')
    .insert([{ ...customer, total_purchases: 0, points: 0, total_debt: 0 }])
    .select()
    .single();

  if (error) {
    console.error('Error adding customer:', error);
    throw error;
  }

  return data;
};

export const updateCustomer = async (id: string, updates: Partial<Customer>): Promise<Customer | null> => {
  const { data, error } = await supabase
    .from('customers')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating customer:', error);
    return null;
  }

  return data;
};

export const deleteCustomer = async (id: string): Promise<boolean> => {
  const { error } = await supabase
    .from('customers')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting customer:', error);
    return false;
  }

  return true;
};

// Products
export const getProducts = async (): Promise<Product[]> => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      categories (
        id,
        name
      )
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching products:', error);
    throw error;
  }

  return (data || []).map(product => ({
    ...product,
    category: product.categories
  }));
};

export const getProduct = async (id: string): Promise<Product | null> => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      categories (
        id,
        name
      )
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching product:', error);
    return null;
  }

  return {
    ...data,
    category: data.categories
  };
};

export const addProduct = async (product: Omit<Product, 'id' | 'created_at' | 'updated_at' | 'category' | 'status'>): Promise<Product> => {
  const { data, error } = await supabase
    .from('products')
    .insert([{ ...product, status: 'متوفر' }])
    .select()
    .single();

  if (error) {
    console.error('Error adding product:', error);
    throw error;
  }

  return data;
};

export const updateProduct = async (id: string, updates: Partial<Product>): Promise<Product | null> => {
  const { data, error } = await supabase
    .from('products')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating product:', error);
    return null;
  }

  return data;
};

export const deleteProduct = async (id: string): Promise<boolean> => {
  const { error } = await supabase
    .from('products')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting product:', error);
    return false;
  }

  return true;
};

export const generateBarcode = async (): Promise<string> => {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return timestamp.slice(-5) + random;
};

// Categories
export const getCategories = async (): Promise<Category[]> => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }

  return data || [];
};

export const getCategory = async (id: string): Promise<Category | null> => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching category:', error);
    return null;
  }

  return data;
};

export const addCategory = async (category: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Promise<Category> => {
  const { data, error } = await supabase
    .from('categories')
    .insert([category])
    .select()
    .single();

  if (error) {
    console.error('Error adding category:', error);
    throw error;
  }

  return data;
};

export const updateCategory = async (id: string, updates: Partial<Category>): Promise<Category | null> => {
  const { data, error } = await supabase
    .from('categories')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating category:', error);
    return null;
  }

  return data;
};

export const deleteCategory = async (id: string): Promise<boolean> => {
  const { error } = await supabase
    .from('categories')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting category:', error);
    return false;
  }

  return true;
};

// Orders
export const getOrders = async (): Promise<Order[]> => {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }

  return data || [];
};

export const getOrder = async (id: string): Promise<Order | null> => {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching order:', error);
    return null;
  }

  return data;
};

export const addOrder = async (order: Omit<Order, 'id' | 'created_at' | 'updated_at'>): Promise<Order> => {
  const { data, error } = await supabase
    .from('orders')
    .insert([order])
    .select()
    .single();

  if (error) {
    console.error('Error adding order:', error);
    throw error;
  }

  return data;
};

export const updateOrder = async (id: string, updates: Partial<Order>): Promise<Order | null> => {
  const { data, error } = await supabase
    .from('orders')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating order:', error);
    return null;
  }

  return data;
};

export const deleteOrder = async (id: string): Promise<boolean> => {
  const { error } = await supabase
    .from('orders')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting order:', error);
    return false;
  }

  return true;
};

// Sales
export const getSales = async (): Promise<Sale[]> => {
  const { data, error } = await supabase
    .from('sales')
    .select(`
      *,
      customers (
        id,
        name,
        phone,
        email
      ),
      sale_items (
        *,
        products (
          id,
          name,
          price
        )
      )
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching sales:', error);
    throw error;
  }

  return (data || []).map(sale => ({
    ...sale,
    customer: sale.customers,
    items: sale.sale_items?.map((item: any) => ({
      ...item,
      product: item.products
    })),
    sale_items: sale.sale_items?.map((item: any) => ({
      ...item,
      product: item.products
    }))
  }));
};

export const createSale = async (saleData: Omit<Sale, 'id' | 'created_at'>, items: Omit<SaleItem, 'id' | 'sale_id' | 'created_at'>[]): Promise<Sale> => {
  const { data: sale, error: saleError } = await supabase
    .from('sales')
    .insert([saleData])
    .select()
    .single();

  if (saleError) {
    console.error('Error creating sale:', saleError);
    throw saleError;
  }

  const saleItems = items.map(item => ({
    ...item,
    sale_id: sale.id
  }));

  const { error: itemsError } = await supabase
    .from('sale_items')
    .insert(saleItems);

  if (itemsError) {
    console.error('Error creating sale items:', itemsError);
    throw itemsError;
  }

  return sale;
};

export const createDebt = async (debtData: Omit<Debt, 'id' | 'created_at' | 'updated_at'>): Promise<Debt> => {
  const { data, error } = await supabase
    .from('debts')
    .insert([debtData])
    .select()
    .single();

  if (error) {
    console.error('Error creating debt:', error);
    throw error;
  }

  return data;
};

export const createInstallment = async (installmentData: Omit<Installment, 'id' | 'created_at' | 'updated_at'>): Promise<Installment> => {
  const { data, error } = await supabase
    .from('installments')
    .insert([installmentData])
    .select()
    .single();

  if (error) {
    console.error('Error creating installment:', error);
    throw error;
  }

  return data;
};

// Debts
export const getCustomerDebts = async (customerId: string): Promise<Debt[]> => {
  const { data, error } = await supabase
    .from('debts')
    .select('*')
    .eq('customer_id', customerId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching debts:', error);
    throw error;
  }

  return data || [];
};

export const getDebts = async (): Promise<Debt[]> => {
  const { data, error } = await supabase
    .from('debts')
    .select(`
      *,
      customers (
        id,
        name,
        phone,
        email
      )
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching debts:', error);
    throw error;
  }

  return data || [];
};

export const payDebt = async (debtId: string, amount: number, paymentType: 'full' | 'partial'): Promise<void> => {
  const { data: debtData, error: debtError } = await supabase
    .from('debts')
    .select('remaining_amount')
    .eq('id', debtId)
    .single();

  if (debtError) {
    console.error('Error fetching debt:', debtError);
    throw debtError;
  }

  if (!debtData) {
    throw new Error('Debt not found');
  }

  const remainingAmount = debtData.remaining_amount;
  const newRemainingAmount = Math.max(0, remainingAmount - amount);

  const updates = {
    remaining_amount: newRemainingAmount,
    status: newRemainingAmount === 0 || paymentType === 'full' ? 'paid' : 'pending',
  };

  const { error } = await supabase
    .from('debts')
    .update(updates)
    .eq('id', debtId);

  if (error) {
    console.error('Error updating debt:', error);
    throw error;
  }
};

// Installments
export const getCustomerInstallments = async (customerId: string): Promise<Installment[]> => {
  const { data, error } = await supabase
    .from('installments')
    .select('*')
    .eq('customer_id', customerId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching installments:', error);
    throw error;
  }

  return data || [];
};

export const getInstallments = async (): Promise<Installment[]> => {
  const { data, error } = await supabase
    .from('installments')
    .select(`
      *,
      customers (
        id,
        name,
        phone,
        email
      )
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching installments:', error);
    throw error;
  }

  return data || [];
};

export const payInstallment = async (installmentId: string, amount: number): Promise<void> => {
  const { data: installmentData, error: installmentError } = await supabase
    .from('installments')
    .select('remaining_amount, monthly_payment, paid_months, months')
    .eq('id', installmentId)
    .single();

  if (installmentError) {
    console.error('Error fetching installment:', installmentError);
    throw installmentError;
  }

  if (!installmentData) {
    throw new Error('Installment not found');
  }

  let { remaining_amount, monthly_payment, paid_months, months } = installmentData;
  let newRemainingAmount = Math.max(0, remaining_amount - amount);
  let newPaidMonths = paid_months;
  let status = 'active';

  if (amount >= monthly_payment) {
    newPaidMonths = Math.min(months, paid_months + 1);
  }

  if (newRemainingAmount <= 0 || newPaidMonths >= months) {
    status = 'completed';
  }

  const updates = {
    remaining_amount: newRemainingAmount,
    paid_months: newPaidMonths,
    status: status,
  };

  const { error } = await supabase
    .from('installments')
    .update(updates)
    .eq('id', installmentId);

  if (error) {
    console.error('Error updating installment:', error);
    throw error;
  }
};

// Payment Reminders
export const updatePaymentReminder = async (reminderId: string, updates: Partial<PaymentReminder>): Promise<PaymentReminder | null> => {
  const { data, error } = await supabase
    .from('payment_reminders')
    .update(updates)
    .eq('id', reminderId)
    .select()
    .single();

  if (error) {
    console.error('Error updating payment reminder:', error);
    return null;
  }

  return data;
};

export const getPaymentReminders = async (): Promise<PaymentReminder[]> => {
  const { data: debts, error: debtsError } = await supabase
    .from('debts')
    .select(`
      id,
      customer_id,
      remaining_amount,
      created_at,
      notes,
      items,
      customers (
        id,
        name,
        phone,
        email,
        address,
        total_purchases,
        total_debt,
        points,
        created_at,
        updated_at
      )
    `)
    .eq('status', 'pending');

  if (debtsError) throw debtsError;

  const { data: installments, error: installmentsError } = await supabase
    .from('installments')
    .select(`
      id,
      customer_id,
      remaining_amount,
      created_at,
      notes,
      items,
      customers (
        id,
        name,
        phone,
        email,
        address,
        total_purchases,
        total_debt,
        points,
        created_at,
        updated_at
      )
    `)
    .eq('status', 'active');

  if (installmentsError) throw installmentsError;

  const reminders: PaymentReminder[] = [];

  // Add debt reminders
  debts?.forEach(debt => {
    const nextDueDate = new Date();
    nextDueDate.setDate(nextDueDate.getDate() + 7); // Due in 7 days

    reminders.push({
      id: `debt-${debt.id}`,
      customer_id: debt.customer_id,
      reference_id: debt.id,
      reference_type: 'debt' as const,
      amount: debt.remaining_amount,
      due_date: nextDueDate.toISOString().split('T')[0],
      status: 'pending',
      notes: debt.notes,
      created_at: debt.created_at,
      updated_at: debt.created_at,
      customer: Array.isArray(debt.customers) ? debt.customers[0] as Customer : debt.customers as Customer
    });
  });

  // Add installment reminders
  installments?.forEach(installment => {
    const nextDueDate = new Date();
    nextDueDate.setDate(nextDueDate.getDate() + 30); // Due in 30 days

    reminders.push({
      id: `installment-${installment.id}`,
      customer_id: installment.customer_id,
      reference_id: installment.id,
      reference_type: 'installment' as const,
      amount: installment.remaining_amount,
      due_date: nextDueDate.toISOString().split('T')[0],
      status: 'pending',
      notes: installment.notes,
      created_at: installment.created_at,
      updated_at: installment.created_at,
      customer: Array.isArray(installment.customers) ? installment.customers[0] as Customer : installment.customers as Customer
    });
  });

  return reminders.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
};

// Dashboard Stats
export const getDashboardStats = async () => {
  const today = new Date();
  const todayStart = new Date(today);
  todayStart.setHours(0, 0, 0, 0);
  
  const [salesData, productsData] = await Promise.all([
    supabase
      .from('sales')
      .select('final_amount')
      .gte('created_at', todayStart.toISOString()),
    supabase
      .from('products')
      .select('stock, min_stock')
  ]);

  const todayTotal = salesData.data?.reduce((sum, sale) => sum + (sale.final_amount || 0), 0) || 0;
  const totalProducts = productsData.data?.length || 0;
  const lowStockCount = productsData.data?.filter(product => product.stock <= product.min_stock).length || 0;
  const outOfStockCount = productsData.data?.filter(product => product.stock === 0).length || 0;

  return {
    todayTotal,
    totalProducts,
    lowStockCount,
    outOfStockCount
  };
};

export const resetAllData = async (): Promise<void> => {
  const { error } = await supabase.rpc('reset_all_data');
  
  if (error) {
    console.error('Error resetting data:', error);
    throw error;
  }
};
