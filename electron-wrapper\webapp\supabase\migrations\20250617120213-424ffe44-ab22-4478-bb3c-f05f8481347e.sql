
-- تحديث دالة تصفير البيانات لتجنب خطأ "DELETE requires a WHERE clause"
CREATE OR REPLACE FUNCTION public.reset_all_data()
RETURNS void
LANGUAGE plpgsql
AS $function$
BEGIN
  -- حذف جميع البيانات بالترتيب الصحيح مع استخدام WHERE clause
  -- حذف عناصر المبيعات أولاً
  DELETE FROM public.sale_items WHERE id IS NOT NULL;
  
  -- حذف عناصر المشتريات
  DELETE FROM public.purchase_items WHERE id IS NOT NULL;
  
  -- حذف مدفوعات الديون والأقساط
  DELETE FROM public.debt_payments WHERE id IS NOT NULL;
  DELETE FROM public.installment_payments WHERE id IS NOT NULL;
  
  -- حذف المبيعات والمشتريات
  DELETE FROM public.sales WHERE id IS NOT NULL;
  DELETE FROM public.purchases WHERE id IS NOT NULL;
  
  -- حذف الديون والأقساط
  DELETE FROM public.debts WHERE id IS NOT NULL;
  DELETE FROM public.installments WHERE id IS NOT NULL;
  
  -- حذف المنتجات والعملاء والموردين
  DELETE FROM public.products WHERE id IS NOT NULL;
  DELETE FROM public.customers WHERE id IS NOT NULL;
  DELETE FROM public.suppliers WHERE id IS NOT NULL;
  
  -- حذف الفئات
  DELETE FROM public.categories WHERE id IS NOT NULL;
  
  -- إعادة إدراج الفئات الافتراضية
  INSERT INTO public.categories (name) VALUES 
  ('مشروبات'),
  ('وجبات خفيفة'),
  ('منظفات'),
  ('مواد غذائية'),
  ('أدوات منزلية');
END;
$function$
