# 🚀 ابدأ من هنا - Iradoo POS Desktop

## ⚡ البدء السريع (3 خطوات):

### 1️⃣ تثبيت Node.js (مرة واحدة فقط)
```
❌ إذا ظهر خطأ "Node.js غير مثبت"
👉 اقرأ ملف: INSTALL-NODEJS.md
👉 أو شغّل: check-nodejs.bat
```

### 2️⃣ تشغيل المدير البسيط
```
👉 انقر نقراً مزدوجاً على: simple-run.bat
```

### 3️⃣ اتبع القائمة
```
[1] فحص Node.js ← ابدأ من هنا
[2] تثبيت التبعيات
[3] نسخ ملفات التطبيق
[4] تشغيل التطبيق
[5] بناء التطبيق
```

---

## 📁 الملفات المهمة:

| الملف | متى تستخدمه |
|-------|-------------|
| `simple-run.bat` | **ابدأ من هنا** - القائمة الرئيسية |
| `check-nodejs.bat` | فحص تثبيت Node.js |
| `INSTALL-NODEJS.md` | دليل تثبيت Node.js |
| `webapp/` | ضع ملفات تطبيقك هنا |
| `assets/` | ضع الأيقونات هنا |

---

## 🎯 الهدف:

تحويل تطبيق الويب إلى تطبيق سطح مكتب يعمل على Windows 7-11

## ✅ النتيجة النهائية:

- ملف **Setup.exe** للتثبيت
- ملف **Portable.exe** محمول
- يعمل بدون إنترنت
- أيقونة مخصصة

---

## ❗ مشكلة؟

1. **Node.js غير مثبت** → اقرأ `INSTALL-NODEJS.md`
2. **أخطاء في النص العربي** → استخدم `simple-run.bat`
3. **التطبيق لا يعمل** → تأكد من وجود `index.html` في `webapp/`

---

## 📞 للمساعدة:

- `README.md` - دليل شامل مفصل
- `QUICK-START.md` - دليل سريع
- `INSTALL-NODEJS.md` - تثبيت Node.js
