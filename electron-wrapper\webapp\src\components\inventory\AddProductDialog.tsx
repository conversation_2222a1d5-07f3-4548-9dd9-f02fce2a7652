
import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Settings, Image, RefreshCw } from 'lucide-react';
import { addProduct, generateBarcode, type Category } from '@/utils/database';
import { validateProductData } from '@/utils/errorHandler';

interface AddProductDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddProduct: () => void;
  categories: Category[];
  onManageCategories: () => void;
}

const AddProductDialog = ({ 
  open, 
  onOpenChange, 
  onAddProduct, 
  categories, 
  onManageCategories
}: AddProductDialogProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    category_id: '',
    price: '',
    cost_price: '',
    wholesale_price: '',
    stock: '',
    min_stock: '',
    barcode: '',
    image: ''
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      console.log('Submitting product data:', formData);

      // Validate form data
      const validationError = validateProductData({
        name: formData.name,
        category_id: formData.category_id,
        price: parseFloat(formData.price),
        stock: parseInt(formData.stock),
        min_stock: parseInt(formData.min_stock),
        barcode: formData.barcode
      });

      if (validationError) {
        toast({
          title: "خطأ في البيانات",
          description: validationError,
          variant: "destructive"
        });
        return;
      }

      const productData = {
        name: formData.name.trim(),
        category_id: formData.category_id,
        price: parseFloat(formData.price),
        cost_price: parseFloat(formData.cost_price) || 0,
        wholesale_price: parseFloat(formData.wholesale_price) || 0,
        stock: parseInt(formData.stock),
        min_stock: parseInt(formData.min_stock),
        barcode: formData.barcode,
        image: formData.image || undefined
      };

      console.log('Adding product with data:', productData);
      await addProduct(productData);
      
      // Reset form
      setFormData({
        name: '',
        category_id: '',
        price: '',
        cost_price: '',
        wholesale_price: '',
        stock: '',
        min_stock: '',
        barcode: '',
        image: ''
      });

      toast({
        title: "تم إضافة المنتج",
        description: `تم إضافة ${formData.name} بنجاح`,
      });
      
      onAddProduct();
    } catch (error: any) {
      console.error('Error adding product:', error);
      
      let errorMessage = "حدث خطأ أثناء إضافة المنتج";
      
      if (error.message?.includes('duplicate')) {
        errorMessage = "الباركود موجود مسبقاً، يرجى استخدام باركود آخر";
      } else if (error.message?.includes('foreign key')) {
        errorMessage = "فئة المنتج غير صحيحة";
      }
      
      toast({
        title: "خطأ في إضافة المنتج",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleGenerateBarcode = async () => {
    try {
      const newBarcode = await generateBarcode();
      handleInputChange('barcode', newBarcode);
      toast({
        title: "تم توليد الباركود",
        description: `الباركود الجديد: ${newBarcode}`,
      });
    } catch (error) {
      console.error('Error generating barcode:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء توليد الباركود",
        variant: "destructive"
      });
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageUrl = event.target?.result as string;
        handleInputChange('image', imageUrl);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle>إضافة منتج جديد</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">اسم المنتج *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="أدخل اسم المنتج"
              className="text-right"
              required
            />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="category">الفئة *</Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onManageCategories}
                className="text-blue-600 hover:text-blue-700"
              >
                <Settings className="w-4 h-4 ml-1" />
                إدارة الفئات
              </Button>
            </div>
            <Select value={formData.category_id} onValueChange={(value) => handleInputChange('category_id', value)}>
              <SelectTrigger className="text-right">
                <SelectValue placeholder="اختر فئة المنتج" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="barcode">الباركود *</Label>
            <div className="flex gap-2">
              <Input
                id="barcode"
                value={formData.barcode}
                onChange={(e) => handleInputChange('barcode', e.target.value.replace(/\D/g, ''))}
                placeholder="أدخل الباركود أو اضغط توليد"
                className="text-right font-mono"
                required
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleGenerateBarcode}
                className="px-3"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
            <p className="text-xs text-gray-500">يجب أن يكون 8 أرقام على الأقل</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="image">صورة المنتج</Label>
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="text-right"
                />
                <Image className="w-5 h-5 text-gray-400" />
              </div>
              {formData.image && (
                <div className="mt-2">
                  <img
                    src={formData.image}
                    alt="معاينة الصورة"
                    className="w-20 h-20 object-cover rounded border"
                  />
                </div>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="cost_price">كلفة المنتج (د.ع)</Label>
              <Input
                id="cost_price"
                type="number"
                min="0"
                step="0.01"
                value={formData.cost_price}
                onChange={(e) => handleInputChange('cost_price', e.target.value)}
                placeholder="0.00"
                className="text-right"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="wholesale_price">سعر الوكيل (د.ع)</Label>
              <Input
                id="wholesale_price"
                type="number"
                min="0"
                step="0.01"
                value={formData.wholesale_price}
                onChange={(e) => handleInputChange('wholesale_price', e.target.value)}
                placeholder="0.00"
                className="text-right"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="price">السعر المفرد (د.ع) *</Label>
              <Input
                id="price"
                type="number"
                min="0"
                step="0.01"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="0.00"
                className="text-right"
                required
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stock">الكمية الحالية *</Label>
              <Input
                id="stock"
                type="number"
                min="0"
                value={formData.stock}
                onChange={(e) => handleInputChange('stock', e.target.value)}
                placeholder="0"
                className="text-right"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="minStock">الحد الأدنى *</Label>
              <Input
                id="minStock"
                type="number"
                min="0"
                value={formData.min_stock}
                onChange={(e) => handleInputChange('min_stock', e.target.value)}
                placeholder="0"
                className="text-right"
                required
              />
            </div>
          </div>
          
          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              إلغاء
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'جاري الإضافة...' : 'إضافة المنتج'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddProductDialog;
