
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Palette, Eye, FileText, Settings, TestTube } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';
import ReceiptTemplateSelector from '../receipts/ReceiptTemplateSelector';
import ReceiptPreview from '../receipts/ReceiptPreview';

const ReceiptDesignSettings = () => {
  const { toast } = useToast();
  const { settings, updateSettings } = useSettings();
  const [showPreview, setShowPreview] = useState(false);

  // Sample data for preview
  const sampleItems = [
    { id: '1', name: 'قهوة عربية', price: 25000, quantity: 2, total: 50000 },
    { id: '2', name: 'شاي أحمر', price: 15000, quantity: 1, total: 15000 },
    { id: '3', name: 'سكر', price: 8000, quantity: 1, total: 8000 }
  ];

  const handleSave = async () => {
    try {
      // حفظ الإعدادات في localStorage أو قاعدة البيانات
      localStorage.setItem('receiptSettings', JSON.stringify({
        receiptTemplate: settings.receiptTemplate,
        storeName: settings.storeName,
        storeAddress: settings.storeAddress,
        storePhone: settings.storePhone,
        taxNumber: settings.taxNumber,
        receiptPrefix: settings.receiptPrefix,
        receiptFooter: settings.receiptFooter,
        printLogo: settings.printLogo,
        printQR: settings.printQR,
        taxEnabled: settings.taxEnabled
      }));

      toast({
        title: "تم الحفظ",
        description: "تم حفظ إعدادات تصميم الفاتورة بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ في الحفظ",
        description: "حدث خطأ أثناء حفظ الإعدادات",
        variant: "destructive"
      });
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    updateSettings({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            قوالب تصميم الفاتورة
          </CardTitle>
          <CardDescription>
            اختر قالب التصميم المناسب لفواتيرك
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ReceiptTemplateSelector
            selectedTemplate={settings.receiptTemplate || 'classic'}
            onTemplateSelect={(template) => handleInputChange('receiptTemplate', template)}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            تخصيص محتوى الفاتورة
          </CardTitle>
          <CardDescription>
            تخصيص المعلومات والنصوص التي تظهر في الفاتورة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="storeName">اسم المتجر</Label>
              <Input
                id="storeName"
                value={settings.storeName}
                onChange={(e) => handleInputChange('storeName', e.target.value)}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="storeAddress">عنوان المتجر</Label>
              <Input
                id="storeAddress"
                value={settings.storeAddress}
                onChange={(e) => handleInputChange('storeAddress', e.target.value)}
                className="text-right"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="storePhone">رقم الهاتف</Label>
              <Input
                id="storePhone"
                value={settings.storePhone}
                onChange={(e) => handleInputChange('storePhone', e.target.value)}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="taxNumber">الرقم الضريبي</Label>
              <Input
                id="taxNumber"
                value={settings.taxNumber}
                onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                className="text-right"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="receiptPrefix">بادئة رقم الفاتورة</Label>
              <Input
                id="receiptPrefix"
                value={settings.receiptPrefix || 'INV'}
                onChange={(e) => handleInputChange('receiptPrefix', e.target.value)}
                className="text-right"
                placeholder="INV"
                maxLength={5}
              />
              <p className="text-sm text-muted-foreground">
                البادئة التي تظهر قبل رقم الفاتورة (مثل: INV-001234)
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="receiptFooter">نص نهاية الفاتورة</Label>
            <Textarea
              id="receiptFooter"
              value={settings.receiptFooter}
              onChange={(e) => handleInputChange('receiptFooter', e.target.value)}
              className="text-right min-h-20"
              placeholder="رسالة شكر أو معلومات إضافية تظهر في نهاية الفاتورة"
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>عرض الشعار</Label>
                <p className="text-sm text-muted-foreground">
                  إظهار شعار المتجر في أعلى الفاتورة
                </p>
              </div>
              <Switch
                checked={settings.printLogo}
                onCheckedChange={(checked) => handleInputChange('printLogo', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>عرض رمز QR</Label>
                <p className="text-sm text-muted-foreground">
                  إضافة رمز QR للتحقق من الفاتورة
                </p>
              </div>
              <Switch
                checked={settings.printQR}
                onCheckedChange={(checked) => handleInputChange('printQR', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>عرض معلومات الضريبة</Label>
                <p className="text-sm text-muted-foreground">
                  إظهار تفاصيل الضريبة في الفاتورة
                </p>
              </div>
              <Switch
                checked={settings.taxEnabled}
                onCheckedChange={(checked) => handleInputChange('taxEnabled', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={() => setShowPreview(!showPreview)}
          className="flex items-center gap-2"
        >
          <Eye className="w-4 h-4" />
          {showPreview ? 'إخفاء المعاينة' : 'معاينة الفاتورة'}
        </Button>
        <Button onClick={handleSave} className="flex items-center gap-2">
          <Settings className="w-4 h-4" />
          حفظ الإعدادات
        </Button>
      </div>

      {showPreview && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              معاينة الفاتورة
            </CardTitle>
            <CardDescription>
              معاينة مباشرة لشكل الفاتورة مع الإعدادات الحالية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ReceiptPreview
              items={sampleItems}
              total={73000}
              template={settings.receiptTemplate as any}
              showLogo={settings.printLogo}
              showQR={settings.printQR}
              customerInfo={{
                name: 'أحمد محمد',
                phone: '+964 ************'
              }}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ReceiptDesignSettings;
