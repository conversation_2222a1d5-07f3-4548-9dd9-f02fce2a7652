
import { useState, useEffect } from 'react';
import { licenseManager } from '@/utils/licenseManager';

export const useLicenseMode = () => {
  const [isViewOnlyMode, setIsViewOnlyMode] = useState(false);
  const [canPerformActions, setCanPerformActions] = useState(false);

  useEffect(() => {
    const checkLicenseMode = () => {
      const viewOnly = licenseManager.isViewOnlyMode();
      const canAct = licenseManager.canPerformActions();
      
      setIsViewOnlyMode(viewOnly);
      setCanPerformActions(canAct);
    };

    checkLicenseMode();
    
    // إعادة فحص الترخيص كل دقيقة
    const interval = setInterval(checkLicenseMode, 60000);
    
    return () => clearInterval(interval);
  }, []);

  return {
    isViewOnlyMode,
    canPerformActions
  };
};
