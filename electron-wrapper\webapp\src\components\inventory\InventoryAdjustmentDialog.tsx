
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { updateProduct, type Product } from '@/utils/database';
import { Package, Plus, Minus, FileText } from 'lucide-react';

interface InventoryAdjustmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product: Product | null;
  onAdjustment: () => void;
}

const InventoryAdjustmentDialog: React.FC<InventoryAdjustmentDialogProps> = ({
  open,
  onOpenChange,
  product,
  onAdjustment
}) => {
  const { toast } = useToast();
  const [adjustmentType, setAdjustmentType] = useState<'add' | 'subtract' | 'set'>('add');
  const [quantity, setQuantity] = useState('');
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!product || !quantity || !reason) return;

    setLoading(true);
    try {
      let newStock = product.stock;
      const adjustmentQty = parseInt(quantity);

      switch (adjustmentType) {
        case 'add':
          newStock = product.stock + adjustmentQty;
          break;
        case 'subtract':
          newStock = Math.max(0, product.stock - adjustmentQty);
          break;
        case 'set':
          newStock = adjustmentQty;
          break;
      }

      await updateProduct(product.id, { stock: newStock });
      
      toast({
        title: "تم تعديل المخزون",
        description: `تم تحديث مخزون ${product.name} من ${product.stock} إلى ${newStock}`,
      });

      onAdjustment();
      onOpenChange(false);
      setQuantity('');
      setReason('');
      setAdjustmentType('add');
    } catch (error) {
      console.error('Error adjusting inventory:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تعديل المخزون",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getAdjustmentIcon = () => {
    switch (adjustmentType) {
      case 'add': return <Plus className="w-5 h-5 text-green-600" />;
      case 'subtract': return <Minus className="w-5 h-5 text-red-600" />;
      case 'set': return <FileText className="w-5 h-5 text-blue-600" />;
    }
  };

  const getAdjustmentDescription = () => {
    if (!product || !quantity) return '';
    const adjustmentQty = parseInt(quantity) || 0;
    
    switch (adjustmentType) {
      case 'add':
        return `المخزون الجديد: ${product.stock + adjustmentQty}`;
      case 'subtract':
        return `المخزون الجديد: ${Math.max(0, product.stock - adjustmentQty)}`;
      case 'set':
        return `المخزون الجديد: ${adjustmentQty}`;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            تعديل المخزون
          </DialogTitle>
        </DialogHeader>

        {product && (
          <div className="space-y-6">
            {/* Product Info */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-gray-900">{product.name}</h3>
              <p className="text-sm text-gray-600">المخزون الحالي: {product.stock}</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Adjustment Type */}
              <div className="space-y-2">
                <Label>نوع التعديل</Label>
                <Select value={adjustmentType} onValueChange={(value: 'add' | 'subtract' | 'set') => setAdjustmentType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="add">
                      <div className="flex items-center gap-2">
                        <Plus className="w-4 h-4 text-green-600" />
                        إضافة للمخزون
                      </div>
                    </SelectItem>
                    <SelectItem value="subtract">
                      <div className="flex items-center gap-2">
                        <Minus className="w-4 h-4 text-red-600" />
                        خصم من المخزون
                      </div>
                    </SelectItem>
                    <SelectItem value="set">
                      <div className="flex items-center gap-2">
                        <FileText className="w-4 h-4 text-blue-600" />
                        تحديد المخزون
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Quantity */}
              <div className="space-y-2">
                <Label>الكمية</Label>
                <div className="relative">
                  <Input
                    type="number"
                    min="0"
                    value={quantity}
                    onChange={(e) => setQuantity(e.target.value)}
                    placeholder="ادخل الكمية"
                    className="pl-10"
                    required
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    {getAdjustmentIcon()}
                  </div>
                </div>
                {quantity && (
                  <p className="text-sm text-gray-600">{getAdjustmentDescription()}</p>
                )}
              </div>

              {/* Reason */}
              <div className="space-y-2">
                <Label>سبب التعديل</Label>
                <Textarea
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="اكتب سبب تعديل المخزون..."
                  required
                />
              </div>

              {/* Submit Button */}
              <Button 
                type="submit" 
                className="w-full" 
                disabled={loading || !quantity || !reason}
              >
                {loading ? "جاري التحديث..." : "تأكيد التعديل"}
              </Button>
            </form>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default InventoryAdjustmentDialog;
