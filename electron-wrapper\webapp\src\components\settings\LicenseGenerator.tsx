
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { Key, Copy, Download, Plus } from 'lucide-react';
import { licenseManager, LicenseData } from '@/utils/licenseManager';
import { useLocalAuth } from '@/hooks/useLocalAuth';
import LicenseAdminPanel from './LicenseAdminPanel';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const LicenseGenerator = () => {
  const { toast } = useToast();
  const { user } = useLocalAuth();
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    macAddress: '',
    deviceName: '',
    expirationDate: '',
    hasExpiration: false
  });
  const [features, setFeatures] = useState({
    sales: true,
    inventory: true,
    reports: true,
    customers: true,
    backup: false,
    multiUser: false
  });
  const [generatedLicense, setGeneratedLicense] = useState('');

  const handleGenerateLicense = () => {
    if (!formData.customerName.trim()) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال اسم العميل',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.customerEmail.trim()) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال بريد العميل الإلكتروني',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.macAddress.trim()) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال معرف الجهاز',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.deviceName.trim()) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال اسم الجهاز',
        variant: 'destructive',
      });
      return;
    }

    if (formData.hasExpiration && !formData.expirationDate) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال تاريخ الانتهاء',
        variant: 'destructive',
      });
      return;
    }

    const selectedFeatures = Object.entries(features)
      .filter(([, enabled]) => enabled)
      .map(([feature]) => feature);

    const licenseData: Omit<LicenseData, 'issuedDate' | 'serialNumber'> = {
      customerName: formData.customerName.trim(),
      macAddress: formData.macAddress.trim(),
      deviceName: formData.deviceName.trim(),
      expirationDate: formData.hasExpiration ? formData.expirationDate : undefined,
      features: selectedFeatures
    };

    try {
      const license = licenseManager.generateLicense(licenseData, user?.email || 'system', formData.customerEmail.trim());
      setGeneratedLicense(license);
      
      toast({
        title: 'تم إنشاء الترخيص',
        description: `تم إنشاء مفتاح الترخيص للعميل: ${formData.customerName}`,
      });
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في إنشاء مفتاح الترخيص',
        variant: 'destructive',
      });
    }
  };

  const copyLicense = () => {
    navigator.clipboard.writeText(generatedLicense);
    toast({
      title: 'تم النسخ',
      description: 'تم نسخ مفتاح الترخيص إلى الحافظة',
    });
  };

  const downloadLicense = () => {
    const licenseInfo = {
      license: generatedLicense,
      customerName: formData.customerName,
      generatedDate: new Date().toISOString(),
      expirationDate: formData.hasExpiration ? formData.expirationDate : null,
      features: Object.entries(features).filter(([, enabled]) => enabled).map(([feature]) => feature),
      generatedBy: user?.email || 'system'
    };

    const blob = new Blob([JSON.stringify(licenseInfo, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `license-${formData.customerName.replace(/\s+/g, '-')}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'تم التنزيل',
      description: 'تم تنزيل ملف الترخيص',
    });
  };

  const resetForm = () => {
    setFormData({
      customerName: '',
      customerEmail: '',
      macAddress: '',
      deviceName: '',
      expirationDate: '',
      hasExpiration: false
    });
    setFeatures({
      sales: true,
      inventory: true,
      reports: true,
      customers: true,
      backup: false,
      multiUser: false
    });
    setGeneratedLicense('');
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="generator" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generator" className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            إنشاء ترخيص جديد
          </TabsTrigger>
          <TabsTrigger value="admin" className="flex items-center gap-2">
            <Key className="w-4 h-4" />
            إدارة التراخيص
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generator" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                مولد التراخيص
              </CardTitle>
              <CardDescription>
                أداة لإنشاء مفاتيح ترخيص جديدة للعملاء
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              
              {/* معلومات العميل */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">معلومات العميل</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="customer-name">اسم العميل *</Label>
                    <Input
                      id="customer-name"
                      value={formData.customerName}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                      placeholder="اسم الشركة أو العميل"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="customer-email">البريد الإلكتروني *</Label>
                    <Input
                      id="customer-email"
                      type="email"
                      value={formData.customerEmail}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerEmail: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="mac-address">معرف الجهاز *</Label>
                    <Input
                      id="mac-address"
                      value={formData.macAddress}
                      onChange={(e) => setFormData(prev => ({ ...prev, macAddress: e.target.value }))}
                      placeholder="معرف الجهاز من العميل"
                      className="font-mono text-sm"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="device-name">اسم الجهاز *</Label>
                    <Input
                      id="device-name"
                      value={formData.deviceName}
                      onChange={(e) => setFormData(prev => ({ ...prev, deviceName: e.target.value }))}
                      placeholder="اسم الجهاز من العميل"
                      className="font-mono text-sm"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="has-expiration"
                        checked={formData.hasExpiration}
                        onCheckedChange={(checked) => 
                          setFormData(prev => ({ ...prev, hasExpiration: checked as boolean }))
                        }
                      />
                      <Label htmlFor="has-expiration">تحديد تاريخ انتهاء</Label>
                    </div>
                    
                    {formData.hasExpiration && (
                      <Input
                        type="date"
                        value={formData.expirationDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, expirationDate: e.target.value }))}
                      />
                    )}
                  </div>
                </div>
              </div>

              {/* الميزات المتاحة */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">الميزات المتاحة</h3>
                
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {Object.entries(features).map(([feature, enabled]) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <Checkbox
                        id={feature}
                        checked={enabled}
                        onCheckedChange={(checked) => 
                          setFeatures(prev => ({ ...prev, [feature]: checked as boolean }))
                        }
                      />
                      <Label htmlFor={feature}>
                        {feature === 'sales' && 'المبيعات'}
                        {feature === 'inventory' && 'المخزون'}
                        {feature === 'reports' && 'التقارير'}
                        {feature === 'customers' && 'العملاء'}
                        {feature === 'backup' && 'النسخ الاحتياطية'}
                        {feature === 'multiUser' && 'تعدد المستخدمين'}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* أزرار التحكم */}
              <div className="flex gap-2">
                <Button onClick={handleGenerateLicense} className="flex-1">
                  <Key className="w-4 h-4 mr-2" />
                  إنشاء مفتاح الترخيص
                </Button>
                <Button variant="outline" onClick={resetForm}>
                  إعادة تعيين
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* مفتاح الترخيص المولد */}
          {generatedLicense && (
            <Card>
              <CardHeader>
                <CardTitle>مفتاح الترخيص</CardTitle>
                <CardDescription>
                  المفتاح الذي تم إنشاؤه للعميل: {formData.customerName}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  value={generatedLicense}
                  readOnly
                  className="min-h-[120px] font-mono text-sm"
                />
                
                <div className="flex gap-2">
                  <Button onClick={copyLicense} variant="outline">
                    <Copy className="w-4 h-4 mr-2" />
                    نسخ المفتاح
                  </Button>
                  <Button onClick={downloadLicense} variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    تنزيل الملف
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="admin">
          <LicenseAdminPanel />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LicenseGenerator;
