
-- إنشاء جدول الفئات
CREATE TABLE public.categories (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول المنتجات
CREATE TABLE public.products (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
  price DECIMAL(10,2) NOT NULL DEFAULT 0 CHECK (price >= 0),
  stock INTEGER NOT NULL DEFAULT 0 CHECK (stock >= 0),
  min_stock INTEGER NOT NULL DEFAULT 0 CHECK (min_stock >= 0),
  barcode TEXT UNIQUE,
  image TEXT,
  status TEXT NOT NULL DEFAULT 'متوفر' CHECK (status IN ('متوفر', 'منخفض', 'نفاد')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- إنشاء جدول العملاء
CREATE TABLE public.customers (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  phone TEXT,
  email TEXT,
  address TEXT,
  total_purchases DECIMAL(10,2) NOT NULL DEFAULT 0,
  points INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- إنشاء جدول المبيعات
CREATE TABLE public.sales (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,
  total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  discount DECIMAL(10,2) NOT NULL DEFAULT 0,
  tax DECIMAL(10,2) NOT NULL DEFAULT 0,
  final_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  payment_method TEXT NOT NULL DEFAULT 'نقدي' CHECK (payment_method IN ('نقدي', 'بطاقة', 'تحويل')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- إنشاء جدول تفاصيل المبيعات
CREATE TABLE public.sale_items (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  sale_id UUID REFERENCES public.sales(id) ON DELETE CASCADE,
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
  total_price DECIMAL(10,2) NOT NULL CHECK (total_price >= 0),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_products_barcode ON public.products(barcode);
CREATE INDEX idx_products_category ON public.products(category_id);
CREATE INDEX idx_sales_customer ON public.sales(customer_id);
CREATE INDEX idx_sales_date ON public.sales(created_at);
CREATE INDEX idx_sale_items_sale ON public.sale_items(sale_id);
CREATE INDEX idx_sale_items_product ON public.sale_items(product_id);

-- إدراج بعض الفئات الافتراضية
INSERT INTO public.categories (name) VALUES 
('مشروبات'),
('وجبات خفيفة'),
('منظفات'),
('مواد غذائية'),
('أدوات منزلية');

-- إنشاء دالة لتحديث حالة المنتج تلقائياً
CREATE OR REPLACE FUNCTION update_product_status()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.stock = 0 THEN
    NEW.status = 'نفاد';
  ELSIF NEW.stock <= NEW.min_stock THEN
    NEW.status = 'منخفض';
  ELSE
    NEW.status = 'متوفر';
  END IF;
  
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغل لتحديث حالة المنتج
CREATE TRIGGER trigger_update_product_status
  BEFORE UPDATE OF stock, min_stock ON public.products
  FOR EACH ROW
  EXECUTE FUNCTION update_product_status();

-- إنشاء دالة لتحديث المخزون عند البيع
CREATE OR REPLACE FUNCTION update_stock_on_sale()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.products 
  SET stock = stock - NEW.quantity
  WHERE id = NEW.product_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغل لتحديث المخزون
CREATE TRIGGER trigger_update_stock_on_sale
  AFTER INSERT ON public.sale_items
  FOR EACH ROW
  EXECUTE FUNCTION update_stock_on_sale();

-- إنشاء دالة لتصفير البيانات
CREATE OR REPLACE FUNCTION reset_all_data()
RETURNS void AS $$
BEGIN
  -- حذف جميع البيانات بالترتيب الصحيح لتجنب مشاكل المراجع الخارجية
  DELETE FROM public.sale_items;
  DELETE FROM public.sales;
  DELETE FROM public.products;
  DELETE FROM public.customers;
  DELETE FROM public.categories;
  
  -- إعادة إدراج الفئات الافتراضية
  INSERT INTO public.categories (name) VALUES 
  ('مشروبات'),
  ('وجبات خفيفة'),
  ('منظفات'),
  ('مواد غذائية'),
  ('أدوات منزلية');
END;
$$ LANGUAGE plpgsql;

-- تمكين Row Level Security (إذا كان مطلوباً لاحقاً)
-- ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.sale_items ENABLE ROW LEVEL SECURITY;
