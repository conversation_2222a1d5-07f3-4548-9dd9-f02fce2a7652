directories:
  output: dist
  buildResources: resources
appId: com.iradoo.pos.desktop
productName: Iradoo POS
files:
  - filter:
      - src/**/*
      - webapp/**/*
      - assets/**/*
      - node_modules/**/*
      - package.json
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Iradoo POS
  include: installer.nsh
portable:
  artifactName: ${productName}-${version}-Portable.${ext}
mac:
  target: dmg
  icon: assets/icon.icns
  category: public.app-category.business
linux:
  target:
    - AppImage
    - deb
  icon: assets/icon.png
  category: Office
electronVersion: 28.3.3
