@echo off
chcp 65001 >nul 2>&1
cls
echo ========================================
echo    Node.js Installation Check
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js is installed!
    echo Version: 
    node --version
    echo.
    echo Checking npm...
    npm --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ npm is available!
        echo Version: 
        npm --version
        echo.
        echo ========================================
        echo ✅ SYSTEM READY!
        echo You can now run the Electron project.
        echo ========================================
        echo.
        echo Next steps:
        echo 1. Run setup.bat to install dependencies
        echo 2. Run copy-webapp.bat to copy your web app
        echo 3. Run start.bat to test the application
        echo 4. Run build.bat to create installer
    ) else (
        echo ❌ npm is not available!
        echo This usually means Node.js installation is incomplete.
        echo Please reinstall Node.js from https://nodejs.org
    )
) else (
    echo ❌ Node.js is NOT installed!
    echo.
    echo REQUIRED: Please install Node.js first
    echo.
    echo Steps:
    echo 1. Go to: https://nodejs.org
    echo 2. Download LTS version for Windows
    echo 3. Run the installer with default settings
    echo 4. Restart this command prompt
    echo 5. Run this check again
    echo.
    echo ========================================
    echo Opening Node.js download page...
    start https://nodejs.org/en/download/
)

echo.
echo Press any key to continue...
pause >nul
