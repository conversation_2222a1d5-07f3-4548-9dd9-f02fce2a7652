const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const crypto = require('crypto');
const log = require('electron-log');

class DatabaseManager {
    constructor() {
        this.data = {
            users: [],
            products: [],
            sales: [],
            sale_items: [],
            settings: [],
            app_logs: []
        };
        this.dbPath = null;
        this.isInitialized = false;
    }

    /**
     * Initialize database and load data
     */
    async initialize() {
        try {
            // Get user data directory
            const userDataPath = app.getPath('userData');
            const dbDir = path.join(userDataPath, 'database');
            
            // Create database directory if it doesn't exist
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
                log.info('Database directory created:', dbDir);
            }

            this.dbPath = path.join(dbDir, 'iradoo_pos.json');
            log.info('Database path:', this.dbPath);

            // Load existing data or create new
            this.loadData();
            
            // Insert default data if needed
            this.insertDefaultData();
            
            // Save data
            this.saveData();
            
            this.isInitialized = true;
            log.info('Database initialized successfully');
            
            return true;
        } catch (error) {
            log.error('Database initialization failed:', error);
            throw error;
        }
    }

    /**
     * Load data from JSON file
     */
    loadData() {
        try {
            if (fs.existsSync(this.dbPath)) {
                const rawData = fs.readFileSync(this.dbPath, 'utf8');
                this.data = JSON.parse(rawData);
                log.info('Data loaded from file');
            } else {
                log.info('No existing data file, starting with empty database');
            }
        } catch (error) {
            log.error('Error loading data:', error);
            // Start with empty data if file is corrupted
            this.data = {
                users: [],
                products: [],
                sales: [],
                sale_items: [],
                settings: [],
                app_logs: []
            };
        }
    }

    /**
     * Save data to JSON file
     */
    saveData() {
        try {
            fs.writeFileSync(this.dbPath, JSON.stringify(this.data, null, 2));
            log.info('Data saved to file');
        } catch (error) {
            log.error('Error saving data:', error);
            throw error;
        }
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return crypto.randomUUID();
    }

    /**
     * Hash password
     */
    hashPassword(password) {
        return crypto.createHash('sha256').update(password).digest('hex');
    }

    /**
     * Insert default data if needed
     */
    insertDefaultData() {
        try {
            // Check if admin user exists
            const adminExists = this.data.users.find(user => user.role === 'admin');
            
            if (!adminExists) {
                // Create default admin user
                const adminUser = {
                    id: this.generateId(),
                    username: 'admin',
                    email: '<EMAIL>',
                    password_hash: this.hashPassword('admin123'),
                    full_name: 'System Administrator',
                    role: 'admin',
                    is_active: true,
                    created_at: new Date().toISOString()
                };
                this.data.users.push(adminUser);
                log.info('Default admin user created');
            }

            // Insert default settings
            const defaultSettings = [
                ['app_name', 'Iradoo POS', 'Application name'],
                ['app_version', '1.0.0', 'Application version'],
                ['currency', 'USD', 'Default currency'],
                ['tax_rate', '0.10', 'Default tax rate (10%)'],
                ['company_name', 'Your Company', 'Company name'],
                ['company_address', '', 'Company address'],
                ['receipt_footer', 'Thank you for your business!', 'Receipt footer text']
            ];

            for (const [key, value, description] of defaultSettings) {
                const exists = this.data.settings.find(setting => setting.key === key);
                if (!exists) {
                    this.data.settings.push({
                        id: this.generateId(),
                        key,
                        value,
                        description,
                        created_at: new Date().toISOString()
                    });
                }
            }

            // Insert sample products
            if (this.data.products.length === 0) {
                const sampleProducts = [
                    ['Coffee', 'Premium coffee blend', 4.99, 2.50, '1234567890123', 'Beverages'],
                    ['Tea', 'Herbal tea selection', 3.49, 1.75, '1234567890124', 'Beverages'],
                    ['Sandwich', 'Fresh sandwich', 7.99, 4.00, '1234567890125', 'Food'],
                    ['Pastry', 'Assorted pastries', 2.99, 1.50, '1234567890126', 'Food']
                ];

                for (const [name, description, price, cost, barcode, category] of sampleProducts) {
                    this.data.products.push({
                        id: this.generateId(),
                        name,
                        description,
                        price,
                        cost,
                        barcode,
                        category,
                        stock_quantity: 100,
                        min_stock_level: 5,
                        is_active: true,
                        created_at: new Date().toISOString()
                    });
                }
                log.info('Sample products inserted');
            }

            log.info('Default data insertion completed');
        } catch (error) {
            log.error('Error inserting default data:', error);
            throw error;
        }
    }

    /**
     * Get all records from a table
     */
    getAll(table) {
        return this.data[table] || [];
    }

    /**
     * Get a single record by ID
     */
    getById(table, id) {
        return this.data[table]?.find(record => record.id === id);
    }

    /**
     * Insert a new record
     */
    insert(table, record) {
        if (!this.data[table]) {
            this.data[table] = [];
        }
        
        record.id = record.id || this.generateId();
        record.created_at = record.created_at || new Date().toISOString();
        
        this.data[table].push(record);
        this.saveData();
        
        return record;
    }

    /**
     * Update a record
     */
    update(table, id, updates) {
        const index = this.data[table]?.findIndex(record => record.id === id);
        if (index !== -1) {
            this.data[table][index] = { ...this.data[table][index], ...updates, updated_at: new Date().toISOString() };
            this.saveData();
            return this.data[table][index];
        }
        return null;
    }

    /**
     * Delete a record
     */
    delete(table, id) {
        const index = this.data[table]?.findIndex(record => record.id === id);
        if (index !== -1) {
            const deleted = this.data[table].splice(index, 1)[0];
            this.saveData();
            return deleted;
        }
        return null;
    }

    /**
     * Find records by criteria
     */
    find(table, criteria) {
        return this.data[table]?.filter(record => {
            return Object.keys(criteria).every(key => record[key] === criteria[key]);
        }) || [];
    }

    /**
     * Get database statistics
     */
    getStats() {
        try {
            const stats = {
                users: { count: this.data.users.length },
                products: { count: this.data.products.length },
                sales: { count: this.data.sales.length },
                totalSales: { 
                    total: this.data.sales.reduce((sum, sale) => sum + (sale.total_amount || 0), 0) 
                },
                dbSize: this.getDbSize()
            };
            return stats;
        } catch (error) {
            log.error('Error getting database stats:', error);
            throw error;
        }
    }

    /**
     * Get database file size
     */
    getDbSize() {
        try {
            if (fs.existsSync(this.dbPath)) {
                const stats = fs.statSync(this.dbPath);
                return Math.round(stats.size / 1024); // Size in KB
            }
            return 0;
        } catch (error) {
            log.error('Error getting database size:', error);
            return 0;
        }
    }

    /**
     * Backup database
     */
    backup(backupPath) {
        try {
            if (!backupPath) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const userDataPath = app.getPath('userData');
                const backupDir = path.join(userDataPath, 'backups');
                
                if (!fs.existsSync(backupDir)) {
                    fs.mkdirSync(backupDir, { recursive: true });
                }
                
                backupPath = path.join(backupDir, `backup-${timestamp}.json`);
            }

            fs.copyFileSync(this.dbPath, backupPath);
            log.info('Database backup created:', backupPath);
            return backupPath;
        } catch (error) {
            log.error('Error creating backup:', error);
            throw error;
        }
    }

    /**
     * Close database connection
     */
    close() {
        this.saveData();
        log.info('Database closed');
    }
}

module.exports = DatabaseManager;
