import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Printer, FileText, Settings, TestTube, RefreshCw, Usb, Network } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

interface PrinterDevice {
  name: string;
  type: 'usb' | 'ip';
  address?: string;
  port?: string;
  status: 'connected' | 'disconnected' | 'unknown';
}

const PrinterSettings = () => {
  const { toast } = useToast();
  const { settings, updateSettings } = useSettings();
  const [availablePrinters, setAvailablePrinters] = useState<PrinterDevice[]>([]);
  const [isSearchingPrinters, setIsSearchingPrinters] = useState(false);
  const [ipAddress, setIpAddress] = useState('');
  const [ipPort, setIpPort] = useState('9100');
  const [connectionType, setConnectionType] = useState<'usb' | 'ip'>('usb');

  const detectUSBPrinters = async () => {
    try {
      const mockUSBPrinters: PrinterDevice[] = [
        { name: 'Epson TM-T20II (USB)', type: 'usb', status: 'connected' as const },
        { name: 'HP LaserJet Pro (USB)', type: 'usb', status: 'connected' as const },
        { name: 'Canon PIXMA (USB)', type: 'usb', status: 'disconnected' as const },
        { name: 'Brother HL-L2350DW (USB)', type: 'usb', status: 'connected' as const },
        { name: 'Samsung ML-2010 (USB)', type: 'usb', status: 'unknown' as const },
        { name: 'Microsoft Print to PDF', type: 'usb', status: 'connected' as const },
        { name: 'Microsoft XPS Document Writer', type: 'usb', status: 'connected' as const }
      ];
      return mockUSBPrinters;
    } catch (error) {
      console.error('Error detecting USB printers:', error);
      return [];
    }
  };

  const detectNetworkPrinters = async () => {
    try {
      // Simulate network printer detection
      const mockNetworkPrinters: PrinterDevice[] = [
        { name: 'Epson TM-T88V Network', type: 'ip', address: '*************', port: '9100', status: 'connected' as const },
        { name: 'HP LaserJet Network', type: 'ip', address: '*************', port: '9100', status: 'connected' as const },
        { name: 'Canon Network Printer', type: 'ip', address: '*************', port: '515', status: 'disconnected' as const }
      ];
      return mockNetworkPrinters;
    } catch (error) {
      console.error('Error detecting network printers:', error);
      return [];
    }
  };

  const detectPrinters = async () => {
    setIsSearchingPrinters(true);
    try {
      const [usbPrinters, networkPrinters] = await Promise.all([
        detectUSBPrinters(),
        detectNetworkPrinters()
      ]);
      
      const allPrinters = [...usbPrinters, ...networkPrinters];
      setAvailablePrinters(allPrinters);
      
      toast({
        title: "تم اكتشاف الطابعات",
        description: `تم العثور على ${allPrinters.length} طابعة (${usbPrinters.length} USB، ${networkPrinters.length} شبكة)`,
      });
    } catch (error) {
      toast({
        title: "خطأ في الاكتشاف",
        description: "فشل في اكتشاف الطابعات المتاحة",
        variant: "destructive"
      });
    } finally {
      setIsSearchingPrinters(false);
    }
  };

  const addIPPrinter = () => {
    if (!ipAddress) {
      toast({
        title: "عنوان IP مطلوب",
        description: "يرجى إدخال عنوان IP للطابعة",
        variant: "destructive"
      });
      return;
    }

    const newPrinter: PrinterDevice = {
      name: `طابعة شبكة (${ipAddress})`,
      type: 'ip',
      address: ipAddress,
      port: ipPort,
      status: 'unknown' as const
    };

    setAvailablePrinters(prev => [...prev, newPrinter]);
    setIpAddress('');
    setIpPort('9100');

    toast({
      title: "تم إضافة الطابعة",
      description: `تم إضافة طابعة الشبكة ${ipAddress}`,
    });
  };

  const testPrinterConnection = async (printer: PrinterDevice) => {
    try {
      // Simulate connection test
      const isConnected = Math.random() > 0.3; // 70% success rate for demo
      
      const updatedPrinters = availablePrinters.map(p => 
        p === printer ? { ...p, status: isConnected ? 'connected' as const : 'disconnected' as const } : p
      );
      setAvailablePrinters(updatedPrinters);

      toast({
        title: isConnected ? "الطابعة متصلة" : "فشل الاتصال",
        description: isConnected 
          ? `تم الاتصال بـ ${printer.name} بنجاح`
          : `فشل الاتصال بـ ${printer.name}`,
        variant: isConnected ? "default" : "destructive"
      });
    } catch (error) {
      toast({
        title: "خطأ في اختبار الاتصال",
        description: "حدث خطأ أثناء اختبار الاتصال بالطابعة",
        variant: "destructive"
      });
    }
  };

  useEffect(() => {
    detectPrinters();
  }, []);

  const handleSave = () => {
    toast({
      title: "تم الحفظ",
      description: "تم حفظ إعدادات الطباعة بنجاح",
    });
  };

  const handleTestPrint = async () => {
    if (!settings.printerName) {
      toast({
        title: "لم يتم اختيار طابعة",
        description: "يرجى اختيار طابعة أولاً",
        variant: "destructive"
      });
      return;
    }

    try {
      const testContent = `
        <div style="font-family: 'Courier New', monospace; font-size: 12px; max-width: 300px; margin: 0 auto;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="margin: 0;">فاتورة تجريبية</h2>
            <p style="margin: 5px 0;">Test Receipt</p>
          </div>
          <hr style="border: 1px dashed #000;">
          <div style="margin: 15px 0;">
            <p><strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-IQ')}</p>
            <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-IQ')}</p>
            <p><strong>الطابعة:</strong> ${settings.printerName}</p>
          </div>
          <hr style="border: 1px dashed #000;">
          <div style="margin: 15px 0;">
            <div style="display: flex; justify-content: space-between;">
              <span>منتج تجريبي × 1</span>
              <span>10,000 د.ع</span>
            </div>
            <div style="display: flex; justify-content: space-between;">
              <span>منتج آخر × 2</span>
              <span>20,000 د.ع</span>
            </div>
          </div>
          <hr style="border: 1px dashed #000;">
          <div style="margin: 15px 0;">
            <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 14px;">
              <span>المجموع:</span>
              <span>30,000 د.ع</span>
            </div>
          </div>
          <hr style="border: 1px dashed #000;">
          <div style="text-align: center; margin-top: 20px;">
            <p style="margin: 5px 0;">شكراً لزيارتكم</p>
            <p style="margin: 5px 0;">Thank you for your visit</p>
            <p style="margin: 5px 0; font-size: 10px;">اختبار الطباعة - ${new Date().toLocaleString()}</p>
          </div>
        </div>
      `;

      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>Test Print - ${settings.printerName}</title>
            <style>
              @media print {
                body { margin: 0; }
              }
            </style>
          </head>
          <body>
            ${testContent}
          </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
      }

      toast({
        title: "طباعة تجريبية",
        description: `تم إرسال فاتورة تجريبية للطابعة ${settings.printerName}`,
      });
    } catch (error) {
      toast({
        title: "خطأ في الطباعة",
        description: "فشل في إرسال الطباعة التجريبية",
        variant: "destructive"
      });
    }
  };

  const handleInputChange = (field: string, value: string | boolean | number) => {
    updateSettings({ [field]: value });
  };

  const getStatusColor = (status: PrinterDevice['status']) => {
    switch (status) {
      case 'connected': return 'text-green-600';
      case 'disconnected': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusText = (status: PrinterDevice['status']) => {
    switch (status) {
      case 'connected': return 'متصل';
      case 'disconnected': return 'غير متصل';
      default: return 'غير معروف';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Printer className="w-5 h-5" />
            إعدادات الطابعة
          </CardTitle>
          <CardDescription>
            تكوين الطابعة وإعدادات الطباعة (USB و IP)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Tabs value={connectionType} onValueChange={(value) => setConnectionType(value as 'usb' | 'ip')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="usb" className="flex items-center gap-2">
                <Usb className="w-4 h-4" />
                طابعات USB
              </TabsTrigger>
              <TabsTrigger value="ip" className="flex items-center gap-2">
                <Network className="w-4 h-4" />
                طابعات الشبكة
              </TabsTrigger>
            </TabsList>

            <TabsContent value="usb" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">الطابعات المتصلة عبر USB</h3>
                <Button
                  onClick={detectPrinters}
                  variant="outline"
                  size="sm"
                  disabled={isSearchingPrinters}
                >
                  <RefreshCw className={`w-4 h-4 ml-2 ${isSearchingPrinters ? 'animate-spin' : ''}`} />
                  {isSearchingPrinters ? 'جاري البحث...' : 'تحديث'}
                </Button>
              </div>

              <div className="space-y-2">
                {availablePrinters
                  .filter(printer => printer.type === 'usb')
                  .map((printer, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Usb className="w-5 h-5 text-blue-600" />
                        <div>
                          <p className="font-medium">{printer.name}</p>
                          <p className={`text-sm ${getStatusColor(printer.status)}`}>
                            {getStatusText(printer.status)}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          onClick={() => testPrinterConnection(printer)}
                          variant="outline"
                          size="sm"
                        >
                          اختبار
                        </Button>
                        <Button
                          onClick={() => handleInputChange('printerName', printer.name)}
                          variant={settings.printerName === printer.name ? "default" : "outline"}
                          size="sm"
                        >
                          {settings.printerName === printer.name ? 'مختارة' : 'اختيار'}
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </TabsContent>

            <TabsContent value="ip" className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-4">طابعات الشبكة (IP)</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 border rounded-lg bg-muted/50">
                  <div className="space-y-2">
                    <Label htmlFor="ipAddress">عنوان IP</Label>
                    <Input
                      id="ipAddress"
                      value={ipAddress}
                      onChange={(e) => setIpAddress(e.target.value)}
                      placeholder="*************"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ipPort">المنفذ</Label>
                    <Input
                      id="ipPort"
                      value={ipPort}
                      onChange={(e) => setIpPort(e.target.value)}
                      placeholder="9100"
                    />
                  </div>
                  <div className="flex items-end">
                    <Button onClick={addIPPrinter} className="w-full">
                      إضافة طابعة
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                {availablePrinters
                  .filter(printer => printer.type === 'ip')
                  .map((printer, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Network className="w-5 h-5 text-purple-600" />
                        <div>
                          <p className="font-medium">{printer.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {printer.address}:{printer.port}
                          </p>
                          <p className={`text-sm ${getStatusColor(printer.status)}`}>
                            {getStatusText(printer.status)}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          onClick={() => testPrinterConnection(printer)}
                          variant="outline"
                          size="sm"
                        >
                          اختبار
                        </Button>
                        <Button
                          onClick={() => handleInputChange('printerName', printer.name)}
                          variant={settings.printerName === printer.name ? "default" : "outline"}
                          size="sm"
                        >
                          {settings.printerName === printer.name ? 'مختارة' : 'اختيار'}
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </TabsContent>
          </Tabs>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>الطابعة المختارة</Label>
              <Input
                value={settings.printerName || 'لم يتم اختيار طابعة'}
                readOnly
                className="bg-muted"
              />
            </div>
            <div className="space-y-2">
              <Label>حجم الورق</Label>
              <Select value={settings.paperSize} onValueChange={(value) => handleInputChange('paperSize', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="58mm">58mm</SelectItem>
                  <SelectItem value="80mm">80mm</SelectItem>
                  <SelectItem value="A4">A4</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>حجم الخط</Label>
              <Select value={settings.fontSize} onValueChange={(value) => handleInputChange('fontSize', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">صغير</SelectItem>
                  <SelectItem value="medium">متوسط</SelectItem>
                  <SelectItem value="large">كبير</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="copies">عدد النسخ</Label>
              <Input
                id="copies"
                type="number"
                value={settings.copies}
                onChange={(e) => handleInputChange('copies', parseInt(e.target.value))}
                min="1"
                max="5"
                className="w-20"
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>الطباعة التلقائية</Label>
                <p className="text-sm text-muted-foreground">
                  طباعة الفاتورة تلقائياً بعد إتمام البيع
                </p>
              </div>
              <Switch
                checked={settings.autoPrint}
                onCheckedChange={(checked) => handleInputChange('autoPrint', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>طباعة الشعار</Label>
                <p className="text-sm text-muted-foreground">
                  إضافة شعار المتجر في أعلى الفاتورة
                </p>
              </div>
              <Switch
                checked={settings.printLogo}
                onCheckedChange={(checked) => handleInputChange('printLogo', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>طباعة رمز QR</Label>
                <p className="text-sm text-muted-foreground">
                  إضافة رمز QR في أسفل الفاتورة
                </p>
              </div>
              <Switch
                checked={settings.printQR}
                onCheckedChange={(checked) => handleInputChange('printQR', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            تخصيص الفاتورة
          </CardTitle>
          <CardDescription>
            تخصيص شكل ومحتوى الفاتورة المطبوعة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="receiptFooter">نص نهاية الفاتورة</Label>
            <Input
              id="receiptFooter"
              value={settings.receiptFooter}
              onChange={(e) => handleInputChange('receiptFooter', e.target.value)}
              className="text-right"
              placeholder="رسالة شكر أو معلومات إضافية"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button variant="outline" onClick={handleTestPrint} className="flex items-center gap-2">
          <TestTube className="w-4 h-4" />
          طباعة تجريبية
        </Button>
        <Button onClick={handleSave} className="flex items-center gap-2">
          <Settings className="w-4 h-4" />
          حفظ الإعدادات
        </Button>
      </div>
    </div>
  );
};

export default PrinterSettings;
