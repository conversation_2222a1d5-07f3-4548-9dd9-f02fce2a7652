
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Shield, Save } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';

const SecuritySettings = () => {
  const { toast } = useToast();
  const { settings, updateSettings, t } = useSettings();

  const handleSave = () => {
    toast({
      title: t('saved'),
      description: "تم حفظ إعدادات الأمان بنجاح",
    });
  };

  const handleInputChange = (field: string, value: string | boolean | number) => {
    updateSettings({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            {t('security_settings')}
          </CardTitle>
          <CardDescription>
            {t('security_settings_desc')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">{t('session_timeout')}</Label>
              <Input
                id="sessionTimeout"
                type="number"
                value={settings.sessionTimeout}
                onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
                min="5"
                max="120"
                className="w-24"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="passwordMinLength">{t('password_min_length')}</Label>
              <Input
                id="passwordMinLength"
                type="number"
                value={settings.passwordMinLength}
                onChange={(e) => handleInputChange('passwordMinLength', parseInt(e.target.value))}
                min="4"
                max="20"
                className="w-24"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="loginAttempts">{t('login_attempts')}</Label>
              <Input
                id="loginAttempts"
                type="number"
                value={settings.loginAttempts}
                onChange={(e) => handleInputChange('loginAttempts', parseInt(e.target.value))}
                min="3"
                max="10"
                className="w-24"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lockoutDuration">{t('lockout_duration')}</Label>
              <Input
                id="lockoutDuration"
                type="number"
                value={settings.lockoutDuration}
                onChange={(e) => handleInputChange('lockoutDuration', parseInt(e.target.value))}
                min="5"
                max="60"
                className="w-24"
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>{t('two_factor_auth')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('two_factor_desc')}
                </p>
              </div>
              <Switch
                checked={settings.enableTwoFactor}
                onCheckedChange={(checked) => handleInputChange('enableTwoFactor', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>{t('require_password_change')}</Label>
                <p className="text-sm text-muted-foreground">
                  {t('require_password_desc')}
                </p>
              </div>
              <Switch
                checked={settings.requirePasswordChange}
                onCheckedChange={(checked) => handleInputChange('requirePasswordChange', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} className="flex items-center gap-2">
          <Save className="w-4 h-4" />
          {t('save_settings')}
        </Button>
      </div>
    </div>
  );
};

export default SecuritySettings;
