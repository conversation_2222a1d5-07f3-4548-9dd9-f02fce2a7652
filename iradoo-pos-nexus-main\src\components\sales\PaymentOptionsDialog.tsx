
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CreditCard, Wallet, Calendar, User } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';
import { Customer } from '@/utils/database';
import WhatsAppSender from './WhatsAppSender';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  total: number;
}

interface PaymentOptionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cartItems: CartItem[];
  cartTotal: number;
  customers: Customer[];
  onPaymentComplete: (paymentData: PaymentData) => void;
}

export interface PaymentData {
  type: 'cash' | 'debt' | 'installment';
  customer_id?: string;
  down_payment?: number;
  months?: number;
  monthly_payment?: number;
  notes?: string;
}

const PaymentOptionsDialog: React.FC<PaymentOptionsDialogProps> = ({
  open,
  onOpenChange,
  cartItems,
  cartTotal,
  customers,
  onPaymentComplete
}) => {
  const { formatCurrency } = useSettings();
  const [selectedCustomer, setSelectedCustomer] = useState<string>('');
  const [downPayment, setDownPayment] = useState<number>(0);
  const [months, setMonths] = useState<number>(1);
  const [notes, setNotes] = useState<string>('');
  const [showWhatsAppSender, setShowWhatsAppSender] = useState(false);
  const [completedPaymentData, setCompletedPaymentData] = useState<PaymentData | null>(null);

  const monthlyPayment = months > 0 ? (cartTotal - downPayment) / months : 0;
  const selectedCustomerData = customers.find(c => c.id === selectedCustomer);

  const handleCashPayment = () => {
    onPaymentComplete({
      type: 'cash'
    });
  };

  const handleDebtPayment = () => {
    if (!selectedCustomer) return;
    
    const paymentData: PaymentData = {
      type: 'debt',
      customer_id: selectedCustomer,
      notes
    };
    
    setCompletedPaymentData(paymentData);
    setShowWhatsAppSender(true);
    onPaymentComplete(paymentData);
  };

  const handleInstallmentPayment = () => {
    if (!selectedCustomer || months < 1) return;
    
    const paymentData: PaymentData = {
      type: 'installment',
      customer_id: selectedCustomer,
      down_payment: downPayment,
      months,
      monthly_payment: monthlyPayment,
      notes
    };
    
    setCompletedPaymentData(paymentData);
    setShowWhatsAppSender(true);
    onPaymentComplete(paymentData);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            اختيار طريقة الدفع
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Cart Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">ملخص الطلب</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <span>{item.name} × {item.quantity}</span>
                    <span className="font-semibold">{formatCurrency(item.total)}</span>
                  </div>
                ))}
                <div className="border-t pt-2 flex justify-between font-bold text-lg">
                  <span>الإجمالي:</span>
                  <span className="text-blue-600">{formatCurrency(cartTotal)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* WhatsApp Sender */}
          {showWhatsAppSender && completedPaymentData && selectedCustomerData && (
            <Card className="bg-green-50 border-green-200">
              <CardHeader>
                <CardTitle className="text-green-700">إرسال رسالة واتساب للعميل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-green-600">
                    يمكنك الآن إرسال رسالة تأكيد للعميل عبر الواتساب
                  </p>
                  <WhatsAppSender
                    phoneNumber={selectedCustomerData.phone}
                    customerName={selectedCustomerData.name}
                    amount={cartTotal}
                    paymentType={completedPaymentData.type as 'debt' | 'installment'}
                    installmentDetails={completedPaymentData.type === 'installment' ? {
                      downPayment: completedPaymentData.down_payment || 0,
                      monthlyPayment: completedPaymentData.monthly_payment || 0,
                      months: completedPaymentData.months || 1
                    } : undefined}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Options */}
          <Tabs defaultValue="cash" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="cash" className="flex items-center gap-2">
                <Wallet className="w-4 h-4" />
                نقدي
              </TabsTrigger>
              <TabsTrigger value="debt" className="flex items-center gap-2">
                <CreditCard className="w-4 h-4" />
                دين
              </TabsTrigger>
              <TabsTrigger value="installment" className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                أقساط
              </TabsTrigger>
            </TabsList>

            <TabsContent value="cash" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-green-600">الدفع النقدي</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600">سيتم إتمام البيع فوراً بالمبلغ الكامل نقداً</p>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 mb-4">
                      المبلغ المطلوب: {formatCurrency(cartTotal)}
                    </div>
                    <Button 
                      onClick={handleCashPayment}
                      className="w-full h-12 bg-green-500 hover:bg-green-600 text-lg"
                    >
                      إتمام الدفع النقدي
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="debt" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-orange-600">البيع بالدين</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="debt-customer">اختيار العميل</Label>
                      <Select value={selectedCustomer} onValueChange={setSelectedCustomer}>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر العميل" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              <div className="flex items-center gap-2">
                                <User className="w-4 h-4" />
                                {customer.name} - {customer.phone}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="debt-notes">ملاحظات</Label>
                      <Textarea
                        id="debt-notes"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        placeholder="ملاحظات إضافية..."
                        rows={3}
                      />
                    </div>

                    <div className="bg-orange-50 p-4 rounded-lg">
                      <div className="text-lg font-semibold text-orange-800">
                        مبلغ الدين: {formatCurrency(cartTotal)}
                      </div>
                      <p className="text-sm text-orange-600 mt-1">
                        سيتم إضافة هذا المبلغ كدين على العميل المحدد
                      </p>
                    </div>

                    <Button 
                      onClick={handleDebtPayment}
                      disabled={!selectedCustomer}
                      className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-lg"
                    >
                      تسجيل البيع بالدين
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="installment" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-blue-600">البيع بالأقساط</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="installment-customer">اختيار العميل</Label>
                      <Select value={selectedCustomer} onValueChange={setSelectedCustomer}>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر العميل" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              <div className="flex items-center gap-2">
                                <User className="w-4 h-4" />
                                {customer.name} - {customer.phone}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="down-payment">المقدم</Label>
                        <Input
                          id="down-payment"
                          type="number"
                          value={downPayment}
                          onChange={(e) => setDownPayment(Number(e.target.value))}
                          min={0}
                          max={cartTotal}
                          className="text-center"
                        />
                      </div>
                      <div>
                        <Label htmlFor="months">عدد الأشهر</Label>
                        <Input
                          id="months"
                          type="number"
                          value={months}
                          onChange={(e) => setMonths(Number(e.target.value))}
                          min={1}
                          max={60}
                          className="text-center"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="installment-notes">ملاحظات</Label>
                      <Textarea
                        id="installment-notes"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        placeholder="ملاحظات إضافية..."
                        rows={3}
                      />
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg space-y-2">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">إجمالي المبلغ:</span>
                          <div className="font-semibold">{formatCurrency(cartTotal)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">المقدم:</span>
                          <div className="font-semibold">{formatCurrency(downPayment)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">المتبقي:</span>
                          <div className="font-semibold">{formatCurrency(cartTotal - downPayment)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">القسط الشهري:</span>
                          <div className="font-semibold text-blue-600">{formatCurrency(monthlyPayment)}</div>
                        </div>
                      </div>
                    </div>

                    <Button 
                      onClick={handleInstallmentPayment}
                      disabled={!selectedCustomer || months < 1}
                      className="w-full h-12 bg-blue-500 hover:bg-blue-600 text-lg"
                    >
                      تسجيل البيع بالأقساط
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentOptionsDialog;

