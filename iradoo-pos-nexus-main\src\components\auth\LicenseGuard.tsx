
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Shield, AlertTriangle, CheckCircle, Key } from 'lucide-react';
import { licenseManager, LicenseInfo } from '@/utils/licenseManager';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import EmailLicenseLogin from './EmailLicenseLogin';

interface LicenseGuardProps {
  children: React.ReactNode;
}

const LicenseGuard: React.FC<LicenseGuardProps> = ({ children }) => {
  const { toast } = useToast();
  const [licenseStatus, setLicenseStatus] = useState<LicenseInfo | null>(null);
  const [licenseKey, setLicenseKey] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [deviceInfo, setDeviceInfo] = useState({ macAddress: '', deviceName: '' });

  useEffect(() => {
    checkLicense();
    loadDeviceInfo();
  }, []);

  const checkLicense = () => {
    setIsLoading(true);
    
    // التحقق أولاً من كون المستخدم مطور معفي
    if (licenseManager.isDeveloperExempt()) {
      console.log('Developer exemption detected, bypassing license check');
      setLicenseStatus({
        key: 'DEVELOPER-EXEMPT',
        data: {
          customerName: 'Developer Account',
          macAddress: '',
          deviceName: '',
          features: ['sales', 'inventory', 'reports', 'customers', 'backup', 'multiUser'],
          issuedDate: new Date().toISOString(),
          serialNumber: 'DEV-EXEMPT'
        },
        isValid: true
      });
      setIsLoading(false);
      return;
    }

    const status = licenseManager.getCurrentLicenseStatus();
    setLicenseStatus(status);
    setIsLoading(false);
  };

  const loadDeviceInfo = () => {
    const info = licenseManager.getCurrentDeviceInfo();
    setDeviceInfo(info);
  };

  const handleActivateLicense = () => {
    if (!licenseKey.trim()) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال مفتاح الترخيص',
        variant: 'destructive',
      });
      return;
    }

    const success = licenseManager.saveLicense(licenseKey.trim());
    
    if (success) {
      toast({
        title: 'تم تفعيل الترخيص',
        description: 'تم تفعيل النظام بنجاح',
      });
      setLicenseKey('');
      checkLicense();
    } else {
      const validation = licenseManager.validateLicense(licenseKey.trim());
      toast({
        title: 'فشل التفعيل',
        description: validation.error || 'مفتاح الترخيص غير صالح',
        variant: 'destructive',
      });
    }
  };

  const handleEmailLoginSuccess = () => {
    checkLicense();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>جاري التحقق من الترخيص...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // إذا كان الترخيص صالح أو المستخدم مطور معفي، عرض التطبيق
  if (licenseStatus?.isValid) {
    return <>{children}</>;
  }

  // إذا لم يكن هناك ترخيص أو كان غير صالح، عرض صفحة التفعيل
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-6">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Shield className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">تفعيل نظام أريدوو POS</h1>
          <p className="text-gray-600 text-lg">اختر طريقة التفعيل المناسبة لك</p>
        </div>

        <Tabs defaultValue="email" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="email">تسجيل دخول بالإيميل</TabsTrigger>
            <TabsTrigger value="license">مفتاح الترخيص</TabsTrigger>
          </TabsList>

          <TabsContent value="email">
            <EmailLicenseLogin onSuccess={handleEmailLoginSuccess} />
          </TabsContent>

          <TabsContent value="license" className="space-y-6">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-xl">تفعيل بمفتاح الترخيص</CardTitle>
                <CardDescription>
                  أدخل مفتاح الترخيص الخاص بك لتفعيل النظام
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* رسالة الخطأ إذا وجدت */}
                {licenseStatus?.error && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {licenseStatus.error}
                    </AlertDescription>
                  </Alert>
                )}

                {/* معلومات الجهاز */}
                <Card className="bg-muted/50">
                  <CardHeader>
                    <CardTitle className="text-lg">معلومات هذا الجهاز</CardTitle>
                    <CardDescription>
                      أرسل هذه المعلومات للحصول على مفتاح ترخيص
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium">معرف الجهاز:</Label>
                      <p className="font-mono text-xs bg-white p-2 rounded border mt-1 break-all">
                        {deviceInfo.macAddress}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">اسم الجهاز:</Label>
                      <p className="font-mono text-xs bg-white p-2 rounded border mt-1">
                        {deviceInfo.deviceName}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* إدخال مفتاح الترخيص */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="license-input" className="text-base font-medium">
                      مفتاح الترخيص
                    </Label>
                    <Textarea
                      id="license-input"
                      placeholder="ARIDOO-..."
                      value={licenseKey}
                      onChange={(e) => setLicenseKey(e.target.value)}
                      className="min-h-[100px] font-mono text-sm"
                    />
                  </div>
                  
                  <Button onClick={handleActivateLicense} className="w-full" size="lg">
                    <Key className="w-5 h-5 mr-2" />
                    تفعيل النظام
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* معلومات إضافية */}
        <Alert className="mt-6">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            للحصول على مفتاح الترخيص، تواصل مع فريق الدعم وقم بإرسال معلومات الجهاز أعلاه.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
};

export default LicenseGuard;
