
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Clean Light/Dark Design System */

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 221.2 83.2% 53.3%;
  }

  .dark {
    /* Dark theme colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.0%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  /* Arabic and Kurdish font support */
  [dir="rtl"] {
    font-family: 'Inter', 'Noto Sans Arabic', 'Cairo', 'Amiri', 'Scheherazade New', Arial, sans-serif;
  }

  /* Kurdish specific font support */
  [lang="ku"], .kurdish-text {
    font-family: 'Noto Sans Arabic', 'Amiri', 'Scheherazade New', 'Arial Unicode MS', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: 0.02em;
  }

  /* Kurdish headings */
  [lang="ku"] h1, [lang="ku"] h2, [lang="ku"] h3, [lang="ku"] h4, [lang="ku"] h5, [lang="ku"] h6,
  .kurdish-text h1, .kurdish-text h2, .kurdish-text h3, .kurdish-text h4, .kurdish-text h5, .kurdish-text h6 {
    font-family: 'Amiri', 'Noto Sans Arabic', 'Scheherazade New', Arial, sans-serif;
    font-weight: 600;
    line-height: 1.4;
  }

  /* Clean scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
}

@layer components {
  /* Simple card styles */
  .modern-card {
    @apply bg-card border border-border rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  .modern-card-hover {
    @apply modern-card hover:scale-[1.01] hover:border-primary/20;
  }

  /* Simple animations */
  .animate-fade-in {
    animation: fadeInUp 0.5s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.4s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  /* Clean button styles */
  .btn-gradient {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md;
  }

  /* Status indicators */
  .status-dot {
    @apply w-2 h-2 rounded-full;
  }

  .status-active {
    @apply bg-green-500;
  }

  .status-pending {
    @apply bg-yellow-500;
  }

  .status-inactive {
    @apply bg-gray-500;
  }

  .status-error {
    @apply bg-red-500;
  }

  /* Clean input styling */
  .modern-input {
    @apply bg-background border-border focus:border-primary focus:ring-1 focus:ring-primary transition-colors duration-200;
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-lg;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* RTL Support */
[dir="rtl"] .ml-2 { margin-right: 0.5rem; margin-left: 0; }
[dir="rtl"] .mr-2 { margin-left: 0.5rem; margin-right: 0; }
[dir="rtl"] .pl-3 { padding-right: 0.75rem; padding-left: 0; }
[dir="rtl"] .pr-3 { padding-left: 0.75rem; padding-right: 0; }
[dir="rtl"] .ml-4 { margin-right: 1rem; margin-left: 0; }
[dir="rtl"] .mr-4 { margin-left: 1rem; margin-right: 0; }
