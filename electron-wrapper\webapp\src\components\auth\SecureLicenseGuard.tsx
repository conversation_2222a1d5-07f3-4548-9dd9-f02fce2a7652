
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Shield, AlertTriangle, CheckCircle, Key } from 'lucide-react';
import { SecureLicenseInfo, secureLicenseManager } from '@/utils/secureLicenseManager';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSecureLocalAuth } from '@/hooks/useSecureLocalAuth';

interface SecureLicenseGuardProps {
  children: React.ReactNode;
}

const SecureLicenseGuard: React.FC<SecureLicenseGuardProps> = ({ children }) => {
  const { toast } = useToast();
  const { user, isAdmin } = useSecureLocalAuth();
  const [licenseStatus, setLicenseStatus] = useState<SecureLicenseInfo | null>(null);
  const [licenseKey, setLicenseKey] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkLicense();
  }, [user]);

  const checkLicense = async () => {
    setIsLoading(true);
    
    try {
      const status = await secureLicenseManager.getCurrentLicenseStatus();
      setLicenseStatus(status);
    } catch (error) {
      console.error('License check error:', error);
      setLicenseStatus({ isValid: false, error: 'License check failed' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleActivateLicense = async () => {
    if (!licenseKey.trim()) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال مفتاح الترخيص',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    
    try {
      const validation = await secureLicenseManager.validateLicense(licenseKey.trim());
      
      if (validation.isValid) {
        secureLicenseManager.saveLicense(licenseKey.trim());
        toast({
          title: 'تم تفعيل الترخيص',
          description: 'تم تفعيل النظام بنجاح',
        });
        setLicenseKey('');
        await checkLicense();
      } else {
        toast({
          title: 'فشل التفعيل',
          description: validation.error || 'مفتاح الترخيص غير صالح',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء التحقق من الترخيص',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span>جاري التحقق من الترخيص...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If license is valid or user is authenticated admin, show app
  if (licenseStatus?.isValid || (isAdmin && user?.email === '<EMAIL>')) {
    return <>{children}</>;
  }

  // Show activation page
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-6">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Shield className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">تفعيل نظام أريدوو POS</h1>
          <p className="text-gray-600 text-lg">أدخل مفتاح الترخيص لتفعيل النظام</p>
        </div>

        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-xl">تفعيل بمفتاح الترخيص</CardTitle>
            <CardDescription>
              أدخل مفتاح الترخيص الخاص بك لتفعيل النظام
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {licenseStatus?.error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {licenseStatus.error}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="license-input" className="text-base font-medium">
                  مفتاح الترخيص
                </Label>
                <Textarea
                  id="license-input"
                  placeholder="ARIDOO-..."
                  value={licenseKey}
                  onChange={(e) => setLicenseKey(e.target.value)}
                  className="min-h-[100px] font-mono text-sm"
                />
              </div>
              
              <Button 
                onClick={handleActivateLicense} 
                className="w-full" 
                size="lg"
                disabled={isLoading}
              >
                <Key className="w-5 h-5 mr-2" />
                {isLoading ? 'جاري التحقق...' : 'تفعيل النظام'}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Alert className="mt-6">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            للحصول على مفتاح الترخيص، تواصل مع فريق الدعم.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
};

export default SecureLicenseGuard;
