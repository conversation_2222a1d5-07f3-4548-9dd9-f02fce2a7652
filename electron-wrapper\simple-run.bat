@echo off
chcp 65001 >nul 2>&1
title Iradoo POS Desktop - Simple Manager
cls

:menu
echo ========================================
echo    Iradoo POS Desktop - Simple Manager
echo ========================================
echo.
echo Choose an option:
echo.
echo [1] Check Node.js installation
echo [2] Install dependencies (setup)
echo [3] Copy web app files
echo [4] Start application (test)
echo [5] Build application (create installer)
echo [6] Open project folder
echo [7] Open distribution folder
echo [0] Exit
echo.
echo ========================================
set /p choice=Enter your choice (0-7): 

if "%choice%"=="1" goto check_nodejs
if "%choice%"=="2" goto setup
if "%choice%"=="3" goto copy_webapp
if "%choice%"=="4" goto start_app
if "%choice%"=="5" goto build_app
if "%choice%"=="6" goto open_project
if "%choice%"=="7" goto open_dist
if "%choice%"=="0" goto exit
goto invalid_choice

:check_nodejs
cls
echo Running Node.js check...
call check-nodejs.bat
goto pause_and_menu

:setup
cls
echo Running setup...
call setup.bat
goto pause_and_menu

:copy_webapp
cls
echo Copying web app files...
call copy-webapp.bat
goto pause_and_menu

:start_app
cls
echo Starting application...
call start.bat
goto pause_and_menu

:build_app
cls
echo Building application...
call build.bat
goto pause_and_menu

:open_project
cls
echo Opening project folder...
start explorer .
echo Project folder opened
goto pause_and_menu

:open_dist
cls
if exist "dist" (
    echo Opening distribution folder...
    start explorer dist
    echo Distribution folder opened
) else (
    echo Distribution folder does not exist!
    echo Please build the application first (option 5)
)
goto pause_and_menu

:invalid_choice
cls
echo Invalid choice! Please choose a number from 0 to 7
goto pause_and_menu

:pause_and_menu
echo.
echo Press any key to return to main menu...
pause >nul
cls
goto menu

:exit
cls
echo Thank you for using Iradoo POS Desktop Manager!
echo.
timeout /t 2 >nul
exit
