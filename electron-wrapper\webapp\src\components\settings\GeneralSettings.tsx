
import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Save, Store, Globe } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const GeneralSettings = () => {
  const { toast } = useToast();
  const { settings, updateSettings, t } = useSettings();

  // Force re-render when settings change
  useEffect(() => {
    const handleSettingsChange = () => {
      // This will trigger a re-render
    };

    window.addEventListener('settings-changed', handleSettingsChange);
    return () => window.removeEventListener('settings-changed', handleSettingsChange);
  }, []);

  const handleSave = () => {
    toast({
      title: t('saved'),
      description: t('general_settings_saved'),
    });
  };

  const handleInputChange = (field: string, value: string | boolean | number) => {
    updateSettings({ [field]: value });
    
    // Show immediate feedback for language and currency changes
    if (field === 'language' || field === 'currency' || field === 'currencySymbol') {
      setTimeout(() => {
        toast({
          title: t('applied'),
          description: t('changes_applied'),
        });
      }, 500);
    }
  };

  const languages = [
    { code: 'ar', name: 'العربية', dir: 'rtl' },
    { code: 'ku', name: 'کوردی سۆرانی', dir: 'rtl' },
    { code: 'en', name: 'English', dir: 'ltr' }
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="w-5 h-5" />
            {t('store_information')}
          </CardTitle>
          <CardDescription>
            {t('store_information_desc')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="storeName">{t('store_name')}</Label>
              <Input
                id="storeName"
                value={settings.storeName}
                onChange={(e) => handleInputChange('storeName', e.target.value)}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="storePhone">{t('phone_number')}</Label>
              <Input
                id="storePhone"
                value={settings.storePhone}
                onChange={(e) => handleInputChange('storePhone', e.target.value)}
                className="text-right"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="storeAddress">{t('address')}</Label>
            <Textarea
              id="storeAddress"
              value={settings.storeAddress}
              onChange={(e) => handleInputChange('storeAddress', e.target.value)}
              className="text-right"
              rows={3}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="storeEmail">{t('email')}</Label>
              <Input
                id="storeEmail"
                type="email"
                value={settings.storeEmail}
                onChange={(e) => handleInputChange('storeEmail', e.target.value)}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="taxNumber">{t('tax_number')}</Label>
              <Input
                id="taxNumber"
                value={settings.taxNumber}
                onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                className="text-right"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            {t('system_settings')}
          </CardTitle>
          <CardDescription>
            {t('system_settings_desc')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label>{t('system_language')}</Label>
            <Select value={settings.language} onValueChange={(value: 'ar' | 'ku' | 'en') => handleInputChange('language', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>{t('auto_backup')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('auto_backup_desc')}
              </p>
            </div>
            <Switch
              checked={settings.autoBackup}
              onCheckedChange={(checked) => handleInputChange('autoBackup', checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>{t('low_stock_alert')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('low_stock_alert_desc')}
              </p>
            </div>
            <Switch
              checked={settings.lowStockAlert}
              onCheckedChange={(checked) => handleInputChange('lowStockAlert', checked)}
            />
          </div>

          {settings.lowStockAlert && (
            <div className="space-y-2">
              <Label htmlFor="lowStockThreshold">{t('stock_alert_threshold')}</Label>
              <Input
                id="lowStockThreshold"
                type="number"
                value={settings.lowStockThreshold}
                onChange={(e) => handleInputChange('lowStockThreshold', parseInt(e.target.value))}
                className="w-32"
                min="1"
              />
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} className="flex items-center gap-2">
          <Save className="w-4 h-4" />
          {t('save_settings')}
        </Button>
      </div>
    </div>
  );
};

export default GeneralSettings;
