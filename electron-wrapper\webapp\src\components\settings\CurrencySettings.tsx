import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { DollarSign, Percent, Calculator, Save } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const CurrencySettings = () => {
  const { toast } = useToast();
  const { settings, updateSettings, formatCurrency } = useSettings();

  const currencies = [
    { code: 'IQD', name: 'دينار عراقي', symbol: 'د.ع' },
    { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
    { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
    { code: 'EUR', name: 'يورو', symbol: '€' },
    { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ' },
    { code: 'KWD', name: 'دينار كويتي', symbol: 'د.ك' }
  ];

  const handleSave = () => {
    toast({
      title: "تم الحفظ",
      description: "تم حفظ إعدادات العملة بنجاح",
    });
  };

  const handleInputChange = (field: string, value: string | boolean | number) => {
    updateSettings({ [field]: value });
  };

  const handleCurrencyChange = (currencyCode: string) => {
    const currency = currencies.find(c => c.code === currencyCode);
    if (currency) {
      updateSettings({
        currency: currency.code,
        currencySymbol: currency.symbol
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            إعدادات العملة
          </CardTitle>
          <CardDescription>
            تكوين العملة الأساسية وتنسيق الأسعار
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>العملة الأساسية</Label>
              <Select value={settings.currency} onValueChange={handleCurrencyChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.code} value={currency.code}>
                      {currency.name} ({currency.symbol})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="currencySymbol">رمز العملة</Label>
              <Input
                id="currencySymbol"
                value={settings.currencySymbol}
                onChange={(e) => handleInputChange('currencySymbol', e.target.value)}
                className="text-center"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>موضع العملة</Label>
              <Select value={settings.currencyPosition} onValueChange={(value) => handleInputChange('currencyPosition', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="before">قبل الرقم</SelectItem>
                  <SelectItem value="after">بعد الرقم</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="decimalPlaces">عدد المنازل العشرية</Label>
              <Input
                id="decimalPlaces"
                type="number"
                value={settings.decimalPlaces}
                onChange={(e) => handleInputChange('decimalPlaces', parseInt(e.target.value))}
                min="0"
                max="4"
                className="w-20"
              />
            </div>
            <div className="space-y-2">
              <Label>طريقة التقريب</Label>
              <Select value={settings.roundingMethod} onValueChange={(value) => handleInputChange('roundingMethod', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="round">تقريب عادي</SelectItem>
                  <SelectItem value="floor">تقريب لأسفل</SelectItem>
                  <SelectItem value="ceil">تقريب لأعلى</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="thousandSeparator">فاصل الآلاف</Label>
              <Input
                id="thousandSeparator"
                value={settings.thousandSeparator}
                onChange={(e) => handleInputChange('thousandSeparator', e.target.value)}
                className="w-16 text-center"
                maxLength={1}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="decimalSeparator">الفاصل العشري</Label>
              <Input
                id="decimalSeparator"
                value={settings.decimalSeparator}
                onChange={(e) => handleInputChange('decimalSeparator', e.target.value)}
                className="w-16 text-center"
                maxLength={1}
              />
            </div>
          </div>

          <Card className="bg-gray-50">
            <CardHeader>
              <CardTitle className="text-sm">معاينة التنسيق</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p>مثال على سعر بسيط: <span className="font-mono font-bold">{formatCurrency(1234.56)}</span></p>
                <p>مثال على سعر كبير: <span className="font-mono font-bold">{formatCurrency(123456.78)}</span></p>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Percent className="w-5 h-5" />
            إعدادات الضريبة
          </CardTitle>
          <CardDescription>
            تكوين الضريبة وطريقة حساب القيمة المضافة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>تفعيل الضريبة</Label>
              <p className="text-sm text-muted-foreground">
                إضافة الضريبة تلقائياً على جميع المبيعات
              </p>
            </div>
            <Switch
              checked={settings.taxEnabled}
              onCheckedChange={(checked) => handleInputChange('taxEnabled', checked)}
            />
          </div>

          {settings.taxEnabled && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="taxName">اسم الضريبة</Label>
                  <Input
                    id="taxName"
                    value={settings.taxName}
                    onChange={(e) => handleInputChange('taxName', e.target.value)}
                    className="text-right"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taxRate">معدل الضريبة (%)</Label>
                  <Input
                    id="taxRate"
                    type="number"
                    value={settings.taxRate}
                    onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value))}
                    min="0"
                    max="100"
                    step="0.01"
                    className="w-24"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>إظهار الضريبة في السعر</Label>
                  <p className="text-sm text-muted-foreground">
                    عرض مبلغ الضريبة منفصلاً في الفاتورة
                  </p>
                </div>
                <Switch
                  checked={settings.showTaxInPrice}
                  onCheckedChange={(checked) => handleInputChange('showTaxInPrice', checked)}
                />
              </div>

              <Card className="bg-blue-50">
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Calculator className="w-4 h-4" />
                    مثال حساب الضريبة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-1 text-sm">
                    <p>السعر الأساسي: {formatCurrency(100)}</p>
                    <p>الضريبة ({settings.taxRate}%): {formatCurrency(100 * settings.taxRate / 100)}</p>
                    <p className="font-bold border-t pt-1">الإجمالي: {formatCurrency(100 + (100 * settings.taxRate / 100))}</p>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} className="flex items-center gap-2">
          <Save className="w-4 h-4" />
          حفظ الإعدادات
        </Button>
      </div>
    </div>
  );
};

export default CurrencySettings;
