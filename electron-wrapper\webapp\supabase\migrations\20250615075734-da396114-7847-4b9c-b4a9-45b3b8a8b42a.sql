
-- إضافة القيود المرجعية مع CASCADE للسماح بالحذف المتسلسل
ALTER TABLE public.sales 
DROP CONSTRAINT IF EXISTS sales_customer_id_fkey,
ADD CONSTRAINT sales_customer_id_fkey 
FOREIGN KEY (customer_id) REFERENCES public.customers(id) ON DELETE CASCADE;

ALTER TABLE public.debts 
DROP CONSTRAINT IF EXISTS debts_customer_id_fkey,
ADD CONSTRAINT debts_customer_id_fkey 
FOREIGN KEY (customer_id) REFERENCES public.customers(id) ON DELETE CASCADE;

ALTER TABLE public.installments 
DROP CONSTRAINT IF EXISTS installments_customer_id_fkey,
ADD CONSTRAINT installments_customer_id_fkey 
FOREIGN KEY (customer_id) REFERENCES public.customers(id) ON DELETE CASCADE;

-- إضافة القيود للجداول الفرعية
ALTER TABLE public.debt_payments 
DROP CONSTRAINT IF EXISTS debt_payments_debt_id_fkey,
ADD CONSTRAINT debt_payments_debt_id_fkey 
FOREIGN KEY (debt_id) REFERENCES public.debts(id) ON DELETE CASCADE;

ALTER TABLE public.installment_payments 
DROP CONSTRAINT IF EXISTS installment_payments_installment_id_fkey,
ADD CONSTRAINT installment_payments_installment_id_fkey 
FOREIGN KEY (installment_id) REFERENCES public.installments(id) ON DELETE CASCADE;
