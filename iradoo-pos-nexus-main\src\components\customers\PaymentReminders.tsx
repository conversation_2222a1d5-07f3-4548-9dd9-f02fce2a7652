
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Phone, Clock, AlertTriangle, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import WhatsAppSender from '../sales/WhatsAppSender';
import { 
  PaymentReminder, 
  getPaymentReminders, 
  updatePaymentReminder 
} from '@/utils/database';

const PaymentReminders = () => {
  const { toast } = useToast();
  const { formatCurrency } = useSettings();
  const [reminders, setReminders] = useState<PaymentReminder[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadReminders();
  }, []);

  const loadReminders = async () => {
    try {
      setLoading(true);
      const data = await getPaymentReminders();
      setReminders(data);
    } catch (error) {
      console.error('Error loading reminders:', error);
      toast({
        title: "خطأ في تحميل التذكيرات",
        description: "حدث خطأ أثناء تحميل تذكيرات الدفع",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const markReminderAsSent = async (reminderId: string) => {
    try {
      await updatePaymentReminder(reminderId, {
        status: 'sent',
        reminder_sent_at: new Date().toISOString()
      });
      
      await loadReminders();
      
      toast({
        title: "تم تحديث التذكير",
        description: "تم تسجيل إرسال التذكير بنجاح",
      });
    } catch (error) {
      console.error('Error updating reminder:', error);
      toast({
        title: "خطأ في تحديث التذكير",
        description: "حدث خطأ أثناء تحديث التذكير",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: string, dueDate: string) => {
    const isOverdue = new Date(dueDate) < new Date();
    
    if (status === 'paid') return 'bg-green-100 text-green-800 border-green-200';
    if (isOverdue) return 'bg-red-100 text-red-800 border-red-200';
    if (status === 'sent') return 'bg-blue-100 text-blue-800 border-blue-200';
    return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  };

  const getStatusText = (status: string, dueDate: string) => {
    const isOverdue = new Date(dueDate) < new Date();
    
    if (status === 'paid') return 'مدفوع';
    if (isOverdue) return 'متأخر';
    if (status === 'sent') return 'تم الإرسال';
    return 'معلق';
  };

  const getDaysOverdue = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = today.getTime() - due.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">تذكيرات الدفع</h2>
        <Badge variant="secondary" className="text-lg px-4 py-2">
          {reminders.length} تذكير
        </Badge>
      </div>

      {reminders.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد تذكيرات</h3>
            <p className="text-gray-500">لا توجد تذكيرات دفع مجدولة حالياً</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reminders.map((reminder) => {
            const daysOverdue = getDaysOverdue(reminder.due_date);
            const isOverdue = daysOverdue > 0;
            
            return (
              <Card key={reminder.id} className={`border-l-4 ${
                reminder.status === 'paid' ? 'border-l-green-500' :
                isOverdue ? 'border-l-red-500' :
                reminder.status === 'sent' ? 'border-l-blue-500' :
                'border-l-yellow-500'
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="w-5 h-5" />
                      {reminder.customer?.name || 'عميل غير معروف'}
                    </CardTitle>
                    <Badge 
                      className={getStatusColor(reminder.status, reminder.due_date)}
                      variant="outline"
                    >
                      {getStatusText(reminder.status, reminder.due_date)}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">المبلغ المستحق</p>
                      <p className="font-bold text-lg">{formatCurrency(reminder.amount)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">تاريخ الاستحقاق</p>
                      <p className="font-semibold">
                        {new Date(reminder.due_date).toLocaleDateString('ar-IQ')}
                      </p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-muted-foreground">نوع المستحق</p>
                    <p className="font-semibold">
                      {reminder.reference_type === 'debt' ? 'دين' : 'قسط'}
                    </p>
                  </div>

                  {isOverdue && (
                    <div className="flex items-center gap-2 text-red-600 bg-red-50 p-2 rounded">
                      <AlertTriangle className="w-4 h-4" />
                      <span className="text-sm font-semibold">
                        متأخر {daysOverdue} يوم
                      </span>
                    </div>
                  )}

                  {reminder.notes && (
                    <div>
                      <p className="text-sm text-muted-foreground">ملاحظات</p>
                      <p className="text-sm">{reminder.notes}</p>
                    </div>
                  )}

                  {reminder.customer?.phone && reminder.status !== 'paid' && (
                    <div className="space-y-2">
                      <WhatsAppSender
                        phoneNumber={reminder.customer.phone}
                        customerName={reminder.customer.name}
                        amount={reminder.amount}
                        paymentType="reminder"
                        reminderDetails={{
                          dueDate: reminder.due_date,
                          overdueCount: daysOverdue
                        }}
                      />
                      
                      {reminder.status === 'pending' && (
                        <Button
                          onClick={() => markReminderAsSent(reminder.id)}
                          variant="outline"
                          size="sm"
                          className="w-full"
                        >
                          <Clock className="w-4 h-4 ml-2" />
                          تسجيل كمرسل
                        </Button>
                      )}
                    </div>
                  )}

                  {reminder.reminder_sent_at && (
                    <div className="text-xs text-muted-foreground">
                      آخر إرسال: {new Date(reminder.reminder_sent_at).toLocaleString('ar-IQ')}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default PaymentReminders;
