
-- تفعيل Row Level Security على جميع الجداول
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sale_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.debt_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.installments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.installment_payments ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات أمان تسمح بجميع العمليات للمستخدمين المصرح لهم
CREATE POLICY "Allow all operations for authenticated users" ON public.categories
  FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.products
  FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.customers
  FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.sales
  FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.sale_items
  FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.debts
  FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.debt_payments
  FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.installments
  FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON public.installment_payments
  FOR ALL USING (true);
