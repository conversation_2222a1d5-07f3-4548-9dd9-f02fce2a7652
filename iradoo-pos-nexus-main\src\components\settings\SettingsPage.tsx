import React from 'react';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Store, DollarSign, Printer, Shield, Bell, Palette, Users, Eye, UserCog, Database, Key } from 'lucide-react';
import GeneralSettings from './GeneralSettings';
import CurrencySettings from './CurrencySettings';
import PrinterSettings from './PrinterSettings';
import ReceiptDesignSettings from './ReceiptDesignSettings';
import SecuritySettings from './SecuritySettings';
import NotificationSettings from './NotificationSettings';
import LocalUserSettings from './LocalUserSettings';
import SecurityAuditLogs from './SecurityAuditLogs';
import AccountManagement from './AccountManagement';
import BackupSettings from './BackupSettings';
import LicenseSettings from './LicenseSettings';
import LicenseGenerator from './LicenseGenerator';
import { useSettings } from '@/contexts/SettingsContext';
import { useLocalAuth } from '@/hooks/useLocalAuth';

const getTabGradient = (index: number) => {
  const gradients = [
    'from-blue-500 to-cyan-500',
    'from-green-500 to-emerald-500',
    'from-purple-500 to-violet-500',
    'from-pink-500 to-rose-500',
    'from-orange-500 to-amber-500',
    'from-slate-500 to-gray-500',
    'from-red-400 to-pink-500',
    'from-yellow-400 to-yellow-600',
    'from-indigo-500 to-purple-500',
    'from-teal-500 to-cyan-500',
    'from-emerald-500 to-teal-500'
  ];
  return gradients[index % gradients.length];
};

const tabConfig = (t: any, isAdmin: boolean, isLicenseGeneratorUser: boolean) => [
  {
    value: 'general',
    label: t('general'),
    icon: Store,
    gradient: getTabGradient(0),
    content: <GeneralSettings />
  },
  {
    value: 'currency',
    label: t('currency'),
    icon: DollarSign,
    gradient: getTabGradient(1),
    content: <CurrencySettings />
  },
  {
    value: 'printer',
    label: t('printer'),
    icon: Printer,
    gradient: getTabGradient(2),
    content: <PrinterSettings />
  },
  {
    value: 'receipt',
    label: t('receipt_design'),
    icon: Palette,
    gradient: getTabGradient(3),
    content: <ReceiptDesignSettings />
  },
  {
    value: 'backup',
    label: t('backup'),
    icon: Database,
    gradient: getTabGradient(4),
    content: <BackupSettings />
  },
  {
    value: 'license',
    label: t('license'),
    icon: Key,
    gradient: getTabGradient(5),
    content: <LicenseSettings />
  },
  {
    value: 'security',
    label: t('security'),
    icon: Shield,
    gradient: getTabGradient(6),
    content: <SecuritySettings />
  },
  {
    value: 'notifications',
    label: t('notifications'),
    icon: Bell,
    gradient: getTabGradient(7),
    content: <NotificationSettings />
  },
  ...(isAdmin
    ? [
        {
          value: 'users',
          label: t('user_management'),
          icon: Users,
          gradient: getTabGradient(8),
          content: <LocalUserSettings />
        },
        {
          value: 'accounts',
          label: t('account_management'),
          icon: UserCog,
          gradient: getTabGradient(9),
          content: <AccountManagement />
        },
        {
          value: 'audit',
          label: t('security_logs'),
          icon: Eye,
          gradient: getTabGradient(10),
          content: <SecurityAuditLogs />
        }
      ]
    : []),
  ...(isLicenseGeneratorUser
    ? [
        {
          value: 'license-generator',
          label: t('license_generator'),
          icon: Key,
          gradient: getTabGradient(11),
          content: <LicenseGenerator />
        }
      ]
    : []),
];

const SettingsPage = () => {
  const { t, settings } = useSettings();
  const { canManageUsers, user } = useLocalAuth();
  
  // التحقق من كون المستخدم هو المخول لاستخدام مولد التراخيص
  const isLicenseGeneratorUser = user?.email === '<EMAIL>';

  const tabs = tabConfig(t, canManageUsers, isLicenseGeneratorUser);

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent mb-2">
          {t('settings_title')}
        </h1>
        <p className="text-muted-foreground">
          {t('settings_description')}
        </p>
      </div>

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 h-auto p-2 bg-muted/50 rounded-lg mb-6">
          {tabs.map((tab, idx) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className={`
                flex flex-col items-center justify-center p-3 h-20 rounded-lg
                bg-gradient-to-br ${tab.gradient}
                text-white border-0
                data-[state=active]:scale-105 data-[state=active]:shadow-lg
                hover:scale-102 transition-all duration-200
                min-w-0 flex-1
              `}
            >
              <tab.icon className="w-5 h-5 mb-1" />
              <span className="text-xs font-medium text-center leading-tight">
                {tab.label}
              </span>
            </TabsTrigger>
          ))}
        </TabsList>

        <div className="mt-6">
          {tabs.map(tab => (
            <TabsContent
              key={tab.value}
              value={tab.value}
              className="mt-0"
            >
              <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
                {tab.content}
              </div>
            </TabsContent>
          ))}
        </div>
      </Tabs>
    </div>
  );
};

export default SettingsPage;
