
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Download, Upload, Database, Calendar, FileText } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';
import { supabase } from '@/integrations/supabase/client';

const BackupSettings = () => {
  const { toast } = useToast();
  const { t } = useSettings();
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const [isRestoringBackup, setIsRestoringBackup] = useState(false);

  const createBackup = async () => {
    setIsCreatingBackup(true);
    try {
      // جلب جميع البيانات من الجداول المختلفة
      const [
        { data: categories },
        { data: products },
        { data: customers },
        { data: sales },
        { data: saleItems },
        { data: debts },
        { data: installments },
        { data: suppliers },
        { data: purchases },
        { data: purchaseItems }
      ] = await Promise.all([
        supabase.from('categories').select('*'),
        supabase.from('products').select('*'),
        supabase.from('customers').select('*'),
        supabase.from('sales').select('*'),
        supabase.from('sale_items').select('*'),
        supabase.from('debts').select('*'),
        supabase.from('installments').select('*'),
        supabase.from('suppliers').select('*'),
        supabase.from('purchases').select('*'),
        supabase.from('purchase_items').select('*')
      ]);

      const backupData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        appName: 'أريدوو POS',
        data: {
          categories: categories || [],
          products: products || [],
          customers: customers || [],
          sales: sales || [],
          sale_items: saleItems || [],
          debts: debts || [],
          installments: installments || [],
          suppliers: suppliers || [],
          purchases: purchases || [],
          purchase_items: purchaseItems || []
        },
        settings: JSON.parse(localStorage.getItem('aridoo-settings') || '{}')
      };

      // تحويل البيانات إلى JSON وتنزيلها
      const blob = new Blob([JSON.stringify(backupData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `aridoo-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: 'تم إنشاء النسخة الاحتياطية',
        description: 'تم تنزيل النسخة الاحتياطية بنجاح',
      });
    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في إنشاء النسخة الاحتياطية',
        variant: 'destructive',
      });
    } finally {
      setIsCreatingBackup(false);
    }
  };

  const restoreBackup = async (file: File) => {
    setIsRestoringBackup(true);
    try {
      const text = await file.text();
      const backupData = JSON.parse(text);

      // التحقق من صحة النسخة الاحتياطية
      if (!backupData.version || !backupData.data) {
        throw new Error('ملف النسخة الاحتياطية غير صالح');
      }

      // استعادة الإعدادات
      if (backupData.settings) {
        localStorage.setItem('aridoo-settings', JSON.stringify(backupData.settings));
      }

      // استعادة البيانات (بترتيب معين لتجنب مشاكل المراجع الخارجية)
      const { data } = backupData;

      // حذف البيانات الحالية أولاً
      await Promise.all([
        supabase.from('sale_items').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('purchase_items').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('debt_payments').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('installment_payments').delete().neq('id', '00000000-0000-0000-0000-000000000000')
      ]);

      await Promise.all([
        supabase.from('sales').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('purchases').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('debts').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('installments').delete().neq('id', '00000000-0000-0000-0000-000000000000')
      ]);

      await Promise.all([
        supabase.from('products').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('customers').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('suppliers').delete().neq('id', '00000000-0000-0000-0000-000000000000'),
        supabase.from('categories').delete().neq('id', '00000000-0000-0000-0000-000000000000')
      ]);

      // إدراج البيانات الجديدة
      if (data.categories?.length > 0) {
        await supabase.from('categories').insert(data.categories);
      }

      if (data.suppliers?.length > 0) {
        await supabase.from('suppliers').insert(data.suppliers);
      }

      if (data.customers?.length > 0) {
        await supabase.from('customers').insert(data.customers);
      }

      if (data.products?.length > 0) {
        await supabase.from('products').insert(data.products);
      }

      if (data.sales?.length > 0) {
        await supabase.from('sales').insert(data.sales);
      }

      if (data.sale_items?.length > 0) {
        await supabase.from('sale_items').insert(data.sale_items);
      }

      if (data.purchases?.length > 0) {
        await supabase.from('purchases').insert(data.purchases);
      }

      if (data.purchase_items?.length > 0) {
        await supabase.from('purchase_items').insert(data.purchase_items);
      }

      if (data.debts?.length > 0) {
        await supabase.from('debts').insert(data.debts);
      }

      if (data.installments?.length > 0) {
        await supabase.from('installments').insert(data.installments);
      }

      toast({
        title: 'تم استعادة النسخة الاحتياطية',
        description: 'تم استعادة جميع البيانات بنجاح. سيتم إعادة تحميل الصفحة.',
      });

      // إعادة تحميل الصفحة لتطبيق التغييرات
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في استعادة النسخة الاحتياطية: ' + (error as Error).message,
        variant: 'destructive',
      });
    } finally {
      setIsRestoringBackup(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type === 'application/json' || file.name.endsWith('.json')) {
        restoreBackup(file);
      } else {
        toast({
          title: 'خطأ',
          description: 'يرجى اختيار ملف JSON صالح',
          variant: 'destructive',
        });
      }
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            النسخ الاحتياطية
          </CardTitle>
          <CardDescription>
            إنشاء واستعادة النسخ الاحتياطية لجميع بيانات التطبيق
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* إنشاء نسخة احتياطية */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              <Label className="text-base font-medium">إنشاء نسخة احتياطية</Label>
            </div>
            <p className="text-sm text-muted-foreground">
              قم بإنشاء نسخة احتياطية كاملة من جميع بيانات التطبيق وتنزيلها على جهازك
            </p>
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span>التاريخ: {new Date().toLocaleDateString('ar-IQ')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  <span>تنسيق: JSON</span>
                </div>
              </div>
            </div>
            <Button 
              onClick={createBackup}
              disabled={isCreatingBackup}
              className="w-full"
            >
              <Download className="w-4 h-4 mr-2" />
              {isCreatingBackup ? 'جاري إنشاء النسخة...' : 'إنشاء وتنزيل النسخة الاحتياطية'}
            </Button>
          </div>

          <div className="border-t pt-6">
            
            {/* استعادة نسخة احتياطية */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                <Label className="text-base font-medium">استعادة نسخة احتياطية</Label>
              </div>
              <p className="text-sm text-muted-foreground">
                اختر ملف النسخة الاحتياطية من جهازك لاستعادة البيانات
              </p>
              
              <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                <div className="flex items-start gap-2">
                  <div className="w-4 h-4 rounded-full bg-yellow-500 mt-0.5 flex-shrink-0"></div>
                  <div className="text-sm">
                    <p className="font-medium text-yellow-800 mb-1">تحذير مهم:</p>
                    <p className="text-yellow-700">
                      استعادة النسخة الاحتياطية ستقوم بحذف جميع البيانات الحالية واستبدالها بالبيانات من الملف المحدد. 
                      تأكد من إنشاء نسخة احتياطية من البيانات الحالية أولاً.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="backup-file">اختيار ملف النسخة الاحتياطية</Label>
                <Input
                  id="backup-file"
                  type="file"
                  accept=".json"
                  onChange={handleFileUpload}
                  disabled={isRestoringBackup}
                  className="cursor-pointer"
                />
              </div>

              {isRestoringBackup && (
                <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-blue-800 text-sm">جاري استعادة النسخة الاحتياطية...</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* معلومات إضافية */}
          <div className="border-t pt-6">
            <div className="space-y-2">
              <Label className="text-base font-medium">ما يتم نسخه احتياطياً:</Label>
              <ul className="text-sm text-muted-foreground space-y-1 mr-4">
                <li>• جميع المنتجات والفئات</li>
                <li>• بيانات العملاء والموردين</li>
                <li>• سجلات المبيعات والمشتريات</li>
                <li>• الديون والأقساط</li>
                <li>• إعدادات التطبيق</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BackupSettings;
