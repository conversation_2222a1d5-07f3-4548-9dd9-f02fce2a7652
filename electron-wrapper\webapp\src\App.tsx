
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { LocalAuthProvider } from "./hooks/useLocalAuth";
import { SettingsProvider } from "./contexts/SettingsContext";
import { ThemeProvider } from "./hooks/useTheme";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import { useEffect } from "react";

const queryClient = new QueryClient();

const AppContent = () => {
  useEffect(() => {
    // Listen for settings changes to force re-render
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'aridoo-settings') {
        // Force a re-render by updating a state or triggering a refresh
        window.dispatchEvent(new Event('settings-changed'));
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/*" element={<Index />} />
        <Route path="/404" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="light" storageKey="aridoo-ui-theme">
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <LocalAuthProvider>
          <SettingsProvider>
            <AppContent />
          </SettingsProvider>
        </LocalAuthProvider>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
