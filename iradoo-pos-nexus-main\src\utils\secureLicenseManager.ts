
import { supabase } from '@/integrations/supabase/client';

export interface SecureLicenseInfo {
  isValid: boolean;
  customerName?: string;
  features?: string[];
  expirationDate?: string;
  error?: string;
}

export class SecureLicenseManager {
  private static instance: SecureLicenseManager;

  private constructor() {}

  public static getInstance(): SecureLicenseManager {
    if (!SecureLicenseManager.instance) {
      SecureLicenseManager.instance = new SecureLicenseManager();
    }
    return SecureLicenseManager.instance;
  }

  // Server-side license validation
  public async validateLicense(licenseKey: string): Promise<SecureLicenseInfo> {
    try {
      const deviceInfo = this.getDeviceFingerprint();
      
      const { data, error } = await supabase.functions.invoke('validate-license', {
        body: { licenseKey, deviceInfo }
      });

      if (error) {
        return { isValid: false, error: 'License validation failed' };
      }

      return data.isValid 
        ? { isValid: true, customerName: 'Licensed User', features: ['all'] }
        : { isValid: false, error: 'Invalid license' };
    } catch (error) {
      console.error('License validation error:', error);
      return { isValid: false, error: 'Network error during validation' };
    }
  }

  // Check if user is authenticated developer
  public async isDeveloperAuthenticated(): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      // Check if user has admin role in database
      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role, email')
          .eq('id', user.id)
          .single();

        return profile?.role === 'admin' && profile?.email === '<EMAIL>';
      }
      
      return false;
    } catch (error) {
      console.error('Developer auth check error:', error);
      return false;
    }
  }

  public async getCurrentLicenseStatus(): Promise<SecureLicenseInfo | null> {
    // First check if user is authenticated developer
    if (await this.isDeveloperAuthenticated()) {
      return {
        isValid: true,
        customerName: 'Developer Account',
        features: ['all']
      };
    }

    // Check for saved license
    const savedLicense = this.getSavedLicense();
    if (!savedLicense) {
      return null;
    }

    return await this.validateLicense(savedLicense);
  }

  public async canPerformActions(): Promise<boolean> {
    if (await this.isDeveloperAuthenticated()) {
      return true;
    }

    const licenseStatus = await this.getCurrentLicenseStatus();
    return licenseStatus?.isValid === true;
  }

  public async hasFeature(feature: string): Promise<boolean> {
    if (await this.isDeveloperAuthenticated()) {
      return true;
    }

    const licenseStatus = await this.getCurrentLicenseStatus();
    return licenseStatus?.isValid && (
      licenseStatus.features?.includes(feature) || 
      licenseStatus.features?.includes('all')
    ) || false;
  }

  private getSavedLicense(): string | null {
    try {
      const encrypted = localStorage.getItem('aridoo-license-encrypted');
      if (!encrypted) return null;
      
      // Decrypt license (implement proper decryption)
      return atob(encrypted); // Basic decoding - implement proper encryption
    } catch (error) {
      console.error('Error retrieving license:', error);
      return null;
    }
  }

  public saveLicense(licenseKey: string): boolean {
    try {
      // Encrypt license before storing
      const encrypted = btoa(licenseKey); // Basic encoding - implement proper encryption
      localStorage.setItem('aridoo-license-encrypted', encrypted);
      return true;
    } catch (error) {
      console.error('Error saving license:', error);
      return false;
    }
  }

  private getDeviceFingerprint(): { macAddress: string; deviceName: string } {
    // Create secure device fingerprint without exposing sensitive info
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Fingerprint', 2, 2);
    }
    
    const fingerprint = canvas.toDataURL();
    const hash = this.simpleHash(fingerprint + navigator.userAgent + screen.width + screen.height);
    
    return {
      macAddress: hash.substring(0, 12),
      deviceName: navigator.platform + '-' + hash.substring(12, 20)
    };
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  public removeLicense(): void {
    localStorage.removeItem('aridoo-license-encrypted');
  }
}

export const secureLicenseManager = SecureLicenseManager.getInstance();
