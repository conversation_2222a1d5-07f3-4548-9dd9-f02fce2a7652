# 🚀 دليل البدء السريع - Iradoo POS Desktop

## خطوات سريعة للبدء:

### 1️⃣ الإعداد الأولي (مرة واحدة فقط)
```
انقر نقراً مزدوجاً على: setup.bat
```

### 2️⃣ نسخ ملفات تطبيق الويب
```
انقر نقراً مزدوجاً على: copy-webapp.bat
```

### 3️⃣ اختبار التطبيق
```
انقر نقراً مزدوجاً على: start.bat
```

### 4️⃣ بناء التطبيق للتوزيع
```
انقر نقراً مزدوجاً على: build.bat
```

---

## 📁 الملفات المهمة:

| الملف | الوصف |
|-------|--------|
| `setup.bat` | إعداد أولي - تثبيت التبعيات |
| `copy-webapp.bat` | نسخ ملفات تطبيق الويب |
| `start.bat` | تشغيل التطبيق للاختبار |
| `build.bat` | بناء التطبيق للتوزيع |
| `webapp/` | مجلد ملفات تطبيق الويب |
| `assets/` | مجلد الأيقونات |
| `config.json` | إعدادات التطبيق |

---

## ⚙️ تخصيص سريع:

### تغيير اسم التطبيق:
1. افتح `package.json`
2. غيّر `"productName": "اسم جديد"`

### تغيير الأيقونة:
1. ضع `icon.png` (512x512) في مجلد `assets/`
2. ضع `icon.ico` في مجلد `assets/`

### استخدام رابط أونلاين بدلاً من ملفات محلية:
1. افتح `config.json`
2. غيّر `"mode": "local"` إلى `"mode": "url"`
3. ضع الرابط في `"url": "https://your-site.com"`

---

## 🔄 تحديث التطبيق:

### تحديث ملفات الويب:
1. شغّل `copy-webapp.bat`
2. شغّل `build.bat`

### تحديث إعدادات التطبيق:
1. عدّل الملفات المطلوبة
2. شغّل `build.bat`

---

## 📦 ملفات التوزيع:

بعد تشغيل `build.bat`، ستجد في مجلد `dist`:

- **Setup.exe** - ملف التثبيت
- **Portable.exe** - نسخة محمولة
- **win-unpacked/** - ملفات التطبيق

---

## ❗ مشاكل شائعة:

### التطبيق لا يبدأ:
- تأكد من وجود `index.html` في `webapp/`
- شغّل `setup.bat` مرة أخرى

### الأيقونة لا تظهر:
- تأكد من وجود `icon.ico` في `assets/`
- تأكد من حجم الصورة (512x512)

### خطأ في البناء:
- تأكد من تثبيت Node.js
- شغّل `setup.bat` مرة أخرى

---

## 📞 للمساعدة:
راجع ملف `README.md` للتفاصيل الكاملة
