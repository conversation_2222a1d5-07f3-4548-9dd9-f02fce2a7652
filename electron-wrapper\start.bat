@echo off
echo ========================================
echo    تشغيل Iradoo POS Desktop
echo ========================================
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo التبعيات غير مثبتة. تشغيل الإعداد...
    call setup.bat
    if %errorlevel% neq 0 (
        echo فشل في الإعداد!
        pause
        exit /b 1
    )
)

REM Check if webapp directory has content
if not exist "webapp\index.html" (
    echo لم يتم العثور على ملفات التطبيق في مجلد webapp
    echo هل تريد نسخ ملفات التطبيق؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        call copy-webapp.bat
    ) else (
        echo يرجى نسخ ملفات تطبيق الويب إلى مجلد webapp أولاً
        pause
        exit /b 1
    )
)

echo تشغيل التطبيق...
call npm start
