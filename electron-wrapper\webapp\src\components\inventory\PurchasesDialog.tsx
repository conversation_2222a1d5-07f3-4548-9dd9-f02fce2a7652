
import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { updateProduct, type Product, type Category } from '@/utils/database';
import { ShoppingCart, Plus, Trash2, Calculator } from 'lucide-react';

interface PurchaseItem {
  product: Product;
  quantity: number;
  unitCost: number;
  total: number;
}

interface PurchasesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  products: Product[];
  categories: Category[];
  onPurchaseComplete: () => void;
}

const PurchasesDialog: React.FC<PurchasesDialogProps> = ({
  open,
  onOpenChange,
  products,
  categories,
  onPurchaseComplete
}) => {
  const { toast } = useToast();
  const [selectedProductId, setSelectedProductId] = useState('');
  const [quantity, setQuantity] = useState('');
  const [unitCost, setUnitCost] = useState('');
  const [purchaseItems, setPurchaseItems] = useState<PurchaseItem[]>([]);
  const [supplier, setSupplier] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  const addToPurchase = () => {
    const product = products.find(p => p.id === selectedProductId);
    if (!product || !quantity || !unitCost) return;

    const qty = parseInt(quantity);
    const cost = parseFloat(unitCost);
    const total = qty * cost;

    const newItem: PurchaseItem = {
      product,
      quantity: qty,
      unitCost: cost,
      total
    };

    setPurchaseItems([...purchaseItems, newItem]);
    setSelectedProductId('');
    setQuantity('');
    setUnitCost('');
  };

  const removeFromPurchase = (index: number) => {
    setPurchaseItems(purchaseItems.filter((_, i) => i !== index));
  };

  const getTotalCost = () => {
    return purchaseItems.reduce((sum, item) => sum + item.total, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (purchaseItems.length === 0) {
      toast({
        title: "لا توجد عناصر",
        description: "يرجى إضافة منتجات للمشتريات",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      // Update product stocks
      for (const item of purchaseItems) {
        await updateProduct(item.product.id, {
          stock: item.product.stock + item.quantity
        });
      }

      toast({
        title: "تم تسجيل المشتريات",
        description: `تم إضافة ${purchaseItems.length} منتج للمخزون بقيمة ${getTotalCost().toLocaleString()} د.ع`,
      });

      onPurchaseComplete();
      onOpenChange(false);
      setPurchaseItems([]);
      setSupplier('');
      setNotes('');
    } catch (error) {
      console.error('Error recording purchase:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء تسجيل المشتريات",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            تسجيل مشتريات جديدة
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add Product Section */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-4">إضافة منتج للمشتريات</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label>المنتج</Label>
                  <Select value={selectedProductId} onValueChange={setSelectedProductId}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر المنتج" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name} - مخزون: {product.stock}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>الكمية</Label>
                  <Input
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={(e) => setQuantity(e.target.value)}
                    placeholder="الكمية"
                  />
                </div>

                <div className="space-y-2">
                  <Label>سعر الوحدة</Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={unitCost}
                    onChange={(e) => setUnitCost(e.target.value)}
                    placeholder="السعر"
                  />
                </div>

                <div className="space-y-2">
                  <Label>&nbsp;</Label>
                  <Button 
                    onClick={addToPurchase}
                    disabled={!selectedProductId || !quantity || !unitCost}
                    className="w-full"
                  >
                    <Plus className="w-4 h-4 ml-2" />
                    إضافة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Purchase Items List */}
          {purchaseItems.length > 0 && (
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-4">عناصر المشتريات</h3>
                <div className="space-y-3">
                  {purchaseItems.map((item, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{item.product.name}</h4>
                        <p className="text-sm text-gray-600">
                          {item.quantity} × {item.unitCost.toLocaleString()} د.ع = {item.total.toLocaleString()} د.ع
                        </p>
                      </div>
                      <Button
                        onClick={() => removeFromPurchase(index)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                <div className="mt-4 pt-4 border-t">
                  <div className="flex items-center justify-between text-lg font-bold">
                    <span className="flex items-center gap-2">
                      <Calculator className="w-5 h-5" />
                      إجمالي التكلفة:
                    </span>
                    <span>{getTotalCost().toLocaleString()} د.ع</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Purchase Details */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-4">تفاصيل المشتريات</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>المورد</Label>
                  <Input
                    value={supplier}
                    onChange={(e) => setSupplier(e.target.value)}
                    placeholder="اسم المورد"
                  />
                </div>

                <div className="space-y-2">
                  <Label>ملاحظات</Label>
                  <Textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="ملاحظات إضافية..."
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex gap-4">
            <Button 
              onClick={handleSubmit}
              disabled={loading || purchaseItems.length === 0}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              {loading ? "جاري التسجيل..." : "تأكيد المشتريات"}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PurchasesDialog;
