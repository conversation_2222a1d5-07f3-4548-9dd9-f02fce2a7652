
import React from 'react';
import {
  Di<PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Receipt,
  User,
  Calendar,
  Clock,
  CreditCard,
  Package,
  DollarSign,
  TrendingUp,
  Phone,
  MapPin
} from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';

interface TransactionItem {
  product_name?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

interface TransactionDetails {
  id: string;
  date: string;
  time: string;
  customer: string;
  customerPhone?: string;
  customerAddress?: string;
  amount: number;
  cost: number;
  profit: number;
  profitMargin: number;
  type: string;
  items: number;
  status: string;
  discount?: number;
  tax?: number;
  transactionItems?: TransactionItem[];
}

interface TransactionDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: TransactionDetails | null;
}

const TransactionDetailsDialog = ({ open, onOpenChange, transaction }: TransactionDetailsDialogProps) => {
  const { formatCurrency } = useSettings();

  if (!transaction) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Receipt className="w-6 h-6" />
            تفاصيل المعاملة {transaction.id}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* معلومات أساسية */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  معلومات المعاملة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">رقم الفاتورة:</span>
                  <Badge variant="outline" className="font-mono">{transaction.id}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">التاريخ:</span>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span>{transaction.date}</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الوقت:</span>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span>{transaction.time}</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">طريقة الدفع:</span>
                  <Badge variant={transaction.type === 'نقدي' ? 'default' : 'secondary'}>
                    <CreditCard className="w-4 h-4 ml-1" />
                    {transaction.type}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الحالة:</span>
                  <Badge variant="default">{transaction.status}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <User className="w-5 h-5" />
                  معلومات العميل
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">اسم العميل:</span>
                  <span className="font-medium">{transaction.customer}</span>
                </div>
                {transaction.customerPhone && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">رقم الهاتف:</span>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span>{transaction.customerPhone}</span>
                    </div>
                  </div>
                )}
                {transaction.customerAddress && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">العنوان:</span>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-gray-500" />
                      <span>{transaction.customerAddress}</span>
                    </div>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">عدد الأصناف:</span>
                  <Badge variant="outline">
                    <Package className="w-4 h-4 ml-1" />
                    {transaction.items} صنف
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* الملخص المالي */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <DollarSign className="w-5 h-5" />
                الملخص المالي
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(transaction.amount)}
                  </div>
                  <div className="text-sm text-gray-600">إجمالي المبيعات</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {formatCurrency(transaction.cost)}
                  </div>
                  <div className="text-sm text-gray-600">إجمالي الكلفة</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(transaction.profit)}
                  </div>
                  <div className="text-sm text-gray-600">صافي الربح</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 flex items-center justify-center gap-1">
                    <TrendingUp className="w-5 h-5" />
                    {transaction.profitMargin.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">هامش الربح</div>
                </div>
              </div>

              {(transaction.discount || transaction.tax) && (
                <>
                  <Separator className="my-4" />
                  <div className="grid grid-cols-2 gap-4">
                    {transaction.discount && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">الخصم:</span>
                        <span className="text-red-600 font-medium">
                          -{formatCurrency(transaction.discount)}
                        </span>
                      </div>
                    )}
                    {transaction.tax && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">الضريبة:</span>
                        <span className="text-blue-600 font-medium">
                          +{formatCurrency(transaction.tax)}
                        </span>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* تفاصيل الأصناف */}
          {transaction.transactionItems && transaction.transactionItems.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  تفاصيل الأصناف
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {transaction.transactionItems.map((item, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <div>
                        <div className="font-medium">{item.product_name || `منتج ${index + 1}`}</div>
                        <div className="text-sm text-gray-600">
                          {item.quantity} × {formatCurrency(item.unit_price)}
                        </div>
                      </div>
                      <div className="text-lg font-bold text-green-600">
                        {formatCurrency(item.total_price)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TransactionDetailsDialog;
