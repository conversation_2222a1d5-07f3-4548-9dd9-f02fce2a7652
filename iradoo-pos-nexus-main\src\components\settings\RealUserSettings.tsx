import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { Plus, Edit, Trash2, UserPlus, Shield, AlertTriangle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Alert,
  AlertDescription,
} from '@/components/ui/alert';
import { supabase } from '@/integrations/supabase/client';
import { useSecureAuth } from '@/hooks/useSecureAuth';
import { validateEmailEnhanced, validatePasswordEnhanced, sanitizeInputEnhanced } from '@/utils/enhancedSecurity';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'cashier' | 'supervisor';
  created_at: string;
  updated_at: string;
}

const RealUserSettings = () => {
  const { toast } = useToast();
  const { isAdmin, checkPermission } = useSecureAuth();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddUser, setShowAddUser] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    password: '',
    role: 'cashier' as 'admin' | 'cashier' | 'supervisor'
  });
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    if (isAdmin) {
      fetchUsers();
    }
  }, [isAdmin]);

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Type cast the data to ensure proper typing
      const typedUsers: UserProfile[] = (data || []).map(user => ({
        ...user,
        role: user.role as 'admin' | 'cashier' | 'supervisor'
      }));
      
      setUsers(typedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل قائمة المستخدمين",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const validateNewUser = (): boolean => {
    const errors: string[] = [];
    
    if (!newUser.name.trim()) {
      errors.push('اسم المستخدم مطلوب');
    }
    
    if (!validateEmailEnhanced(newUser.email)) {
      errors.push('البريد الإلكتروني غير صحيح');
    }
    
    const passwordValidation = validatePasswordEnhanced(newUser.password);
    if (!passwordValidation.isValid) {
      errors.push(...passwordValidation.errors);
    }
    
    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleAddUser = async () => {
    if (!validateNewUser()) return;

    if (!await checkPermission('manage_users')) {
      toast({
        title: "خطأ في الصلاحيات",
        description: "ليس لديك صلاحية لإضافة مستخدمين",
        variant: "destructive"
      });
      return;
    }

    try {
      const sanitizedName = sanitizeInputEnhanced(newUser.name);
      const sanitizedEmail = sanitizeInputEnhanced(newUser.email);

      const { data, error } = await supabase.auth.signUp({
        email: sanitizedEmail,
        password: newUser.password,
        options: {
          emailRedirectTo: `${window.location.origin}/`,
          data: {
            name: sanitizedName,
            role: newUser.role
          }
        }
      });

      if (error) throw error;

      if (data.user) {
        // Update the profile with the correct role
        const { error: profileError } = await supabase
          .from('profiles')
          .update({ role: newUser.role })
          .eq('id', data.user.id);

        if (profileError) throw profileError;
      }

      setNewUser({ name: '', email: '', password: '', role: 'cashier' });
      setShowAddUser(false);
      setValidationErrors([]);
      await fetchUsers();
      
      toast({
        title: "تم الإضافة",
        description: "تم إضافة المستخدم بنجاح مع تطبيق المعايير الأمنية المحسّنة",
      });
    } catch (error: any) {
      console.error('Error adding user:', error);
      toast({
        title: "خطأ",
        description: error.message || "فشل في إضافة المستخدم",
        variant: "destructive"
      });
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!await checkPermission('manage_users')) {
      toast({
        title: "خطأ في الصلاحيات",
        description: "ليس لديك صلاحية لحذف مستخدمين",
        variant: "destructive"
      });
      return;
    }

    try {
      // Note: supabase.auth.admin requires service role key, which we don't have in client
      // For now, we'll just show an error message
      toast({
        title: "خطأ",
        description: "حذف المستخدمين يتطلب صلاحيات الخادم. يرجى الاتصال بمسؤول النظام.",
        variant: "destructive"
      });
      setShowDeleteConfirm(null);
    } catch (error: any) {
      console.error('Error deleting user:', error);
      toast({
        title: "خطأ",
        description: error.message || "فشل في حذف المستخدم",
        variant: "destructive"
      });
    }
  };

  const updateUserRole = async (userId: string, newRole: 'admin' | 'cashier' | 'supervisor') => {
    if (!await checkPermission('manage_users')) {
      toast({
        title: "خطأ في الصلاحيات",
        description: "ليس لديك صلاحية لتعديل صلاحيات المستخدمين",
        variant: "destructive"
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role: newRole })
        .eq('id', userId);

      if (error) throw error;

      await fetchUsers();
      toast({
        title: "تم التحديث",
        description: "تم تحديث صلاحيات المستخدم بنجاح",
      });
    } catch (error: any) {
      console.error('Error updating user role:', error);
      toast({
        title: "خطأ",
        description: error.message || "فشل في تحديث الصلاحيات",
        variant: "destructive"
      });
    }
  };

  const roleLabels = {
    admin: 'مدير النظام',
    supervisor: 'مشرف',
    cashier: 'كاشير'
  };

  const roleColors = {
    admin: 'bg-red-100 text-red-800',
    supervisor: 'bg-blue-100 text-blue-800',
    cashier: 'bg-green-100 text-green-800'
  };

  if (!isAdmin) {
    return (
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          ليس لديك صلاحية للوصول إلى إدارة المستخدمين
        </AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return <div className="flex justify-center p-8">جاري التحميل...</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                إدارة المستخدمين
              </CardTitle>
              <CardDescription>
                إضافة وإدارة مستخدمي النظام وصلاحياتهم
              </CardDescription>
            </div>
            <Button onClick={() => setShowAddUser(true)} className="flex items-center gap-2">
              <UserPlus className="w-4 h-4" />
              إضافة مستخدم
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <Avatar className="w-12 h-12">
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                      {user.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{user.name}</h3>
                    <p className="text-sm text-gray-500">{user.email}</p>
                    <p className="text-xs text-gray-400">
                      تم الإنشاء: {new Date(user.created_at).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Select 
                    value={user.role} 
                    onValueChange={(value: 'admin' | 'cashier' | 'supervisor') => 
                      updateUserRole(user.id, value)
                    }
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cashier">كاشير</SelectItem>
                      <SelectItem value="supervisor">مشرف</SelectItem>
                      <SelectItem value="admin">مدير النظام</SelectItem>
                    </SelectContent>
                  </Select>
                  <Badge className={roleColors[user.role]}>
                    {roleLabels[user.role]}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDeleteConfirm(user.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Add User Dialog with enhanced validation */}
      <Dialog open={showAddUser} onOpenChange={setShowAddUser}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>إضافة مستخدم جديد (محسّن)</DialogTitle>
            <DialogDescription>
              أدخل معلومات المستخدم الجديد مع تطبيق المعايير الأمنية المحسّنة
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {validationErrors.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="list-disc list-inside">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
            <div className="space-y-2">
              <Label htmlFor="userName">الاسم</Label>
              <Input
                id="userName"
                value={newUser.name}
                onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="userEmail">البريد الإلكتروني</Label>
              <Input
                id="userEmail"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="userPassword">كلمة المرور</Label>
              <Input
                id="userPassword"
                type="password"
                value={newUser.password}
                onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                className="text-right"
              />
              <p className="text-xs text-gray-500">
                يجب أن تحتوي على 8 أحرف على الأقل مع حرف كبير وصغير ورقم ورمز خاص
              </p>
            </div>
            <div className="space-y-2">
              <Label>الصلاحية</Label>
              <Select 
                value={newUser.role} 
                onValueChange={(value: 'admin' | 'cashier' | 'supervisor') => 
                  setNewUser({ ...newUser, role: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cashier">كاشير</SelectItem>
                  <SelectItem value="supervisor">مشرف</SelectItem>
                  <SelectItem value="admin">مدير النظام</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddUser(false)}>
              إلغاء
            </Button>
            <Button onClick={handleAddUser}>
              إضافة المستخدم
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!showDeleteConfirm} onOpenChange={() => setShowDeleteConfirm(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد الحذف</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
              إلغاء
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => showDeleteConfirm && handleDeleteUser(showDeleteConfirm)}
            >
              حذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RealUserSettings;
