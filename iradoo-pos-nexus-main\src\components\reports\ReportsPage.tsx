import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { BarChart3, TrendingUp, TrendingDown, Download, Calendar, DollarSign, Users, Package, ShoppingCart, FileText, Eye, AlertTriangle, Clock, CreditCard, Archive, Calculator, Target, TrendingUpIcon, Search } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, ComposedChart, Area, AreaChart } from 'recharts';
import { getDashboardStats, getSales, getCustomers, getProducts, getDebts, getInstallments, type Sale, type Customer, type Product, type Debt, type Installment } from '@/utils/database';
import TransactionDetailsDialog from './TransactionDetailsDialog';
import { useSettings } from '@/contexts/SettingsContext';

const ReportsPage = () => {
  const { toast } = useToast();
  const { t, formatCurrency } = useSettings();
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const [activeChart, setActiveChart] = useState('sales');
  const [loading, setLoading] = useState(true);
  const [searchInvoiceId, setSearchInvoiceId] = useState('');
  const [searchedTransaction, setSearchedTransaction] = useState<any>(null);
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [dashboardStats, setDashboardStats] = useState({
    todayTotal: 0,
    totalProducts: 0,
    lowStockCount: 0,
    outOfStockCount: 0
  });
  const [sales, setSales] = useState<Sale[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [debts, setDebts] = useState<Debt[]>([]);
  const [installments, setInstallments] = useState<Installment[]>([]);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const [showTransactionDialog, setShowTransactionDialog] = useState(false);

  const loadData = async () => {
    try {
      setLoading(true);
      const [statsData, salesData, customersData, productsData, debtsData, installmentsData] = await Promise.all([
        getDashboardStats(),
        getSales(),
        getCustomers(),
        getProducts(),
        getDebts(),
        getInstallments()
      ]);
      
      setDashboardStats(statsData);
      setSales(salesData);
      setCustomers(customersData);
      setProducts(productsData);
      setDebts(debtsData);
      setInstallments(installmentsData);
    } catch (error) {
      toast({
        title: t('error'),
        description: t('operation_failed'),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const calculateAdvancedStats = () => {
    const today = new Date();
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfYear = new Date(today.getFullYear(), 0, 1);

    const filterSalesByPeriod = (period: string) => {
      const now = new Date();
      return sales.filter(sale => {
        const saleDate = new Date(sale.created_at);
        switch (period) {
          case 'today':
            return saleDate.toDateString() === now.toDateString();
          case 'week':
            return saleDate >= startOfWeek;
          case 'month':
            return saleDate >= startOfMonth;
          case 'year':
            return saleDate >= startOfYear;
          default:
            return true;
        }
      });
    };

    const periodSales = filterSalesByPeriod(selectedPeriod);
    const totalSales = periodSales.reduce((sum, sale) => sum + Number(sale.final_amount), 0);
    const totalTransactions = periodSales.length;
    
    let totalCost = 0;
    let totalProfit = 0;
    
    periodSales.forEach(sale => {
      sale.sale_items?.forEach(item => {
        const product = products.find(p => p.id === item.product_id);
        if (product) {
          const itemCost = Number(product.cost_price) * item.quantity;
          const itemRevenue = Number(item.total_price);
          totalCost += itemCost;
          totalProfit += (itemRevenue - itemCost);
        }
      });
    });

    const totalDebts = debts
      .filter(debt => debt.status === 'pending')
      .reduce((sum, debt) => sum + Number(debt.remaining_amount), 0);
    
    const totalInstallments = installments
      .filter(inst => inst.status === 'active')
      .reduce((sum, inst) => sum + Number(inst.remaining_amount), 0);

    const averageTransaction = totalTransactions > 0 ? totalSales / totalTransactions : 0;
    
    return {
      totalSales,
      totalTransactions,
      totalCost,
      totalProfit,
      totalDebts,
      totalInstallments,
      averageTransaction,
      profitMargin: totalSales > 0 ? (totalProfit / totalSales) * 100 : 0
    };
  };

  const advancedStats = calculateAdvancedStats();

  const getDetailedProductAnalysis = () => {
    const productAnalysis = products.map(product => {
      const productSales = sales.reduce((acc, sale) => {
        const saleItems = sale.sale_items?.filter(item => item.product_id === product.id) || [];
        return acc + saleItems.reduce((sum, item) => sum + item.quantity, 0);
      }, 0);

      const productRevenue = sales.reduce((acc, sale) => {
        const saleItems = sale.sale_items?.filter(item => item.product_id === product.id) || [];
        return acc + saleItems.reduce((sum, item) => sum + Number(item.total_price), 0);
      }, 0);

      const productCost = Number(product.cost_price) * productSales;
      const productProfit = productRevenue - productCost;
      const stockValue = Number(product.cost_price) * product.stock;
      const potentialRevenue = Number(product.price) * product.stock;

      return {
        id: product.id,
        name: product.name,
        category: product.category?.name || t('no_data'),
        stock: product.stock,
        minStock: product.min_stock,
        costPrice: Number(product.cost_price),
        sellingPrice: Number(product.price),
        totalSales: productSales,
        totalRevenue: productRevenue,
        totalProfit: productProfit,
        stockValue: stockValue,
        potentialRevenue: potentialRevenue,
        status: product.status,
        profitMargin: productRevenue > 0 ? (productProfit / productRevenue) * 100 : 0,
        turnoverRate: product.stock > 0 ? productSales / product.stock : 0,
        stockStatus: product.stock === 0 ? t('out_of_stock') : product.stock <= product.min_stock ? t('low_stock') : t('good_stock')
      };
    });

    return productAnalysis.sort((a, b) => b.totalRevenue - a.totalRevenue);
  };

  const detailedProductAnalysis = getDetailedProductAnalysis();

  const getDetailedDebtAnalysis = () => {
    const debtAnalysis = debts.map(debt => {
      const customer = customers.find(c => c.id === debt.customer_id);
      const daysSinceCreated = Math.floor((new Date().getTime() - new Date(debt.created_at).getTime()) / (1000 * 60 * 60 * 24));
      
      return {
        id: debt.id,
        customerName: customer?.name || t('no_data'),
        customerPhone: customer?.phone || t('no_data'),
        totalAmount: Number(debt.total_amount),
        remainingAmount: Number(debt.remaining_amount),
        paidAmount: Number(debt.total_amount) - Number(debt.remaining_amount),
        status: debt.status,
        daysPending: daysSinceCreated,
        paymentProgress: Number(debt.total_amount) > 0 ? ((Number(debt.total_amount) - Number(debt.remaining_amount)) / Number(debt.total_amount)) * 100 : 0,
        priority: daysSinceCreated > 30 ? t('high_priority') : daysSinceCreated > 14 ? t('medium_priority') : t('low_priority'),
        createdAt: debt.created_at,
        notes: debt.notes || ''
      };
    });

    return debtAnalysis.sort((a, b) => b.remainingAmount - a.remainingAmount);
  };

  const detailedDebtAnalysis = getDetailedDebtAnalysis();

  const getDetailedInstallmentAnalysis = () => {
    const installmentAnalysis = installments.map(installment => {
      const customer = customers.find(c => c.id === installment.customer_id);
      const monthsRemaining = installment.months - installment.paid_months;
      const nextPaymentDue = new Date();
      nextPaymentDue.setMonth(nextPaymentDue.getMonth() + 1);
      
      return {
        id: installment.id,
        customerName: customer?.name || t('no_data'),
        customerPhone: customer?.phone || t('no_data'),
        totalAmount: Number(installment.total_amount),
        downPayment: Number(installment.down_payment),
        remainingAmount: Number(installment.remaining_amount),
        monthlyPayment: Number(installment.monthly_payment),
        totalMonths: installment.months,
        paidMonths: installment.paid_months,
        monthsRemaining: monthsRemaining,
        status: installment.status,
        paymentProgress: installment.months > 0 ? (installment.paid_months / installment.months) * 100 : 0,
        nextPaymentDue: nextPaymentDue.toISOString().split('T')[0],
        totalPaid: Number(installment.total_amount) - Number(installment.remaining_amount),
        priority: monthsRemaining <= 1 ? t('high_priority') : monthsRemaining <= 3 ? t('medium_priority') : t('low_priority'),
        createdAt: installment.created_at,
        notes: installment.notes || ''
      };
    });

    return installmentAnalysis.sort((a, b) => b.remainingAmount - a.remainingAmount);
  };

  const detailedInstallmentAnalysis = getDetailedInstallmentAnalysis();

  const getDailySalesData = () => {
    const dailyData = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateString = date.toDateString();
      
      const daySales = sales.filter(sale => 
        new Date(sale.created_at).toDateString() === dateString
      );
      
      const dayTotal = daySales.reduce((sum, sale) => sum + Number(sale.final_amount), 0);
      let dayProfit = 0;
      
      daySales.forEach(sale => {
        sale.sale_items?.forEach(item => {
          const product = products.find(p => p.id === item.product_id);
          if (product) {
            const itemCost = Number(product.cost_price) * item.quantity;
            const itemRevenue = Number(item.total_price);
            dayProfit += (itemRevenue - itemCost);
          }
        });
      });

      dailyData.push({
        name: date.toLocaleDateString('ar-IQ', { weekday: 'short', day: 'numeric' }),
        sales: dayTotal,
        profit: dayProfit,
        transactions: daySales.length,
        date: dateString
      });
    }
    return dailyData;
  };

  const dailySalesData = getDailySalesData();

  const getTopSellingProducts = () => {
    const productSales = new Map();
    
    sales.forEach(sale => {
      sale.sale_items?.forEach(item => {
        const product = products.find(p => p.id === item.product_id);
        if (product) {
          const existing = productSales.get(product.id) || {
            name: product.name,
            category: product.category?.name || t('no_data'),
            totalQuantity: 0,
            totalRevenue: 0,
            totalProfit: 0,
            transactionCount: 0
          };
          
          const itemCost = Number(product.cost_price) * item.quantity;
          const itemRevenue = Number(item.total_price);
          
          existing.totalQuantity += item.quantity;
          existing.totalRevenue += itemRevenue;
          existing.totalProfit += (itemRevenue - itemCost);
          existing.transactionCount += 1;
          
          productSales.set(product.id, existing);
        }
      });
    });
    
    return Array.from(productSales.values())
      .sort((a, b) => b.totalRevenue - a.totalRevenue)
      .slice(0, 10);
  };

  const topSellingProducts = getTopSellingProducts();

  const getCategoryData = () => {
    const categoryStats = new Map();
    
    sales.forEach(sale => {
      sale.sale_items?.forEach(item => {
        const product = products.find(p => p.id === item.product_id);
        if (product) {
          const categoryName = product.category?.name || t('no_data');
          const existing = categoryStats.get(categoryName) || {
            name: categoryName,
            sales: 0,
            count: 0
          };
          
          existing.sales += Number(item.total_price);
          existing.count += 1;
          categoryStats.set(categoryName, existing);
        }
      });
    });
    
    const total = Array.from(categoryStats.values()).reduce((sum, cat) => sum + cat.sales, 0);
    
    return Array.from(categoryStats.values()).map((cat, index) => ({
      ...cat,
      value: total > 0 ? Math.round((cat.sales / total) * 100) : 0,
      color: ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d'][index % 6]
    }));
  };

  const categoryData = getCategoryData();

  const getTopCustomers = () => {
    return customers
      .filter(customer => Number(customer.total_purchases) > 0)
      .sort((a, b) => Number(b.total_purchases) - Number(a.total_purchases))
      .slice(0, 8)
      .map(customer => {
        const customerSales = sales.filter(sale => sale.customer_id === customer.id);
        const customerDebts = debts.filter(debt => debt.customer_id === customer.id && debt.status === 'pending');
        const customerInstallments = installments.filter(inst => inst.customer_id === customer.id && inst.status === 'active');
        
        return {
          name: customer.name,
          totalPurchases: Number(customer.total_purchases),
          transactionCount: customerSales.length,
          pendingDebts: customerDebts.reduce((sum, debt) => sum + Number(debt.remaining_amount), 0),
          activeInstallments: customerInstallments.reduce((sum, inst) => sum + Number(inst.remaining_amount), 0),
          lastVisit: customerSales.length > 0 
            ? new Date(Math.max(...customerSales.map(sale => new Date(sale.created_at).getTime()))).toLocaleDateString('ar-IQ')
            : t('no_data'),
          phone: customer.phone || t('no_data')
        };
      });
  };

  const topCustomers = getTopCustomers();

  const getInventoryAnalysis = () => {
    const lowStock = products.filter(p => p.stock <= p.min_stock && p.stock > 0);
    const outOfStock = products.filter(p => p.stock === 0);
    const overStock = products.filter(p => p.stock > p.min_stock * 3);
    
    const totalInventoryValue = products.reduce((sum, product) => 
      sum + (Number(product.cost_price) * product.stock), 0
    );
    
    const totalRetailValue = products.reduce((sum, product) => 
      sum + (Number(product.price) * product.stock), 0
    );

    return {
      lowStock,
      outOfStock,
      overStock,
      totalInventoryValue,
      totalRetailValue,
      potentialProfit: totalRetailValue - totalInventoryValue
    };
  };

  const inventoryAnalysis = getInventoryAnalysis();

  const recentTransactions = sales.slice(0, 10).map(sale => {
    let totalCost = 0;
    let totalProfit = 0;
    
    sale.sale_items?.forEach(item => {
      const product = products.find(p => p.id === item.product_id);
      if (product) {
        const itemCost = Number(product.cost_price) * item.quantity;
        const itemRevenue = Number(item.total_price);
        totalCost += itemCost;
        totalProfit += (itemRevenue - itemCost);
      }
    });

    return {
      id: `#${sale.id.slice(-4)}`,
      date: new Date(sale.created_at).toLocaleDateString('ar-IQ'),
      time: new Date(sale.created_at).toLocaleTimeString('ar-IQ', { hour: '2-digit', minute: '2-digit' }),
      customer: sale.customer?.name || t('no_data'),
      customerPhone: sale.customer?.phone,
      customerAddress: sale.customer?.address,
      amount: Number(sale.final_amount),
      cost: totalCost,
      profit: totalProfit,
      type: sale.payment_method,
      items: sale.sale_items?.length || 0,
      status: 'مكتملة',
      profitMargin: totalCost > 0 ? ((totalProfit / Number(sale.final_amount)) * 100) : 0,
      discount: sale.discount ? Number(sale.discount) : 0,
      tax: sale.tax ? Number(sale.tax) : 0,
      transactionItems: sale.sale_items?.map(item => {
        const product = products.find(p => p.id === item.product_id);
        return {
          product_name: product?.name,
          quantity: item.quantity,
          unit_price: Number(item.unit_price),
          total_price: Number(item.total_price)
        };
      })
    };
  });

  const handleViewTransaction = (transaction: any) => {
    setSelectedTransaction(transaction);
    setShowTransactionDialog(true);
  };

  const handleExportReport = () => {
    toast({
      title: "جاري تصدير التقرير",
      description: "سيتم تحميل التقرير المفصل خلال لحظات",
    });
    
    setTimeout(() => {
      const reportData = {
        period: selectedPeriod,
        generatedAt: new Date().toISOString(),
        summary: advancedStats,
        dailySales: dailySalesData,
        topProducts: topSellingProducts,
        detailedProductAnalysis: detailedProductAnalysis,
        topCustomers: topCustomers,
        recentTransactions: recentTransactions,
        categoryBreakdown: categoryData,
        inventoryAnalysis: inventoryAnalysis,
        detailedDebtAnalysis: detailedDebtAnalysis,
        detailedInstallmentAnalysis: detailedInstallmentAnalysis,
        debtsAndInstallments: {
          totalDebts: advancedStats.totalDebts,
          totalInstallments: advancedStats.totalInstallments,
          debtCount: debts.filter(d => d.status === 'pending').length,
          installmentCount: installments.filter(i => i.status === 'active').length
        }
      };
      
      const dataStr = JSON.stringify(reportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `تقرير-مفصل-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      toast({
        title: "تم تصدير التقرير",
        description: "تم تحميل التقرير المفصل بنجاح",
      });
    }, 1500);
  };

  const handleSearchInvoice = async () => {
    if (!searchInvoiceId.trim()) {
      toast({
        title: t('error'),
        description: t('required_field'),
        variant: "destructive"
      });
      return;
    }

    try {
      // البحث في المبيعات المحلية
      const foundSale = sales.find(sale => 
        sale.id.slice(-4).toLowerCase() === searchInvoiceId.toLowerCase() ||
        sale.id.toLowerCase() === searchInvoiceId.toLowerCase()
      );

      if (foundSale) {
        // تحويل بيانات المبيعة إلى نفس تنسيق المعاملات
        let totalCost = 0;
        let totalProfit = 0;
        
        foundSale.sale_items?.forEach(item => {
          const product = products.find(p => p.id === item.product_id);
          if (product) {
            const itemCost = Number(product.cost_price) * item.quantity;
            const itemRevenue = Number(item.total_price);
            totalCost += itemCost;
            totalProfit += (itemRevenue - itemCost);
          }
        });

        const transactionDetails = {
          id: `#${foundSale.id.slice(-4)}`,
          date: new Date(foundSale.created_at).toLocaleDateString('ar-IQ'),
          time: new Date(foundSale.created_at).toLocaleTimeString('ar-IQ', { hour: '2-digit', minute: '2-digit' }),
          customer: foundSale.customer?.name || t('no_data'),
          customerPhone: foundSale.customer?.phone,
          customerAddress: foundSale.customer?.address,
          amount: Number(foundSale.final_amount),
          cost: totalCost,
          profit: totalProfit,
          type: foundSale.payment_method,
          items: foundSale.sale_items?.length || 0,
          status: 'مكتملة',
          profitMargin: totalCost > 0 ? ((totalProfit / Number(foundSale.final_amount)) * 100) : 0,
          discount: foundSale.discount ? Number(foundSale.discount) : 0,
          tax: foundSale.tax ? Number(foundSale.tax) : 0,
          transactionItems: foundSale.sale_items?.map(item => {
            const product = products.find(p => p.id === item.product_id);
            return {
              product_name: product?.name,
              quantity: item.quantity,
              unit_price: Number(item.unit_price),
              total_price: Number(item.total_price)
            };
          })
        };

        setSearchedTransaction(transactionDetails);
        setShowSearchDialog(true);
        toast({
          title: "تم العثور على الفاتورة",
          description: `فاتورة رقم ${searchInvoiceId} موجودة`,
        });
      } else {
        toast({
          title: "لم يتم العثور على الفاتورة",
          description: `لا توجد فاتورة برقم ${searchInvoiceId}`,
          variant: "destructive"
        });
        setSearchedTransaction(null);
      }
    } catch (error) {
      toast({
        title: t('error'),
        description: t('operation_failed'),
        variant: "destructive"
      });
    }
  };

  const chartConfig = {
    sales: {
      label: "المبيعات",
      color: "#2563eb",
    },
    profit: {
      label: "الربح",
      color: "#16a34a",
    },
    transactions: {
      label: "المعاملات",
      color: "#dc2626",
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">جاري تحميل التقارير المفصلة...</div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6 p-6 bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">التقارير والتحليلات الشاملة</h1>
            <p className="text-gray-600 mt-2">تحليل شامل ومفصل لأداء نقطة البيع والعمليات التجارية مع تفاصيل المنتجات والديون والأقساط</p>
          </div>
          <div className="flex gap-3">
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="اختر الفترة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="week">هذا الأسبوع</SelectItem>
                <SelectItem value="month">هذا الشهر</SelectItem>
                <SelectItem value="year">هذا العام</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleExportReport} className="bg-blue-600 hover:bg-blue-700">
              <Download className="w-4 h-4 ml-2" />
              تصدير التقرير الشامل
            </Button>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-green-100 rounded-xl">
                  <DollarSign className="w-8 h-8 text-green-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-gray-900">{advancedStats.totalSales.toLocaleString()} د.ع</p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                    <span className="text-sm text-green-600">هامش ربح {advancedStats.profitMargin.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-blue-100 rounded-xl">
                  <ShoppingCart className="w-8 h-8 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">عدد المعاملات</p>
                  <p className="text-2xl font-bold text-gray-900">{advancedStats.totalTransactions}</p>
                  <div className="flex items-center mt-2">
                    <span className="text-sm text-blue-600">متوسط الفاتورة: {advancedStats.averageTransaction.toLocaleString()} د.ع</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-purple-100 rounded-xl">
                  <TrendingUp className="w-8 h-8 text-purple-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">صافي الربح</p>
                  <p className="text-2xl font-bold text-gray-900">{advancedStats.totalProfit.toLocaleString()} د.ع</p>
                  <div className="flex items-center mt-2">
                    <span className="text-sm text-purple-600">كلفة: {advancedStats.totalCost.toLocaleString()} د.ع</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-orange-100 rounded-xl">
                  <CreditCard className="w-8 h-8 text-orange-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">الديون والأقساط</p>
                  <p className="text-2xl font-bold text-gray-900">{(advancedStats.totalDebts + advancedStats.totalInstallments).toLocaleString()} د.ع</p>
                  <div className="flex items-center mt-2">
                    <span className="text-sm text-orange-600">ديون: {advancedStats.totalDebts.toLocaleString()} | أقساط: {advancedStats.totalInstallments.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Inventory Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-red-100 rounded-xl">
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">مخزون منخفض</p>
                  <p className="text-2xl font-bold text-red-600">{inventoryAnalysis.lowStock.length}</p>
                  <p className="text-xs text-gray-500">منتج</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gray-100 rounded-xl">
                  <Package className="w-8 h-8 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">نفاد المخزون</p>
                  <p className="text-2xl font-bold text-gray-600">{inventoryAnalysis.outOfStock.length}</p>
                  <p className="text-xs text-gray-500">منتج</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-indigo-100 rounded-xl">
                  <DollarSign className="w-8 h-8 text-indigo-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">قيمة المخزون</p>
                  <p className="text-2xl font-bold text-indigo-600">{inventoryAnalysis.totalInventoryValue.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">د.ع (كلفة)</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-md">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-teal-100 rounded-xl">
                  <TrendingUp className="w-8 h-8 text-teal-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">ربح محتمل</p>
                  <p className="text-2xl font-bold text-teal-600">{inventoryAnalysis.potentialProfit.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">د.ع</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Enhanced Sales Chart */}
          <Card className="xl:col-span-2 border-0 shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-bold">تحليل المبيعات اليومية (آخر 7 أيام)</CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant={activeChart === 'sales' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setActiveChart('sales')}
                  >
                    المبيعات
                  </Button>
                  <Button
                    variant={activeChart === 'profit' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setActiveChart('profit')}
                  >
                    الأرباح
                  </Button>
                  <Button
                    variant={activeChart === 'transactions' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setActiveChart('transactions')}
                  >
                    المعاملات
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={dailySalesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar 
                      dataKey={activeChart} 
                      fill={chartConfig[activeChart as keyof typeof chartConfig]?.color} 
                      radius={[4, 4, 0, 0]}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="transactions" 
                      stroke="#dc2626" 
                      strokeWidth={2}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* Category Distribution */}
          <Card className="border-0 shadow-md">
            <CardHeader>
              <CardTitle className="text-xl font-bold">توزيع المبيعات حسب الفئة</CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={categoryData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </PieChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Product Analysis */}
        <Card className="border-0 shadow-md">
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Package className="w-6 h-6" />
              تحليل المنتجات المفصل
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">اسم المنتج</TableHead>
                    <TableHead className="text-right">الفئة</TableHead>
                    <TableHead className="text-right">المخزون</TableHead>
                    <TableHead className="text-right">حالة المخزون</TableHead>
                    <TableHead className="text-right">سعر الكلفة</TableHead>
                    <TableHead className="text-right">سعر البيع</TableHead>
                    <TableHead className="text-right">المبيعات</TableHead>
                    <TableHead className="text-right">الإيرادات</TableHead>
                    <TableHead className="text-right">الأرباح</TableHead>
                    <TableHead className="text-right">هامش الربح</TableHead>
                    <TableHead className="text-right">قيمة المخزون</TableHead>
                    <TableHead className="text-right">معدل الدوران</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {detailedProductAnalysis.slice(0, 20).map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{product.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <span className={`font-bold ${product.stock <= product.minStock ? 'text-red-600' : 'text-green-600'}`}>
                          {product.stock}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant={product.stockStatus === 'نفاد' ? 'destructive' : product.stockStatus === 'منخفض' ? 'secondary' : 'default'}>
                          {product.stockStatus}
                        </Badge>
                      </TableCell>
                      <TableCell>{product.costPrice.toLocaleString()} د.ع</TableCell>
                      <TableCell>{product.sellingPrice.toLocaleString()} د.ع</TableCell>
                      <TableCell className="font-bold">{product.totalSales}</TableCell>
                      <TableCell className="text-blue-600 font-bold">{product.totalRevenue.toLocaleString()} د.ع</TableCell>
                      <TableCell className="text-green-600 font-bold">{product.totalProfit.toLocaleString()} د.ع</TableCell>
                      <TableCell>
                        <Badge variant={product.profitMargin > 30 ? 'default' : product.profitMargin > 15 ? 'secondary' : 'destructive'}>
                          {product.profitMargin.toFixed(1)}%
                        </Badge>
                      </TableCell>
                      <TableCell>{product.stockValue.toLocaleString()} د.ع</TableCell>
                      <TableCell>
                        <Badge variant={product.turnoverRate > 2 ? 'default' : product.turnoverRate > 1 ? 'secondary' : 'outline'}>
                          {product.turnoverRate.toFixed(2)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Debt Analysis */}
        <Card className="border-0 shadow-md">
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <CreditCard className="w-6 h-6" />
              تحليل الديون المفصل
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">اسم العميل</TableHead>
                    <TableHead className="text-right">رقم الهاتف</TableHead>
                    <TableHead className="text-right">المبلغ الإجمالي</TableHead>
                    <TableHead className="text-right">المبلغ المدفوع</TableHead>
                    <TableHead className="text-right">المبلغ المتبقي</TableHead>
                    <TableHead className="text-right">نسبة السداد</TableHead>
                    <TableHead className="text-right">أيام التأخير</TableHead>
                    <TableHead className="text-right">الأولوية</TableHead>
                    <TableHead className="text-right">الحالة</TableHead>
                    <TableHead className="text-right">تاريخ الإنشاء</TableHead>
                    <TableHead className="text-right">ملاحظات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {detailedDebtAnalysis.map((debt) => (
                    <TableRow key={debt.id}>
                      <TableCell className="font-medium">{debt.customerName}</TableCell>
                      <TableCell>{debt.customerPhone}</TableCell>
                      <TableCell className="font-bold">{debt.totalAmount.toLocaleString()} د.ع</TableCell>
                      <TableCell className="text-green-600">{debt.paidAmount.toLocaleString()} د.ع</TableCell>
                      <TableCell className="text-red-600 font-bold">{debt.remainingAmount.toLocaleString()} د.ع</TableCell>
                      <TableCell>
                        <Badge variant={debt.paymentProgress > 50 ? 'default' : 'secondary'}>
                          {debt.paymentProgress.toFixed(1)}%
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className={`font-bold ${debt.daysPending > 30 ? 'text-red-600' : debt.daysPending > 14 ? 'text-orange-600' : 'text-green-600'}`}>
                          {debt.daysPending} يوم
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant={debt.priority === 'عالية' ? 'destructive' : debt.priority === 'متوسطة' ? 'secondary' : 'outline'}>
                          {debt.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={debt.status === 'pending' ? 'secondary' : 'default'}>
                          {debt.status === 'pending' ? 'معلق' : 'مسدد'}
                        </Badge>
                      </TableCell>
                      <TableCell>{new Date(debt.createdAt).toLocaleDateString('ar-IQ')}</TableCell>
                      <TableCell className="max-w-32 truncate">{debt.notes}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Installment Analysis */}
        <Card className="border-0 shadow-md">
          <CardHeader>
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <Calculator className="w-6 h-6" />
              تحليل الأقساط المفصل
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">اسم العميل</TableHead>
                    <TableHead className="text-right">رقم الهاتف</TableHead>
                    <TableHead className="text-right">المبلغ الإجمالي</TableHead>
                    <TableHead className="text-right">الدفعة المقدمة</TableHead>
                    <TableHead className="text-right">القسط الشهري</TableHead>
                    <TableHead className="text-right">المبلغ المتبقي</TableHead>
                    <TableHead className="text-right">الأشهر المدفوعة</TableHead>
                    <TableHead className="text-right">الأشهر المتبقية</TableHead>
                    <TableHead className="text-right">نسبة السداد</TableHead>
                    <TableHead className="text-right">تاريخ القسط القادم</TableHead>
                    <TableHead className="text-right">الأولوية</TableHead>
                    <TableHead className="text-right">الحالة</TableHead>
                    <TableHead className="text-right">ملاحظات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {detailedInstallmentAnalysis.map((installment) => (
                    <TableRow key={installment.id}>
                      <TableCell className="font-medium">{installment.customerName}</TableCell>
                      <TableCell>{installment.customerPhone}</TableCell>
                      <TableCell className="font-bold">{installment.totalAmount.toLocaleString()} د.ع</TableCell>
                      <TableCell className="text-blue-600">{installment.downPayment.toLocaleString()} د.ع</TableCell>
                      <TableCell className="text-purple-600 font-bold">{installment.monthlyPayment.toLocaleString()} د.ع</TableCell>
                      <TableCell className="text-red-600 font-bold">{installment.remainingAmount.toLocaleString()} د.ع</TableCell>
                      <TableCell>
                        <Badge variant="default">
                          {installment.paidMonths} / {installment.totalMonths}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className={`font-bold ${installment.monthsRemaining <= 1 ? 'text-red-600' : installment.monthsRemaining <= 3 ? 'text-orange-600' : 'text-green-600'}`}>
                          {installment.monthsRemaining} شهر
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant={installment.paymentProgress > 70 ? 'default' : installment.paymentProgress > 40 ? 'secondary' : 'outline'}>
                          {installment.paymentProgress.toFixed(1)}%
                        </Badge>
                      </TableCell>
                      <TableCell>{new Date(installment.nextPaymentDue).toLocaleDateString('ar-IQ')}</TableCell>
                      <TableCell>
                        <Badge variant={installment.priority === 'عالية' ? 'destructive' : installment.priority === 'متوسطة' ? 'secondary' : 'outline'}>
                          {installment.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={installment.status === 'active' ? 'default' : 'secondary'}>
                          {installment.status === 'active' ? 'نشط' : 'مكتمل'}
                        </Badge>
                      </TableCell>
                      <TableCell className="max-w-32 truncate">{installment.notes}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Products and Customers Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Products */}
          <Card className="border-0 shadow-md">
            <CardHeader>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <Package className="w-6 h-6" />
                أفضل المنتجات مبيعاً (تفصيلي)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topSellingProducts.slice(0, 8).map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">{product.name}</p>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">{product.category}</Badge>
                          <span className="text-sm text-gray-500">{product.totalQuantity} وحدة</span>
                          <span className="text-sm text-gray-400">•</span>
                          <span className="text-sm text-gray-500">{product.transactionCount} معاملة</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-gray-900">{product.totalRevenue.toLocaleString()} د.ع</p>
                      <p className="text-sm text-green-600">ربح: {product.totalProfit.toLocaleString()} د.ع</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Customers */}
          <Card className="border-0 shadow-md">
            <CardHeader>
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <Users className="w-6 h-6" />
                أفضل العملاء (تفصيلي)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topCustomers.map((customer, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-green-600">{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">{customer.name}</p>
                        <div className="flex items-center gap-2 text-xs">
                          <span className="text-gray-500">{customer.transactionCount} معاملة</span>
                          <span className="text-gray-400">•</span>
                          <span className="text-gray-500">آخر زيارة: {customer.lastVisit}</span>
                        </div>
                        {(customer.pendingDebts > 0 || customer.activeInstallments > 0) && (
                          <div className="flex items-center gap-2 mt-1">
                            {customer.pendingDebts > 0 && (
                              <Badge variant="destructive" className="text-xs">
                                دين: {customer.pendingDebts.toLocaleString()}
                              </Badge>
                            )}
                            {customer.activeInstallments > 0 && (
                              <Badge variant="outline" className="text-xs">
                                قسط: {customer.activeInstallments.toLocaleString()}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-gray-900">{customer.totalPurchases.toLocaleString()} د.ع</p>
                      <p className="text-xs text-gray-500">{customer.phone}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Recent Transactions with Search */}
        <Card className="border-0 shadow-md">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                <FileText className="w-6 h-6" />
                المعاملات الأخيرة المفصلة
              </CardTitle>
              
              {/* Invoice Search Section */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 bg-white rounded-lg border p-2">
                  <Search className="w-4 h-4 text-gray-500" />
                  <Input
                    placeholder="ابحث برقم الفاتورة..."
                    value={searchInvoiceId}
                    onChange={(e) => setSearchInvoiceId(e.target.value)}
                    className="border-0 focus-visible:ring-0 w-48"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleSearchInvoice();
                      }
                    }}
                  />
                  <Button 
                    onClick={handleSearchInvoice}
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    بحث
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">رقم الفاتورة</TableHead>
                    <TableHead className="text-right">التاريخ والوقت</TableHead>
                    <TableHead className="text-right">اسم العميل</TableHead>
                    <TableHead className="text-right">عدد الأصناف</TableHead>
                    <TableHead className="text-right">المبلغ الإجمالي</TableHead>
                    <TableHead className="text-right">الكلفة</TableHead>
                    <TableHead className="text-right">الربح</TableHead>
                    <TableHead className="text-right">هامش الربح</TableHead>
                    <TableHead className="text-right">نوع الدفع</TableHead>
                    <TableHead className="text-right">الحالة</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.id}</TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{transaction.date}</p>
                          <p className="text-sm text-gray-500">{transaction.time}</p>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{transaction.customer}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{transaction.items} صنف</Badge>
                      </TableCell>
                      <TableCell className="font-bold text-blue-600">
                        {transaction.amount.toLocaleString()} د.ع
                      </TableCell>
                      <TableCell className="text-red-600">
                        {transaction.cost.toLocaleString()} د.ع
                      </TableCell>
                      <TableCell className="text-green-600 font-bold">
                        {transaction.profit.toLocaleString()} د.ع
                      </TableCell>
                      <TableCell>
                        <Badge variant={transaction.profitMargin > 20 ? 'default' : transaction.profitMargin > 10 ? 'secondary' : 'destructive'}>
                          {transaction.profitMargin.toFixed(1)}%
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={transaction.type === 'نقدي' ? 'default' : 'secondary'}>
                          {transaction.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="default">
                          {transaction.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleViewTransaction(transaction)}
                          className="hover:bg-blue-50"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      <TransactionDetailsDialog
        open={showTransactionDialog}
        onOpenChange={setShowTransactionDialog}
        transaction={selectedTransaction}
      />

      {/* Search Result Dialog */}
      <TransactionDetailsDialog
        open={showSearchDialog}
        onOpenChange={setShowSearchDialog}
        transaction={searchedTransaction}
      />
    </>
  );
};

export default ReportsPage;
