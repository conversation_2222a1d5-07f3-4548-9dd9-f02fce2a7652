import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Printer, Download, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/contexts/SettingsContext';

interface ReceiptData {
  receiptNumber: string;
  timestamp: string;
  date: string;
  time: string;
  store: {
    name: string;
    address: string;
    phone: string;
    taxNumber: string;
  };
  customer: {
    id: string;
    name: string;
    phone: string;
    address: string;
  };
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  financial: {
    subtotal: number;
    tax: number;
    discount: number;
    total: number;
  };
  payment: {
    method: string;
    amount: number;
    change: number;
  };
  printSettings: {
    showLogo: boolean;
    showTaxNumber: boolean;
    showFooter: boolean;
    footerText: string;
  };
}

interface ReceiptGeneratorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  receiptData: ReceiptData | null;
  onPrint?: () => void;
  onSave?: () => void;
}

const ReceiptGenerator: React.FC<ReceiptGeneratorProps> = ({
  open,
  onOpenChange,
  receiptData,
  onPrint,
  onSave
}) => {
  const { toast } = useToast();
  const { settings } = useSettings();
  const printRef = useRef<HTMLDivElement>(null);

  if (!receiptData) return null;

  const handlePrint = async () => {
    try {
      console.log('Print button clicked - creating isolated receipt data');
      
      // Create isolated data for printing only
      const isolatedReceiptData = {
        receiptNumber: receiptData.receiptNumber,
        timestamp: receiptData.timestamp,
        date: receiptData.date,
        time: receiptData.time,
        store: { ...receiptData.store },
        customer: { ...receiptData.customer },
        items: receiptData.items.map(item => ({ ...item })),
        financial: { ...receiptData.financial },
        payment: { ...receiptData.payment },
        printSettings: { ...receiptData.printSettings }
      };

      if (printRef.current) {
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          const printContent = printRef.current.innerHTML;
          
          printWindow.document.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>فاتورة ${isolatedReceiptData.receiptNumber}</title>
                <style>
                  body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    direction: rtl;
                  }
                  .receipt { 
                    max-width: 300px; 
                    margin: 0 auto;
                    font-size: 12px;
                  }
                  .text-center { text-align: center; }
                  .font-bold { font-weight: bold; }
                  .border-t { border-top: 1px solid #000; }
                  .border-b { border-bottom: 1px solid #000; }
                  .py-2 { padding: 8px 0; }
                  .mb-2 { margin-bottom: 8px; }
                  .mb-4 { margin-bottom: 16px; }
                  .flex { display: flex; }
                  .justify-between { justify-content: space-between; }
                  .text-sm { font-size: 11px; }
                  @media print {
                    body { margin: 0; padding: 10px; }
                    .receipt { max-width: 100%; }
                  }
                </style>
              </head>
              <body>
                ${printContent}
              </body>
            </html>
          `);
          
          printWindow.document.close();
          printWindow.focus();
          
          setTimeout(() => {
            printWindow.print();
            printWindow.close();
            
            console.log('Receipt printed successfully without affecting system variables:', {
              receiptNumber: isolatedReceiptData.receiptNumber,
              itemsCount: isolatedReceiptData.items.length,
              total: isolatedReceiptData.financial.total,
              customerName: isolatedReceiptData.customer.name,
              paymentType: isolatedReceiptData.payment.method,
              isolatedOperation: true
            });
            
            toast({
              title: "تم طباعة الفاتورة",
              description: `تم طباعة فاتورة رقم ${isolatedReceiptData.receiptNumber}`,
            });
            
            if (onPrint) {
              onPrint();
            }
          }, 500);
        }
      }
    } catch (error) {
      console.error('Error printing receipt:', error);
      toast({
        title: "خطأ في الطباعة",
        description: "حدث خطأ أثناء طباعة الفاتورة",
        variant: "destructive"
      });
    }
  };

  const handleSave = async () => {
    try {
      console.log('Save button clicked - creating isolated receipt data');
      
      // Create isolated data for saving only
      const isolatedReceiptData = {
        receiptNumber: receiptData.receiptNumber,
        timestamp: receiptData.timestamp,
        date: receiptData.date,
        time: receiptData.time,
        store: { ...receiptData.store },
        customer: { ...receiptData.customer },
        items: receiptData.items.map(item => ({ ...item })),
        financial: { ...receiptData.financial },
        payment: { ...receiptData.payment },
        printSettings: { ...receiptData.printSettings }
      };

      // Create downloadable content
      const receiptText = `
فاتورة رقم: ${isolatedReceiptData.receiptNumber}
التاريخ: ${isolatedReceiptData.date}
الوقت: ${isolatedReceiptData.time}

${isolatedReceiptData.store.name}
${isolatedReceiptData.store.address}
${isolatedReceiptData.store.phone}

العميل: ${isolatedReceiptData.customer.name}
${isolatedReceiptData.customer.phone}

العناصر:
${isolatedReceiptData.items.map(item => 
  `${item.name} - ${item.quantity} × ${item.price.toLocaleString()} = ${item.total.toLocaleString()}`
).join('\n')}

المجموع الفرعي: ${isolatedReceiptData.financial.subtotal.toLocaleString()} د.ع
الخصم: ${isolatedReceiptData.financial.discount.toLocaleString()} د.ع
الضريبة: ${isolatedReceiptData.financial.tax.toLocaleString()} د.ع
الإجمالي: ${isolatedReceiptData.financial.total.toLocaleString()} د.ع

طريقة الدفع: ${isolatedReceiptData.payment.method}
المبلغ المدفوع: ${isolatedReceiptData.payment.amount.toLocaleString()} د.ع
الباقي: ${isolatedReceiptData.payment.change.toLocaleString()} د.ع

شكراً لزيارتكم
      `.trim();

      const blob = new Blob([receiptText], { type: 'text/plain;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `receipt-${isolatedReceiptData.receiptNumber}.txt`;
      link.click();
      window.URL.revokeObjectURL(url);

      console.log('Receipt saved successfully without affecting system variables:', {
        receiptNumber: isolatedReceiptData.receiptNumber,
        itemsCount: isolatedReceiptData.items.length,
        total: isolatedReceiptData.financial.total,
        customerName: isolatedReceiptData.customer.name,
        paymentType: isolatedReceiptData.payment.method,
        isolatedOperation: true
      });

      toast({
        title: "تم حفظ الفاتورة",
        description: `تم حفظ فاتورة رقم ${isolatedReceiptData.receiptNumber}`,
      });

      if (onSave) {
        onSave();
      }
    } catch (error) {
      console.error('Error saving receipt:', error);
      toast({
        title: "خطأ في الحفظ",
        description: "حدث خطأ أثناء حفظ الفاتورة",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>معاينة الفاتورة</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div ref={printRef} className="receipt bg-white p-4 border rounded-lg">
          <div className="text-center mb-4">
            <h2 className="font-bold text-lg">{receiptData.store.name}</h2>
            <p className="text-sm">{receiptData.store.address}</p>
            <p className="text-sm">{receiptData.store.phone}</p>
            {receiptData.printSettings.showTaxNumber && receiptData.store.taxNumber && (
              <p className="text-sm">الرقم الضريبي: {receiptData.store.taxNumber}</p>
            )}
          </div>

          <div className="text-center mb-4 border-t border-b py-2">
            <p className="font-bold">فاتورة رقم: {receiptData.receiptNumber}</p>
            <p className="text-sm">{new Date(receiptData.date).toLocaleDateString('ar-SA', {
              year: 'numeric',
              month: '2-digit', 
              day: '2-digit',
              calendar: 'gregory'
            })} - {receiptData.time}</p>
          </div>

          {receiptData.customer.name !== 'غير محدد' && (
            <div className="mb-4">
              <p className="font-bold">العميل: {receiptData.customer.name}</p>
              {receiptData.customer.phone && (
                <p className="text-sm">الهاتف: {receiptData.customer.phone}</p>
              )}
            </div>
          )}

          <div className="mb-4">
            <div className="border-b mb-2 pb-2">
              <div className="flex justify-between font-bold text-sm">
                <span>المجموع</span>
                <span>السعر</span>
                <span>الكمية</span>
                <span>الصنف</span>
              </div>
            </div>
            
            {receiptData.items.map((item, index) => (
              <div key={index} className="flex justify-between text-sm mb-1">
                <span>{item.total.toLocaleString()}</span>
                <span>{item.price.toLocaleString()}</span>
                <span>{item.quantity}</span>
                <span>{item.name}</span>
              </div>
            ))}
          </div>

          <div className="border-t pt-2 mb-4">
            <div className="flex justify-between mb-1">
              <span>المجموع الفرعي:</span>
              <span>{receiptData.financial.subtotal.toLocaleString()} د.ع</span>
            </div>
            
            {receiptData.financial.discount > 0 && (
              <div className="flex justify-between mb-1">
                <span>الخصم:</span>
                <span>-{receiptData.financial.discount.toLocaleString()} د.ع</span>
              </div>
            )}
            
            {receiptData.financial.tax > 0 && (
              <div className="flex justify-between mb-1">
                <span>الضريبة:</span>
                <span>{receiptData.financial.tax.toLocaleString()} د.ع</span>
              </div>
            )}
            
            <div className="flex justify-between font-bold border-t pt-1">
              <span>الإجمالي:</span>
              <span>{receiptData.financial.total.toLocaleString()} د.ع</span>
            </div>
          </div>

          <div className="mb-4">
            <div className="flex justify-between mb-1">
              <span>طريقة الدفع:</span>
              <span>{receiptData.payment.method}</span>
            </div>
            <div className="flex justify-between mb-1">
              <span>المبلغ المدفوع:</span>
              <span>{receiptData.payment.amount.toLocaleString()} د.ع</span>
            </div>
            {receiptData.payment.change > 0 && (
              <div className="flex justify-between">
                <span>الباقي:</span>
                <span>{receiptData.payment.change.toLocaleString()} د.ع</span>
              </div>
            )}
          </div>

          {receiptData.printSettings.showFooter && receiptData.printSettings.footerText && (
            <div className="text-center border-t pt-2">
              <p className="text-sm">{receiptData.printSettings.footerText}</p>
            </div>
          )}

          <div className="text-center mt-4">
            <p className="text-sm">شكراً لزيارتكم</p>
          </div>
        </div>

        <div className="flex gap-2 mt-4">
          <Button onClick={handlePrint} className="flex-1">
            <Printer className="w-4 h-4 ml-2" />
            طباعة
          </Button>
          <Button onClick={handleSave} variant="outline" className="flex-1">
            <Download className="w-4 h-4 ml-2" />
            حفظ
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReceiptGenerator;
