<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iradoo POS - نظام نقاط البيع</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 3rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            padding: 1rem;
            border-radius: 10px;
            margin: 2rem 0;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
            text-align: right;
            line-height: 1.6;
        }
        
        .instructions h3 {
            margin-bottom: 1rem;
            color: #ffd700;
        }
        
        .instructions ol {
            margin-right: 1rem;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
        }
        
        .electron-info {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(33, 150, 243, 0.2);
            border-radius: 10px;
            font-size: 0.9rem;
        }
        
        .version {
            margin-top: 1rem;
            opacity: 0.7;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏪 Iradoo POS</h1>
        <p class="subtitle">نظام نقاط البيع - إصدار سطح المكتب</p>
        
        <div class="status">
            <h3>✅ التطبيق يعمل بنجاح!</h3>
            <p>تم تحميل التطبيق بنجاح في بيئة Electron</p>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ol>
                <li>استبدل محتويات مجلد <code>webapp</code> بملفات تطبيقك</li>
                <li>تأكد من وجود ملف <code>index.html</code> في المجلد الرئيسي</li>
                <li>قم بتشغيل <code>npm run build</code> لبناء التطبيق</li>
                <li>ستجد ملف التثبيت في مجلد <code>dist</code></li>
            </ol>
        </div>
        
        <div class="electron-info" id="electronInfo">
            <strong>معلومات البيئة:</strong><br>
            <span id="platformInfo">جاري التحميل...</span>
        </div>
        
        <div class="version">
            الإصدار: <span id="versionInfo">1.0.0</span>
        </div>
    </div>

    <script>
        // Check if running in Electron and display info
        if (window.electronAPI) {
            document.getElementById('platformInfo').innerHTML = `
                المنصة: ${window.electronAPI.getPlatform()}<br>
                بيئة Electron: نعم<br>
                الإصدار: ${window.electronAPI.getVersion()}
            `;
            document.getElementById('versionInfo').textContent = window.electronAPI.getVersion();
        } else {
            document.getElementById('platformInfo').innerHTML = `
                بيئة Electron: لا<br>
                يعمل في متصفح عادي
            `;
        }
        
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Iradoo POS Desktop App loaded successfully!');
            
            // Add click effect to container
            const container = document.querySelector('.container');
            container.addEventListener('click', function() {
                this.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
