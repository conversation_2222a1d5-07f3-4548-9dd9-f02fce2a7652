@echo off
chcp 65001 >nul 2>&1
cls
echo ========================================
echo    Iradoo POS Desktop Setup
echo ========================================
echo.

echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please download and install Node.js from: https://nodejs.org
    echo.
    echo Opening download page...
    start https://nodejs.org/en/download/
    pause
    exit /b 1
)

echo Node.js found:
node --version

echo.
echo Checking npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available!
    pause
    exit /b 1
)

echo npm found:
npm --version

echo.
echo [1/2] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo [2/2] Testing application...
echo Do you want to run the app for testing? (y/n)
set /p choice=
if /i "%choice%"=="y" (
    echo Starting application...
    call npm start
)

echo.
echo ========================================
echo Setup completed successfully!
echo.
echo Available commands:
echo   npm start        - Run app for testing
echo   npm run build    - Build app for distribution
echo   build.bat        - Build app (Windows)
echo.
echo ========================================
pause
