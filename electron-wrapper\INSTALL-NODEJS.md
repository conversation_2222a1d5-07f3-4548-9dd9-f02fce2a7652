# 📥 تثبيت Node.js - دليل مفصل

## ❗ مطلوب: تثبيت Node.js أولاً

Node.js مطلوب لتشغيل Electron وبناء التطبيق.

## 🔽 خطوات التثبيت:

### 1. تحميل Node.js
- اذهب إلى: https://nodejs.org
- اختر **LTS** (Long Term Support) - الإصدار المستقر
- حمل النسخة المناسبة لنظامك:
  - **Windows x64** (64-bit) - الأكثر شيوعاً
  - **Windows x86** (32-bit) - للأنظمة القديمة

### 2. تثبيت Node.js
1. شغّل ملف التثبيت الذي حملته
2. اتبع المعالج بالإعدادات الافتراضية
3. تأكد من تحديد خيار **"Add to PATH"** (عادة محدد افتراضياً)
4. انتظر حتى اكتمال التثبيت

### 3. التحقق من التثبيت
1. افتح **Command Prompt** جديد (مهم: جديد!)
2. اكتب: `node --version`
3. يجب أن ترى رقم الإصدار (مثل: v18.17.0)
4. اكتب: `npm --version`
5. يجب أن ترى رقم إصدار npm (مثل: 9.6.7)

## ✅ بعد التثبيت:

1. **أعد تشغيل Command Prompt**
2. انتقل إلى مجلد المشروع
3. شغّل `check-nodejs.bat` للتأكد
4. إذا كان كل شيء يعمل، شغّل `simple-run.bat`

## 🔧 حل المشاكل الشائعة:

### "node is not recognized"
- تأكد من إعادة تشغيل Command Prompt بعد التثبيت
- تأكد من تحديد "Add to PATH" أثناء التثبيت
- أعد تشغيل الكمبيوتر إذا لزم الأمر

### "npm is not available"
- npm يأتي مع Node.js تلقائياً
- إذا لم يعمل، أعد تثبيت Node.js

### مشاكل الصلاحيات
- شغّل Command Prompt كـ Administrator
- أو ثبت Node.js كـ Administrator

## 📋 متطلبات النظام:

- **Windows 7** أو أحدث
- **50 MB** مساحة فارغة للـ Node.js
- **اتصال إنترنت** للتحميل والتثبيت

## 🎯 الخطوة التالية:

بعد تثبيت Node.js بنجاح:
```
شغّل: simple-run.bat
اختر: [1] Check Node.js installation
```

إذا ظهر ✅، فأنت جاهز للمتابعة!
