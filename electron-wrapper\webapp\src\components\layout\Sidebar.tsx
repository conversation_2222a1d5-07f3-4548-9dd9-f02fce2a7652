
import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, ShoppingCart, Package, Users, BarChart3, Settings, LogOut, Sparkles } from 'lucide-react';
import { useLocalAuth } from '../../hooks/useLocalAuth';
import { useSettings } from '@/contexts/SettingsContext';
import { Button } from '@/components/ui/button';

const Sidebar = () => {
  const { logout, user } = useLocalAuth();
  const { settings, t } = useSettings();

  const getNavigationItems = () => {
    return [
      { icon: Home, label: t('dashboard'), path: '/', gradient: 'from-blue-500 to-cyan-500' },
      { icon: ShoppingCart, label: t('sales'), path: '/sales', gradient: 'from-green-500 to-emerald-500' },
      { icon: Package, label: t('inventory'), path: '/inventory', gradient: 'from-purple-500 to-violet-500' },
      { icon: Users, label: t('customers'), path: '/customers', gradient: 'from-pink-500 to-rose-500' },
      { icon: BarChart3, label: t('reports'), path: '/reports', gradient: 'from-orange-500 to-amber-500' },
      { icon: Settings, label: t('settings'), path: '/settings', gradient: 'from-slate-500 to-gray-500' },
    ];
  };

  const getAppName = () => {
    return settings.storeName || 'أريدوو';
  };

  const getAppSubtitle = () => {
    return t('pos_system');
  };

  const getRoleTranslation = (role: string) => {
    if (role === 'admin') return t('admin');
    if (role === 'cashier') return t('cashier');
    return t('supervisor');
  };

  const navigationItems = getNavigationItems();

  return (
    <div className="w-80 bg-background shadow-lg border-r border-border flex flex-col min-h-screen">
      {/* Logo Section */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center shadow-sm">
            <Sparkles className="w-6 h-6 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground">
              {getAppName()}
            </h1>
            <p className="text-sm text-muted-foreground">{getAppSubtitle()}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => (
            <li key={item.path}>
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${
                    isActive
                      ? 'bg-primary text-primary-foreground shadow-sm'
                      : 'text-foreground hover:text-primary hover:bg-accent'
                  }`
                }
              >
                <div className="p-2 rounded-lg bg-accent">
                  <item.icon className="w-5 h-5" />
                </div>
                <span className="font-medium">{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>

      {/* User Section */}
      <div className="p-4 border-t border-border">
        <div className="bg-accent rounded-lg p-4 mb-4 border border-border">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-sm font-bold text-primary-foreground">
                {user?.name?.charAt(0) || 'م'}
              </span>
            </div>
            <div className="flex-1">
              <p className="text-foreground font-medium text-sm">{user?.name || t('user')}</p>
              <p className="text-muted-foreground text-xs">
                {getRoleTranslation(user?.role || 'cashier')}
              </p>
            </div>
          </div>
        </div>
        
        <Button
          onClick={logout}
          variant="outline"
          className="w-full flex items-center gap-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-950/50"
        >
          <LogOut className="w-4 h-4" />
          <span>{t('logout')}</span>
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;
