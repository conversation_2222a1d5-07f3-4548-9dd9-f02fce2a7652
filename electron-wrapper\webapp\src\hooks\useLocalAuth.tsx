
import React, { createContext, useContext, useState, useEffect } from 'react';
import { localUserManager, LocalUser, LoginSession } from '@/utils/localUserManager';

interface LocalAuthContextType {
  user: LocalUser | null;
  session: LoginSession | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  canManageUsers: boolean;
  isLicenseGeneratorUser: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
}

const LocalAuthContext = createContext<LocalAuthContextType | undefined>(undefined);

export const LocalAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<LocalUser | null>(null);
  const [session, setSession] = useState<LoginSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = () => {
    const currentSession = localUserManager.getCurrentSession();
    if (currentSession && currentSession.isActive) {
      setSession(currentSession);
      setUser(currentSession.user);
    }
    setIsLoading(false);
  };

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    const result = localUserManager.login(email, password);
    
    if (result.success && result.user) {
      const newSession = localUserManager.getCurrentSession();
      setSession(newSession);
      setUser(result.user);
    }
    
    return result;
  };

  const logout = () => {
    localUserManager.logout();
    setUser(null);
    setSession(null);
  };

  const isAuthenticated = !!session && !!user;
  const canManageUsers = user?.role === 'admin';
  const isLicenseGeneratorUser = user?.email === '<EMAIL>';

  return (
    <LocalAuthContext.Provider value={{ 
      user, 
      session, 
      isAuthenticated, 
      isLoading, 
      canManageUsers,
      isLicenseGeneratorUser,
      login, 
      logout 
    }}>
      {children}
    </LocalAuthContext.Provider>
  );
};

export const useLocalAuth = () => {
  const context = useContext(LocalAuthContext);
  if (context === undefined) {
    throw new Error('useLocalAuth must be used within a LocalAuthProvider');
  }
  return context;
};
