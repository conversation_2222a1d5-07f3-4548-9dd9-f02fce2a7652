import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, RefreshCw, Eye, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useLocalAuth } from '@/hooks/useLocalAuth';

interface SecurityAuditLog {
  id: string;
  action: string;
  table_name?: string;
  record_id?: string;
  user_id?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

const SecurityAuditLogs = () => {
  const { canManageUsers } = useLocalAuth();
  const [logs, setLogs] = useState<SecurityAuditLog[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (canManageUsers) {
      fetchLogs();
    }
  }, [canManageUsers]);

  const fetchLogs = async () => {
    setLoading(true);
    try {
      // Since we're using local authentication, we'll create mock audit logs for demonstration
      // In a real implementation, these would come from a local storage or API
      const mockLogs: SecurityAuditLog[] = [
        {
          id: '1',
          action: 'INSERT',
          table_name: 'products',
          record_id: 'prod-123',
          user_id: 'user-1',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0...',
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          action: 'UPDATE',
          table_name: 'sales',
          record_id: 'sale-456',
          user_id: 'user-2',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0...',
          created_at: new Date(Date.now() - 3600000).toISOString()
        },
        {
          id: '3',
          action: 'DELETE',
          table_name: 'customers',
          record_id: 'cust-789',
          user_id: 'user-1',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0...',
          created_at: new Date(Date.now() - 7200000).toISOString()
        }
      ];
      
      setLogs(mockLogs);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionColor = (action: string) => {
    switch (action.toUpperCase()) {
      case 'INSERT':
        return 'bg-green-100 text-green-800';
      case 'UPDATE':
        return 'bg-blue-100 text-blue-800';
      case 'DELETE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionLabel = (action: string) => {
    switch (action.toUpperCase()) {
      case 'INSERT':
        return 'إضافة';
      case 'UPDATE':
        return 'تعديل';
      case 'DELETE':
        return 'حذف';
      default:
        return action;
    }
  };

  const getTableLabel = (tableName?: string) => {
    switch (tableName) {
      case 'products':
        return 'المنتجات';
      case 'sales':
        return 'المبيعات';
      case 'profiles':
        return 'المستخدمين';
      case 'categories':
        return 'الفئات';
      case 'customers':
        return 'العملاء';
      case 'debts':
        return 'الديون';
      case 'installments':
        return 'الأقساط';
      default:
        return tableName || 'غير محدد';
    }
  };

  if (!canManageUsers) {
    return (
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          ليس لديك صلاحية للوصول إلى سجلات الأمان
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                سجلات الأمان والمراجعة
              </CardTitle>
              <CardDescription>
                عرض جميع العمليات الحساسة في النظام
              </CardDescription>
            </div>
            <Button 
              onClick={fetchLogs} 
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center p-8">جاري التحميل...</div>
          ) : logs.length === 0 ? (
            <div className="text-center p-8 text-gray-500">
              لا توجد سجلات أمان متاحة
            </div>
          ) : (
            <div className="space-y-4">
              {logs.map((log) => (
                <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <Badge className={getActionColor(log.action)}>
                          {getActionLabel(log.action)}
                        </Badge>
                        <span className="text-sm font-medium">
                          {getTableLabel(log.table_name)}
                        </span>
                        {log.record_id && (
                          <span className="text-xs text-gray-500">
                            ID: {log.record_id.substring(0, 8)}...
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">
                        المستخدم: {log.user_id ? log.user_id.substring(0, 8) + '...' : 'النظام'}
                      </p>
                      <p className="text-xs text-gray-400">
                        {new Date(log.created_at).toLocaleString('ar-SA', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="text-left">
                    {log.ip_address && (
                      <p className="text-xs text-gray-500">IP: {log.ip_address}</p>
                    )}
                    {log.user_agent && (
                      <p className="text-xs text-gray-400 max-w-xs truncate">
                        {log.user_agent}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityAuditLogs;
