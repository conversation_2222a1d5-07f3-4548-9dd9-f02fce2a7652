
-- Create security definer functions to check user roles without R<PERSON> recursion
CREATE OR R<PERSON><PERSON>CE FUNCTION public.get_current_user_role()
RETURNS TEXT AS $$
  SELECT role FROM public.profiles WHERE id = auth.uid();
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

CREATE OR REPLACE FUNCTION public.has_role(required_role TEXT)
RETURNS BOOLEAN AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = required_role
  );
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
  SELECT public.has_role('admin');
$$ LANGUAGE SQL SECURITY DEFINER STABLE;

-- Create security audit tables
CREATE TABLE IF NOT EXISTS public.security_audit (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  action text NOT NULL,
  table_name text,
  record_id uuid,
  old_values jsonb,
  new_values jsonb,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.failed_login_attempts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text NOT NULL,
  ip_address inet,
  attempted_at timestamp with time zone DEFAULT now(),
  user_agent text
);

CREATE TABLE IF NOT EXISTS public.user_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  session_token text NOT NULL,
  expires_at timestamp with time zone NOT NULL,
  ip_address inet,
  user_agent text,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  last_activity timestamp with time zone DEFAULT now()
);

-- Enable RLS on new security tables
ALTER TABLE public.security_audit ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.failed_login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.installments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.debt_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.installment_payments ENABLE ROW LEVEL SECURITY;

-- Remove overly permissive policies
DROP POLICY IF EXISTS "Authenticated users can access products" ON public.products;
DROP POLICY IF EXISTS "Authenticated users can access categories" ON public.categories;
DROP POLICY IF EXISTS "Authenticated users can access customers" ON public.customers;
DROP POLICY IF EXISTS "Authenticated users can access sales" ON public.sales;
DROP POLICY IF EXISTS "Authenticated users can access sale_items" ON public.sale_items;

-- Create role-based policies for products
CREATE POLICY "All authenticated users can view products" ON public.products
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins and supervisors can insert products" ON public.products
  FOR INSERT WITH CHECK (public.has_role('admin') OR public.has_role('supervisor'));

CREATE POLICY "Admins and supervisors can update products" ON public.products
  FOR UPDATE USING (public.has_role('admin') OR public.has_role('supervisor'));

CREATE POLICY "Only admins can delete products" ON public.products
  FOR DELETE USING (public.has_role('admin'));

-- Create role-based policies for categories
CREATE POLICY "All authenticated users can view categories" ON public.categories
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Only admins can manage categories" ON public.categories
  FOR ALL USING (public.has_role('admin'));

-- Create role-based policies for customers
CREATE POLICY "All authenticated users can view customers" ON public.customers
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can manage customers" ON public.customers
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can update customers" ON public.customers
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Only admins can delete customers" ON public.customers
  FOR DELETE USING (public.has_role('admin'));

-- Create role-based policies for sales
CREATE POLICY "All authenticated users can view sales" ON public.sales
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create sales" ON public.sales
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Only admins can delete sales" ON public.sales
  FOR DELETE USING (public.has_role('admin'));

-- Create role-based policies for sale_items
CREATE POLICY "All authenticated users can view sale_items" ON public.sale_items
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create sale_items" ON public.sale_items
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Only admins can delete sale_items" ON public.sale_items
  FOR DELETE USING (public.has_role('admin'));

-- Create policies for debts
CREATE POLICY "All authenticated users can view debts" ON public.debts
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create debts" ON public.debts
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Admins and supervisors can update debts" ON public.debts
  FOR UPDATE USING (public.has_role('admin') OR public.has_role('supervisor'));

CREATE POLICY "Only admins can delete debts" ON public.debts
  FOR DELETE USING (public.has_role('admin'));

-- Create policies for installments
CREATE POLICY "All authenticated users can view installments" ON public.installments
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create installments" ON public.installments
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Admins and supervisors can update installments" ON public.installments
  FOR UPDATE USING (public.has_role('admin') OR public.has_role('supervisor'));

CREATE POLICY "Only admins can delete installments" ON public.installments
  FOR DELETE USING (public.has_role('admin'));

-- Create policies for debt_payments
CREATE POLICY "All authenticated users can view debt_payments" ON public.debt_payments
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create debt_payments" ON public.debt_payments
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Only admins can delete debt_payments" ON public.debt_payments
  FOR DELETE USING (public.has_role('admin'));

-- Create policies for installment_payments
CREATE POLICY "All authenticated users can view installment_payments" ON public.installment_payments
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create installment_payments" ON public.installment_payments
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Only admins can delete installment_payments" ON public.installment_payments
  FOR DELETE USING (public.has_role('admin'));

-- Create policies for security audit (admin only)
CREATE POLICY "Only admins can view security_audit" ON public.security_audit
  FOR SELECT USING (public.has_role('admin'));

CREATE POLICY "System can insert security_audit" ON public.security_audit
  FOR INSERT WITH CHECK (true);

-- Create policies for failed_login_attempts (admin only)
CREATE POLICY "Only admins can view failed_login_attempts" ON public.failed_login_attempts
  FOR SELECT USING (public.has_role('admin'));

CREATE POLICY "System can insert failed_login_attempts" ON public.failed_login_attempts
  FOR INSERT WITH CHECK (true);

-- Create policies for user_sessions (admin only)
CREATE POLICY "Only admins can view user_sessions" ON public.user_sessions
  FOR SELECT USING (public.has_role('admin'));

CREATE POLICY "Users can view their own sessions" ON public.user_sessions
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can manage user_sessions" ON public.user_sessions
  FOR ALL WITH CHECK (true);

-- Create admin-only policies for profiles
CREATE POLICY "Admins can view all profiles" ON public.profiles
  FOR SELECT USING (public.has_role('admin'));

CREATE POLICY "Admins can update all profiles" ON public.profiles
  FOR UPDATE USING (public.has_role('admin'));

-- Create helper functions for security
CREATE OR REPLACE FUNCTION public.is_account_locked(user_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  attempt_count INTEGER;
  last_attempt TIMESTAMP WITH TIME ZONE;
BEGIN
  SELECT COUNT(*), MAX(attempted_at) 
  INTO attempt_count, last_attempt
  FROM public.failed_login_attempts 
  WHERE email = user_email 
  AND attempted_at > now() - interval '15 minutes';
  
  RETURN attempt_count >= 5 AND last_attempt > now() - interval '15 minutes';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit trigger function
CREATE OR REPLACE FUNCTION public.audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.security_audit (
    user_id,
    action,
    table_name,
    record_id,
    old_values,
    new_values
  ) VALUES (
    auth.uid(),
    TG_OP,
    TG_TABLE_NAME,
    COALESCE(NEW.id, OLD.id),
    CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
    CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN row_to_json(NEW) ELSE NULL END
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add audit triggers to sensitive tables
CREATE TRIGGER audit_profiles_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();

CREATE TRIGGER audit_products_trigger
  AFTER DELETE ON public.products
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();

CREATE TRIGGER audit_categories_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.categories
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();

CREATE TRIGGER audit_sales_trigger
  AFTER DELETE ON public.sales
  FOR EACH ROW EXECUTE FUNCTION public.audit_trigger_function();
