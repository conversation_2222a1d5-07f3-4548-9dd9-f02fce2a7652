
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { 
  getCategories, 
  addCategory, 
  deleteCategory, 
  type Category 
} from '@/utils/database';
import { Trash } from 'lucide-react';

interface CategoryManagerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  categories: Category[];
  onUpdateCategories: () => Promise<void>;
}

const CategoryManager: React.FC<CategoryManagerProps> = ({
  open,
  onOpenChange,
  categories,
  onUpdateCategories
}) => {
  const { toast } = useToast();
  const [newCategoryName, setNewCategoryName] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم الفئة",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoading(true);
      await addCategory({ name: newCategoryName.trim() });
      setNewCategoryName('');
      await onUpdateCategories();
    
      toast({
        title: "تم إضافة الفئة",
        description: "تم إضافة الفئة الجديدة بنجاح",
      });
    } catch (error) {
      console.error('Error adding category:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء إضافة الفئة",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      setLoading(true);
      await deleteCategory(categoryId);
      await onUpdateCategories();
      toast({
        title: "تم حذف الفئة",
        description: "تم حذف الفئة بنجاح",
      });
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء حذف الفئة",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>إدارة الفئات</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4">
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="اسم الفئة الجديدة"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              disabled={loading}
            />
            <Button onClick={handleAddCategory} disabled={loading}>
              {loading ? 'جاري الإضافة...' : 'إضافة فئة'}
            </Button>
          </div>
          
          {categories.length === 0 ? (
            <div className="text-center py-8 text-gray-500">لا توجد فئات.</div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {categories.map((category) => (
                <Card key={category.id}>
                  <CardContent className="flex items-center justify-between p-4">
                    <span>{category.name}</span>
                    <Button 
                      variant="destructive" 
                      size="icon" 
                      onClick={() => handleDeleteCategory(category.id)}
                      disabled={loading}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CategoryManager;
