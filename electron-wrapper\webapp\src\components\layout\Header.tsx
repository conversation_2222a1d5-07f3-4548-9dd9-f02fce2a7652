
import React from 'react';
import { <PERSON>, <PERSON>, Sun, Moon, User, Settings as SettingsIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useSettings } from '@/contexts/SettingsContext';
import { useLocalAuth } from '@/hooks/useLocalAuth';
import { useTheme } from '@/hooks/useTheme';

const Header = () => {
  const { settings } = useSettings();
  const { user } = useLocalAuth();
  const { actualTheme, toggleTheme } = useTheme();

  const getRoleTranslation = (role: string) => {
    if (role === 'admin') return settings.language === 'ar' ? 'مدير النظام' : settings.language === 'ku' ? 'بەڕێوەبەری سیستەم' : 'Admin';
    if (role === 'cashier') return settings.language === 'ar' ? 'كاشير' : settings.language === 'ku' ? 'کاشێر' : 'Cashier';
    return settings.language === 'ar' ? 'مشرف' : settings.language === 'ku' ? 'سەرپەرشتیار' : 'Supervisor';
  };

  const getWelcomeMessage = () => {
    const userName = user?.name || 'مستخدم';
    if (settings.language === 'ar') return `مرحباً بك، ${userName}`;
    if (settings.language === 'ku') return `بەخێربێیت، ${userName}`;
    return `Welcome, ${userName}`;
  };

  const getSearchPlaceholder = () => {
    if (settings.language === 'ar') return 'البحث في النظام...';
    if (settings.language === 'ku') return 'گەڕان لە سیستەمەکە...';
    return 'Search in system...';
  };

  return (
    <header className="bg-background border-b border-border p-6 mb-6 shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6 flex-1">
          {/* Welcome Section */}
          <div>
            <h2 className="text-2xl font-bold text-foreground">
              {getWelcomeMessage()}
            </h2>
            <div className="text-muted-foreground mt-1 flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              {new Date().toLocaleDateString(
                settings.language === 'ar' ? 'ar-SA' : 
                settings.language === 'ku' ? 'ku-IQ' : 'en-US', 
                { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                }
              )}
            </div>
          </div>
          
          {/* Search */}
          <div className="relative max-w-md flex-1">
            <Search className={`absolute ${settings.language === 'en' ? 'left-3' : 'right-3'} top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4`} />
            <Input
              placeholder={getSearchPlaceholder()}
              className={`${settings.language === 'en' ? 'pl-10 pr-4' : 'pr-10 pl-4'} h-10 ${settings.language === 'en' ? 'text-left' : 'text-right'} bg-background border-border`}
            />
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Theme Toggle Button */}
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={toggleTheme}
            className="w-10 h-10 rounded-lg border border-border bg-background hover:bg-accent"
          >
            {actualTheme === 'dark' ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
          </Button>

          <Button 
            variant="ghost" 
            size="icon" 
            className="w-10 h-10 rounded-lg border border-border bg-background hover:bg-accent relative"
          >
            <Bell className="w-4 h-4" />
            <Badge className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center p-0">
              3
            </Badge>
          </Button>

          {/* User Profile */}
          <div className="flex items-center gap-3 bg-accent rounded-lg p-3 border border-border">
            <div className={settings.language === 'en' ? 'text-left' : 'text-right'}>
              <p className="text-sm font-medium text-foreground">
                {user?.name || 'مستخدم'}
              </p>
              <div className="text-xs text-muted-foreground flex items-center gap-1">
                <User className="w-3 h-3" />
                {getRoleTranslation(user?.role || 'cashier')}
              </div>
            </div>
            <Avatar className="w-10 h-10 border-2 border-border">
              <AvatarFallback className="bg-primary text-primary-foreground font-medium">
                {user?.name?.charAt(0) || 'م'}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
