{"name": "iradoo-pos-desktop", "version": "1.0.0", "description": "Desktop wrapper for Iradoo POS application", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electron", "desktop", "pos", "iradoo"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.iradoo.pos.desktop", "productName": "Iradoo POS", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "webapp/**/*", "assets/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Iradoo POS"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}}}