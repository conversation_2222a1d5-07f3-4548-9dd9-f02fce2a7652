@echo off
echo ========================================
echo    نسخ ملفات تطبيق الويب
echo ========================================
echo.

set SOURCE_DIR=..\iradoo-pos-nexus-main
set WEBAPP_DIR=webapp
set BUILD_DIR=%SOURCE_DIR%\dist

echo البحث عن ملفات تطبيق الويب...

REM Check if source directory exists
if not exist "%SOURCE_DIR%" (
    echo خطأ: لم يتم العثور على مجلد المصدر: %SOURCE_DIR%
    echo يرجى التأكد من وجود مجلد iradoo-pos-nexus-main في المجلد الأب
    pause
    exit /b 1
)

echo تم العثور على مجلد المصدر: %SOURCE_DIR%

REM Check if build directory exists
if exist "%BUILD_DIR%" (
    echo تم العثور على مجلد البناء: %BUILD_DIR%
    echo هل تريد نسخ ملفات البناء؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo نسخ ملفات البناء...
        if exist "%WEBAPP_DIR%" rmdir /s /q "%WEBAPP_DIR%"
        xcopy "%BUILD_DIR%\*" "%WEBAPP_DIR%\" /E /I /Y
        if %errorlevel% equ 0 (
            echo تم نسخ ملفات البناء بنجاح!
        ) else (
            echo خطأ في نسخ ملفات البناء!
        )
        goto :end
    )
)

REM If no build directory, check for source files
echo لم يتم العثور على مجلد البناء أو اخترت عدم النسخ
echo هل تريد بناء التطبيق أولاً؟ (y/n)
set /p build_choice=
if /i "%build_choice%"=="y" (
    echo الانتقال إلى مجلد المصدر وبناء التطبيق...
    cd /d "%SOURCE_DIR%"
    
    REM Check if package.json exists
    if not exist "package.json" (
        echo خطأ: لم يتم العثور على package.json في مجلد المصدر
        cd /d "%~dp0"
        pause
        exit /b 1
    )
    
    echo تثبيت التبعيات...
    call npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت التبعيات!
        cd /d "%~dp0"
        pause
        exit /b 1
    )
    
    echo بناء التطبيق...
    call npm run build
    if %errorlevel% neq 0 (
        echo خطأ في بناء التطبيق!
        cd /d "%~dp0"
        pause
        exit /b 1
    )
    
    echo العودة إلى مجلد Electron...
    cd /d "%~dp0"
    
    REM Copy built files
    if exist "%BUILD_DIR%" (
        echo نسخ ملفات البناء الجديدة...
        if exist "%WEBAPP_DIR%" rmdir /s /q "%WEBAPP_DIR%"
        xcopy "%BUILD_DIR%\*" "%WEBAPP_DIR%\" /E /I /Y
        if %errorlevel% equ 0 (
            echo تم نسخ ملفات البناء بنجاح!
        ) else (
            echo خطأ في نسخ ملفات البناء!
        )
    ) else (
        echo خطأ: لم يتم إنشاء مجلد البناء!
    )
) else (
    echo هل تريد نسخ ملفات المصدر مباشرة؟ (للتطوير فقط) (y/n)
    set /p source_choice=
    if /i "%source_choice%"=="y" (
        echo نسخ ملفات المصدر...
        if exist "%WEBAPP_DIR%" rmdir /s /q "%WEBAPP_DIR%"
        xcopy "%SOURCE_DIR%\*" "%WEBAPP_DIR%\" /E /I /Y /EXCLUDE:copy-exclude.txt
        if %errorlevel% equ 0 (
            echo تم نسخ ملفات المصدر بنجاح!
            echo ملاحظة: قد تحتاج لتعديل المسارات في ملفات HTML
        ) else (
            echo خطأ في نسخ ملفات المصدر!
        )
    )
)

:end
echo.
if exist "%WEBAPP_DIR%\index.html" (
    echo ✅ تم العثور على index.html في مجلد webapp
    echo يمكنك الآن تشغيل: npm start للاختبار
    echo أو تشغيل: build.bat لبناء التطبيق
) else (
    echo ❌ لم يتم العثور على index.html في مجلد webapp
    echo يرجى التأكد من نسخ الملفات بشكل صحيح
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
