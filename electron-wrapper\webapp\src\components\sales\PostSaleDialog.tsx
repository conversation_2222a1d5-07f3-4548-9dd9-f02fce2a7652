import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent } from '@/components/ui/card';
import { Receipt, CreditCard, Calendar, FileText, UserPlus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import { addCustomer, type Customer } from '@/utils/database';

interface PostSaleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  saleData: any;
  onCompleteSale: (postSaleOptions: any) => void;
}

const PostSaleDialog = ({ open, onOpenChange, saleData, onCompleteSale }: PostSaleDialogProps) => {
  const { toast } = useToast();
  const { formatCurrency } = useSettings();
  const [saleType, setSaleType] = useState('cash');
  const [downPayment, setDownPayment] = useState(0);
  const [monthlyPayment, setMonthlyPayment] = useState(0);
  const [months, setMonths] = useState(12);
  const [notes, setNotes] = useState('');
  const [printReceipt, setPrintReceipt] = useState(true);
  const [sendWhatsApp, setSendWhatsApp] = useState(false);
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [newCustomerData, setNewCustomerData] = useState({
    name: '',
    phone: '',
    email: '',
    address: ''
  });

  const handleAddNewCustomer = async () => {
    if (!newCustomerData.name.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم العميل",
        variant: "destructive"
      });
      return;
    }

    try {
      await addCustomer({
        name: newCustomerData.name,
        phone: newCustomerData.phone || undefined,
        email: newCustomerData.email || undefined,
        address: newCustomerData.address || undefined,
        notes: undefined
      });

      setShowAddCustomer(false);
      setNewCustomerData({ name: '', phone: '', email: '', address: '' });
      
      toast({
        title: "تم إضافة العميل",
        description: "تم إضافة العميل الجديد بنجاح",
      });
    } catch (error) {
      console.error('Error adding customer:', error);
      toast({
        title: "خطأ في إضافة العميل",
        description: "حدث خطأ أثناء إضافة العميل",
        variant: "destructive"
      });
    }
  };

  const handleCompleteSale = () => {
    const postSaleOptions = {
      saleType,
      downPayment,
      monthlyPayment,
      months,
      notes,
      printReceipt,
      sendWhatsApp
    };

    onCompleteSale(postSaleOptions);
  };

  if (!saleData) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5" />
            إعدادات ما بعد البيع
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Sale Summary */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-3">ملخص البيع</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>عدد المنتجات:</span>
                  <span>{saleData.cart?.length || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span>العميل:</span>
                  <span>{saleData.selectedCustomer?.name || 'بدون عميل'}</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span>المجموع النهائي:</span>
                  <span className="text-green-600">{formatCurrency(saleData.total || 0)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sale Type */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">نوع البيع</Label>
            <RadioGroup value={saleType} onValueChange={setSaleType}>
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="cash" id="cash" />
                <Label htmlFor="cash">نقدي</Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="debt" id="debt" />
                <Label htmlFor="debt">دين (آجل)</Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="installment" id="installment" />
                <Label htmlFor="installment">تقسيط</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Installment Options */}
          {saleType === 'installment' && (
            <Card>
              <CardContent className="p-4 space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  إعدادات التقسيط
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>الدفعة المقدمة</Label>
                    <Input
                      type="number"
                      value={downPayment}
                      onChange={(e) => setDownPayment(parseFloat(e.target.value) || 0)}
                      min="0"
                      max={saleData.total}
                    />
                  </div>
                  <div>
                    <Label>عدد الأشهر</Label>
                    <Input
                      type="number"
                      value={months}
                      onChange={(e) => {
                        const newMonths = parseInt(e.target.value) || 1;
                        setMonths(newMonths);
                        setMonthlyPayment((saleData.total - downPayment) / newMonths);
                      }}
                      min="1"
                      max="60"
                    />
                  </div>
                </div>
                <div>
                  <Label>القسط الشهري</Label>
                  <Input
                    type="number"
                    value={monthlyPayment}
                    onChange={(e) => setMonthlyPayment(parseFloat(e.target.value) || 0)}
                    min="0"
                  />
                </div>
                <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                  <p>المبلغ المتبقي: {formatCurrency((saleData.total || 0) - downPayment)}</p>
                  <p>إجمالي الأقساط: {formatCurrency(monthlyPayment * months)}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label>ملاحظات (اختياري)</Label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="أدخل أي ملاحظات إضافية..."
              rows={3}
            />
          </div>

          {/* Post Sale Actions */}
          <Card>
            <CardContent className="p-4 space-y-4">
              <h3 className="font-semibold">إجراءات ما بعد البيع</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="printReceipt"
                    checked={printReceipt}
                    onChange={(e) => setPrintReceipt(e.target.checked)}
                  />
                  <Label htmlFor="printReceipt">طباعة الفاتورة</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="sendWhatsApp"
                    checked={sendWhatsApp}
                    onChange={(e) => setSendWhatsApp(e.target.checked)}
                    disabled={!saleData.selectedCustomer?.phone}
                  />
                  <Label htmlFor="sendWhatsApp">
                    إرسال رسالة واتساب للعميل
                    {!saleData.selectedCustomer?.phone && (
                      <span className="text-gray-500 text-sm"> (لا يوجد رقم هاتف)</span>
                    )}
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Add Customer Option */}
          {!saleData.selectedCustomer && (
            <Card>
              <CardContent className="p-4">
                {!showAddCustomer ? (
                  <Button 
                    onClick={() => setShowAddCustomer(true)}
                    variant="outline"
                    className="w-full"
                  >
                    <UserPlus className="w-4 h-4 ml-2" />
                    إضافة عميل جديد
                  </Button>
                ) : (
                  <div className="space-y-4">
                    <h3 className="font-semibold">إضافة عميل جديد</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>اسم العميل *</Label>
                        <Input
                          value={newCustomerData.name}
                          onChange={(e) => setNewCustomerData({...newCustomerData, name: e.target.value})}
                          placeholder="أدخل اسم العميل"
                        />
                      </div>
                      <div>
                        <Label>رقم الهاتف</Label>
                        <Input
                          value={newCustomerData.phone}
                          onChange={(e) => setNewCustomerData({...newCustomerData, phone: e.target.value})}
                          placeholder="05xxxxxxxx"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>البريد الإلكتروني</Label>
                        <Input
                          value={newCustomerData.email}
                          onChange={(e) => setNewCustomerData({...newCustomerData, email: e.target.value})}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <Label>العنوان</Label>
                        <Input
                          value={newCustomerData.address}
                          onChange={(e) => setNewCustomerData({...newCustomerData, address: e.target.value})}
                          placeholder="أدخل العنوان"
                        />
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button onClick={handleAddNewCustomer}>
                        إضافة العميل
                      </Button>
                      <Button variant="outline" onClick={() => setShowAddCustomer(false)}>
                        إلغاء
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            إلغاء
          </Button>
          <Button onClick={handleCompleteSale} className="bg-green-600 hover:bg-green-700">
            <Receipt className="w-4 h-4 ml-2" />
            إتمام البيع
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PostSaleDialog;
