import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  Receipt, 
  CreditCard,
  Package,
  DollarSign,
  Users,
  BarChart3,
  Calculator
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import { 
  getProducts, 
  getCustomers, 
  createSale, 
  createDebt, 
  createInstallment,
  type Product, 
  type Customer 
} from '@/utils/database';
import PostSaleDialog from './PostSaleDialog';
import ReceiptDialog from './ReceiptDialog';

interface CartItem {
  product: Product;
  quantity: number;
  price: number;
}

const SalesPage = () => {
  const { toast } = useToast();
  const { formatCurrency, settings } = useSettings();
  const [products, setProducts] = useState<Product[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [discount, setDiscount] = useState(0);
  const [tax, setTax] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState('نقدي');
  const [loading, setLoading] = useState(true);
  const [showPostSaleDialog, setShowPostSaleDialog] = useState(false);
  const [currentSaleData, setCurrentSaleData] = useState<any>(null);
  const [showReceiptDialog, setShowReceiptDialog] = useState(false);
  const [receiptData, setReceiptData] = useState<any>(null);
  const [cashReceived, setCashReceived] = useState(0);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [productsData, customersData] = await Promise.all([
        getProducts(),
        getCustomers()
      ]);
      setProducts(productsData);
      setCustomers(customersData);
    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "خطأ في تحميل البيانات",
        description: "حدث خطأ أثناء تحميل بيانات المنتجات والعملاء",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBarcodeSearch = (value: string) => {
    setSearchTerm(value);
    
    // البحث عن منتج بالباركود المطابق تماماً
    const productByBarcode = products.find(product => 
      product.barcode && product.barcode === value.trim()
    );
    
    // إذا تم العثور على منتج بالباركود المطابق، أضفه للسلة مباشرة
    if (productByBarcode && value.trim().length > 0) {
      addToCart(productByBarcode);
      setSearchTerm(''); // مسح حقل البحث بعد الإضافة
      toast({
        title: "تم إضافة المنتج",
        description: `تم إضافة ${productByBarcode.name} إلى السلة`,
      });
    }
  };

  const addToCart = (product: Product) => {
    const existingItem = cart.find(item => item.product.id === product.id);
    
    if (existingItem) {
      setCart(cart.map(item => 
        item.product.id === product.id 
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { product, quantity: 1, price: product.price }]);
    }
  };

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.product.id !== productId));
  };

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }
    
    setCart(cart.map(item => 
      item.product.id === productId 
        ? { ...item, quantity }
        : item
    ));
  };

  const updatePrice = (productId: string, price: number) => {
    setCart(cart.map(item => 
      item.product.id === productId 
        ? { ...item, price }
        : item
    ));
  };

  const calculateSubtotal = () => {
    return cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const discountAmount = (subtotal * discount) / 100;
    const taxAmount = ((subtotal - discountAmount) * tax) / 100;
    return subtotal - discountAmount + taxAmount;
  };

  const calculateChange = () => {
    return cashReceived - calculateTotal();
  };

  const handleCompleteSale = async (postSaleOptions: any) => {
    if (cart.length === 0) {
      toast({
        title: "خطأ",
        description: "لا توجد منتجات في السلة",
        variant: "destructive"
      });
      return;
    }

    try {
      const saleData = {
        customer_id: selectedCustomer?.id,
        total_amount: calculateSubtotal(),
        discount,
        tax,
        final_amount: calculateTotal(),
        payment_method: paymentMethod
      };

      const saleItems = cart.map(item => ({
        product_id: item.product.id,
        quantity: item.quantity,
        unit_price: item.price,
        total_price: item.price * item.quantity
      }));

      const sale = await createSale(saleData, saleItems);

      // Handle post-sale options
      if (postSaleOptions.saleType === 'debt' && selectedCustomer) {
        await createDebt({
          customer_id: selectedCustomer.id,
          total_amount: calculateTotal(),
          remaining_amount: calculateTotal(),
          status: 'pending',
          notes: postSaleOptions.notes || '',
          items: cart
        });
      } else if (postSaleOptions.saleType === 'installment' && selectedCustomer) {
        await createInstallment({
          customer_id: selectedCustomer.id,
          total_amount: calculateTotal(),
          remaining_amount: calculateTotal() - postSaleOptions.downPayment,
          monthly_payment: postSaleOptions.monthlyPayment,
          months: postSaleOptions.months,
          paid_months: 0,
          down_payment: postSaleOptions.downPayment,
          status: 'active',
          notes: postSaleOptions.notes || '',
          items: cart
        });
      }

      // إعداد بيانات الفاتورة
      const receiptInfo = {
        id: sale.id,
        cart: [...cart],
        selectedCustomer: selectedCustomer,
        total: calculateTotal(),
        subtotal: calculateSubtotal(),
        discount: discount,
        tax: tax,
        paymentMethod: paymentMethod,
        timestamp: new Date().toISOString()
      };

      // Clear cart and reset form
      setCart([]);
      setSelectedCustomer(null);
      setDiscount(0);
      setTax(0);
      setPaymentMethod('نقدي');
      setShowPostSaleDialog(false);

      // عرض الفاتورة
      setReceiptData(receiptInfo);
      setShowReceiptDialog(true);

      // طباعة تلقائية إذا كانت مفعلة في الإعدادات
      if (postSaleOptions.printReceipt && settings.autoPrint) {
        setTimeout(() => {
          // سيتم تنفيذ الطباعة من خلال مكون الفاتورة
        }, 1000);
      }

      toast({
        title: "تم إتمام البيع بنجاح",
        description: `تم بيع ${receiptInfo.cart.length} منتج بقيمة ${formatCurrency(receiptInfo.total)}`,
      });

    } catch (error) {
      console.error('Error completing sale:', error);
      toast({
        title: "خطأ في إتمام البيع",
        description: "حدث خطأ أثناء إتمام عملية البيع",
        variant: "destructive"
      });
    }
  };

  const filteredProducts = products.filter(product => 
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.barcode && product.barcode.includes(searchTerm))
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent mx-auto"></div>
          <div className="text-xl font-semibold text-gray-700">جاري تحميل بيانات المبيعات...</div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 animate-fade-in">
                <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-primary to-primary/60 flex items-center justify-center shadow-lg hover-glow">
                  <ShoppingCart className="w-8 h-8 text-primary-foreground" />
                </div>
                <div>
                  <h1 className="text-3xl font-extrabold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    نقطة البيع
                  </h1>
                  <p className="text-base text-muted-foreground font-semibold">إدارة المبيعات والفواتير</p>
                </div>
              </div>
              <Badge variant="secondary" className="text-lg px-4 py-2 bg-green-100 text-green-800 border-green-200">
                <Package className="w-5 h-5 ml-2" />
                {cart.length} منتج في السلة
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Products Section */}
            <div className="lg:col-span-2 space-y-6">
              {/* Search */}
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                <CardContent className="p-4">
                  <div className="relative">
                    <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      placeholder="البحث بالاسم أو مسح الباركود..."
                      value={searchTerm}
                      onChange={(e) => handleBarcodeSearch(e.target.value)}
                      className="pr-12 h-12 text-lg border-2 border-gray-200 focus:border-blue-400 rounded-xl bg-white/50"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Products Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {filteredProducts.map((product) => (
                  <Card key={product.id} className="group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-0 bg-white/90 backdrop-blur-sm cursor-pointer" onClick={() => addToCart(product)}>
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        {product.image && (
                          <img 
                            src={product.image} 
                            alt={product.name}
                            className="w-full h-32 object-cover rounded-lg"
                          />
                        )}
                        <div>
                          <h3 className="font-bold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                            {product.name}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {product.category?.name || 'بدون فئة'}
                          </p>
                          {product.barcode && (
                            <p className="text-xs text-gray-400 font-mono">
                              {product.barcode}
                            </p>
                          )}
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-bold text-green-600">
                            {formatCurrency(product.price)}
                          </span>
                          <Badge variant={product.stock > product.min_stock ? 'default' : product.stock > 0 ? 'secondary' : 'destructive'}>
                            {product.stock} متوفر
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Cart Section */}
            <div className="space-y-6">
              <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingCart className="w-5 h-5" />
                    سلة المشتريات ({cart.length})
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {cart.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">
                      <ShoppingCart className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                      <p>السلة فارغة</p>
                    </div>
                  ) : (
                    <>
                      {cart.map((item) => {
                        const remainingStock = item.product.stock - item.quantity;
                        return (
                          <div key={item.product.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-semibold">{item.product.name}</h4>
                              <div className="flex items-center gap-2 mt-2">
                                <Button 
                                  size="sm" 
                                  variant="outline" 
                                  onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                                >
                                  <Minus className="w-4 h-4" />
                                </Button>
                                <span className="mx-2 font-medium">{item.quantity}</span>
                                <Button 
                                  size="sm" 
                                  variant="outline" 
                                  onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                                >
                                  <Plus className="w-4 h-4" />
                                </Button>
                                <Button 
                                  size="sm" 
                                  variant="destructive" 
                                  onClick={() => removeFromCart(item.product.id)}
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                              <div className="mt-2 flex items-center gap-2">
                                <Input
                                  type="number"
                                  value={item.price}
                                  onChange={(e) => updatePrice(item.product.id, parseFloat(e.target.value))}
                                  className="w-24 h-8 text-sm"
                                />
                                <Badge 
                                  variant={remainingStock >= 0 ? 'secondary' : 'destructive'}
                                  className="text-xs"
                                >
                                  متبقي: {remainingStock}
                                </Badge>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-green-600">
                                {formatCurrency(item.price * item.quantity)}
                              </p>
                            </div>
                          </div>
                        );
                      })}

                      <Separator />

                      {/* Customer Selection */}
                      <div className="space-y-2">
                        <Label>العميل (اختياري)</Label>
                        <select 
                          value={selectedCustomer?.id || ''} 
                          onChange={(e) => {
                            const customer = customers.find(c => c.id === e.target.value);
                            setSelectedCustomer(customer || null);
                          }}
                          className="w-full p-2 border rounded-lg"
                        >
                          <option value="">بدون عميل</option>
                          {customers.map(customer => (
                            <option key={customer.id} value={customer.id}>
                              {customer.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Discount and Tax */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>الخصم (%)</Label>
                          <Input
                            type="number"
                            value={discount}
                            onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
                            min="0"
                            max="100"
                          />
                        </div>
                        <div>
                          <Label>الضريبة (%)</Label>
                          <Input
                            type="number"
                            value={tax}
                            onChange={(e) => setTax(parseFloat(e.target.value) || 0)}
                            min="0"
                            max="100"
                          />
                        </div>
                      </div>

                      {/* Totals */}
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>المجموع الفرعي:</span>
                          <span>{formatCurrency(calculateSubtotal())}</span>
                        </div>
                        {discount > 0 && (
                          <div className="flex justify-between text-red-600">
                            <span>الخصم ({discount}%):</span>
                            <span>-{formatCurrency((calculateSubtotal() * discount) / 100)}</span>
                          </div>
                        )}
                        {tax > 0 && (
                          <div className="flex justify-between text-blue-600">
                            <span>الضريبة ({tax}%):</span>
                            <span>+{formatCurrency(((calculateSubtotal() - (calculateSubtotal() * discount) / 100) * tax) / 100)}</span>
                          </div>
                        )}
                        <Separator />
                        <div className="flex justify-between font-bold text-lg">
                          <span>المجموع النهائي:</span>
                          <span className="text-green-600">{formatCurrency(calculateTotal())}</span>
                        </div>
                      </div>

                      {/* Cash Calculator - Only show for cash payments */}
                      {paymentMethod === 'نقدي' && (
                        <Card className="bg-blue-50 border-blue-200">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm flex items-center gap-2">
                              <Calculator className="w-4 h-4" />
                              حساب الباقي
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <div>
                              <Label className="text-sm">المبلغ المستلم</Label>
                              <Input
                                type="number"
                                value={cashReceived}
                                onChange={(e) => setCashReceived(parseFloat(e.target.value) || 0)}
                                min="0"
                                className="text-lg font-semibold text-center"
                                placeholder="أدخل المبلغ المستلم"
                              />
                            </div>
                            <div className="text-center">
                              <div className="text-sm text-gray-600">المطلوب: {formatCurrency(calculateTotal())}</div>
                              <div className="text-lg font-bold mt-1">
                                {calculateChange() >= 0 ? (
                                  <span className="text-green-600">
                                    الباقي: {formatCurrency(calculateChange())}
                                  </span>
                                ) : (
                                  <span className="text-red-600">
                                    المتبقي: {formatCurrency(Math.abs(calculateChange()))}
                                  </span>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Payment Method */}
                      <div className="space-y-2">
                        <Label>طريقة الدفع</Label>
                        <select 
                          value={paymentMethod} 
                          onChange={(e) => setPaymentMethod(e.target.value)}
                          className="w-full p-2 border rounded-lg"
                        >
                          <option value="نقدي">نقدي</option>
                          <option value="بطاقة">بطاقة</option>
                          <option value="تحويل">تحويل بنكي</option>
                        </select>
                      </div>

                      {/* Complete Sale Button */}
                      <Button 
                        onClick={() => {
                          setCurrentSaleData({
                            cart,
                            selectedCustomer,
                            total: calculateTotal(),
                            paymentMethod
                          });
                          setShowPostSaleDialog(true);
                        }}
                        className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold py-3 text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                        disabled={cart.length === 0}
                      >
                        <Receipt className="w-5 h-5 ml-2" />
                        إتمام البيع
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <PostSaleDialog
        open={showPostSaleDialog}
        onOpenChange={setShowPostSaleDialog}
        saleData={currentSaleData}
        onCompleteSale={handleCompleteSale}
      />

      <ReceiptDialog
        open={showReceiptDialog}
        onOpenChange={setShowReceiptDialog}
        saleData={receiptData}
        autoPrint={settings.autoPrint}
      />
    </>
  );
};

export default SalesPage;
