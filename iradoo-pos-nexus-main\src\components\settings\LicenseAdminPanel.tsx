import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Shield, Eye, ShieldOff, RefreshCw, Copy, AlertTriangle } from 'lucide-react';
import { licenseManager, LicenseRecord } from '@/utils/licenseManager';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useLocalAuth } from '@/hooks/useLocalAuth';

const LicenseAdminPanel = () => {
  const { toast } = useToast();
  const { user, canManageUsers } = useLocalAuth();
  const [licenseRecords, setLicenseRecords] = useState<LicenseRecord[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (canManageUsers) {
      loadLicenseRecords();
    }
  }, [canManageUsers]);

  const loadLicenseRecords = () => {
    setLoading(true);
    try {
      const records = licenseManager.getAllLicenseRecords();
      setLicenseRecords(records.sort((a, b) => new Date(b.issuedDate).getTime() - new Date(a.issuedDate).getTime()));
    } catch (error) {
      console.error('Error loading license records:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeactivateLicense = (licenseId: string) => {
    if (!user) return;
    
    const success = licenseManager.deactivateLicense(licenseId, user.email);
    
    if (success) {
      toast({
        title: 'تم إيقاف الترخيص',
        description: 'تم إيقاف الترخيص بنجاح',
      });
      loadLicenseRecords();
    } else {
      toast({
        title: 'خطأ',
        description: 'فشل في إيقاف الترخيص',
        variant: 'destructive',
      });
    }
  };

  const handleReactivateLicense = (licenseId: string) => {
    const success = licenseManager.reactivateLicense(licenseId);
    
    if (success) {
      toast({
        title: 'تم إعادة تفعيل الترخيص',
        description: 'تم إعادة تفعيل الترخيص بنجاح',
      });
      loadLicenseRecords();
    } else {
      toast({
        title: 'خطأ',
        description: 'فشل في إعادة تفعيل الترخيص',
        variant: 'destructive',
      });
    }
  };

  const copyLicenseKey = (licenseKey: string) => {
    navigator.clipboard.writeText(licenseKey);
    toast({
      title: 'تم النسخ',
      description: 'تم نسخ مفتاح الترخيص إلى الحافظة',
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-IQ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!canManageUsers) {
    return (
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          ليس لديك صلاحية للوصول إلى لوحة إدارة التراخيص
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                سجلات التراخيص المولدة
              </CardTitle>
              <CardDescription>
                عرض وإدارة جميع التراخيص المولدة في النظام
              </CardDescription>
            </div>
            <Button 
              onClick={loadLicenseRecords} 
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center p-8">جاري التحميل...</div>
          ) : licenseRecords.length === 0 ? (
            <div className="text-center p-8 text-gray-500">
              لا توجد تراخيص مولدة
            </div>
          ) : (
            <div className="space-y-4">
              {licenseRecords.map((record) => (
                <div key={record.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium text-lg">{record.customerName}</h3>
                        <Badge variant={record.isActive ? 'default' : 'destructive'}>
                          {record.isActive ? 'نشط' : 'موقف'}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
                        <p><strong>الرقم التسلسلي:</strong> {record.serialNumber}</p>
                        <p><strong>تاريخ الإصدار:</strong> {formatDate(record.issuedDate)}</p>
                        <p><strong>تم الإنشاء بواسطة:</strong> {record.generatedBy}</p>
                        {record.expirationDate && (
                          <p><strong>تاريخ الانتهاء:</strong> {formatDate(record.expirationDate)}</p>
                        )}
                      </div>

                      <div className="flex items-center gap-2 mb-3">
                        <span className="text-sm font-medium">الميزات:</span>
                        {record.features.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature === 'sales' && 'المبيعات'}
                            {feature === 'inventory' && 'المخزون'}
                            {feature === 'reports' && 'التقارير'}
                            {feature === 'customers' && 'العملاء'}
                            {feature === 'backup' && 'النسخ الاحتياطية'}
                            {feature === 'multiUser' && 'تعدد المستخدمين'}
                          </Badge>
                        ))}
                      </div>

                      {!record.isActive && record.deactivatedBy && (
                        <Alert variant="destructive" className="mb-3">
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            تم إيقافه بواسطة: {record.deactivatedBy} في {formatDate(record.deactivatedAt!)}
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-sm bg-gray-50 p-2 rounded mb-3 font-mono">
                    <span className="flex-1 truncate">{record.licenseKey}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyLicenseKey(record.licenseKey)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="flex gap-2">
                    {record.isActive ? (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeactivateLicense(record.id)}
                      >
                        <ShieldOff className="w-4 h-4 mr-2" />
                        إيقاف الترخيص
                      </Button>
                    ) : (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleReactivateLicense(record.id)}
                      >
                        <Shield className="w-4 h-4 mr-2" />
                        إعادة تفعيل
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default LicenseAdminPanel;
