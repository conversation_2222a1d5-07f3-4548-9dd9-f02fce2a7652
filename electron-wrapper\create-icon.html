<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة Iradoo POS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
            background: #f0f0f0;
        }
        .icon-container {
            display: inline-block;
            margin: 20px;
        }
        canvas {
            border: 2px solid #333;
            background: white;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
        }
        button:hover {
            background: #45a049;
        }
        .instructions {
            max-width: 600px;
            margin: 20px auto;
            text-align: right;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>🎨 إنشاء أيقونة Iradoo POS</h1>
    
    <div class="icon-container">
        <canvas id="iconCanvas" width="512" height="512"></canvas>
        <br>
        <button onclick="generateIcon()">إنشاء أيقونة</button>
        <button onclick="downloadIcon()">تحميل PNG</button>
    </div>

    <div class="instructions">
        <h3>📋 التعليمات:</h3>
        <ol>
            <li>انقر على "إنشاء أيقونة" لإنشاء أيقونة تجريبية</li>
            <li>انقر على "تحميل PNG" لحفظ الأيقونة</li>
            <li>احفظ الملف باسم <code>icon.png</code> في مجلد <code>assets/</code></li>
            <li>لإنشاء ملف ICO، استخدم موقع مثل <a href="https://convertio.co/png-ico/" target="_blank">convertio.co</a></li>
            <li>احفظ ملف ICO باسم <code>icon.ico</code> في مجلد <code>assets/</code></li>
        </ol>
        
        <h3>💡 نصائح:</h3>
        <ul>
            <li>استخدم ألوان واضحة ومتباينة</li>
            <li>تجنب التفاصيل الدقيقة جداً</li>
            <li>تأكد من وضوح الأيقونة في الأحجام الصغيرة</li>
            <li>يمكنك تعديل الكود أدناه لتخصيص الأيقونة</li>
        </ul>
    </div>

    <script>
        function generateIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 512, 512);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, 512, 512);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 512, 512);
            
            // Add border radius effect
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, 512, 512, 50);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Store icon (main symbol)
            ctx.fillStyle = 'white';
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 8;
            
            // Store building
            ctx.fillRect(150, 200, 212, 200);
            ctx.strokeRect(150, 200, 212, 200);
            
            // Store roof
            ctx.beginPath();
            ctx.moveTo(130, 200);
            ctx.lineTo(256, 120);
            ctx.lineTo(382, 200);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            
            // Door
            ctx.fillStyle = '#667eea';
            ctx.fillRect(230, 320, 52, 80);
            
            // Windows
            ctx.fillStyle = '#667eea';
            ctx.fillRect(170, 240, 40, 40);
            ctx.fillRect(302, 240, 40, 40);
            
            // POS text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('POS', 256, 450);
            
            // Arabic text
            ctx.font = 'bold 24px Arial';
            ctx.fillText('نقاط البيع', 256, 480);
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Generate icon on page load
        window.onload = function() {
            generateIcon();
        };
        
        // Add roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
