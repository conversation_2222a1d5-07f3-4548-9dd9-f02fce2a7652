{"name": "iradoo-pos-desktop", "version": "1.0.0", "description": "Iradoo POS Desktop Application with Local Database", "main": "src/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-all": "electron-builder --win --mac --linux", "dist": "npm run build", "pack": "electron-builder --dir", "rebuild": "electron-rebuild", "clean": "rimraf dist node_modules/.cache", "setup-db": "node src/database/setup.js", "test": "echo \"No tests specified\" && exit 0", "dev-test": "timeout 5 && electron . --dev"}, "keywords": ["electron", "desktop", "pos", "iradoo", "sqlite", "database", "offline"], "author": {"name": "Iradoo Team", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"electron-store": "^8.1.0", "electron-log": "^5.0.1", "electron-updater": "^6.1.7"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9", "rimraf": "^5.0.5"}, "build": {"appId": "com.iradoo.pos.desktop", "productName": "Iradoo POS", "directories": {"output": "dist", "buildResources": "resources"}, "files": ["src/**/*", "webapp/**/*", "assets/**/*", "node_modules/**/*", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Iradoo POS"}, "portable": {"artifactName": "${productName}-${version}-Portable.${ext}"}, "mac": {"target": "dmg", "icon": "assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": ["AppImage", "deb"], "icon": "assets/icon.png", "category": "Office"}}}