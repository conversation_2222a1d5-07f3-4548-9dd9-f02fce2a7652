# Iradoo POS Desktop Application

نظام نقاط البيع المتقدم المبني باستخدام Electron.js مع قاعدة بيانات JSON محلية مبسطة.

## المميزات

- 🏪 **نظام نقاط بيع كامل** - واجهة سهلة الاستخدام لإدارة المبيعات
- 💾 **قاعدة بيانات محلية** - JSON مع إعداد تلقائي وبيانات افتراضية (بدون تعقيدات SQLite)
- 🔒 **آمان متقدم** - تشفير كلمات المرور ونظام مستخدمين
- 📊 **تقارير مفصلة** - إحصائيات المبيعات والمخزون
- 💿 **نسخ احتياطي** - نسخ احتياطي تلقائي لقاعدة البيانات
- 🎨 **واجهة عصرية** - تصميم متجاوب يدعم اللغة العربية
- 📦 **مثبت احترافي** - ملفات تثبيت لـ Windows مع NSIS

## متطلبات النظام

- Windows 7/8/10/11
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة فارغة
- Node.js 16+ (للتطوير فقط)

## التثبيت السريع

### للمستخدمين النهائيين:
1. قم بتحميل ملف التثبيت `Iradoo-POS-Setup.exe`
2. شغل الملف واتبع التعليمات
3. سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول

### للمطورين:

```bash
# استنساخ المشروع
git clone [repository-url]
cd iradoo-desktop

# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير
npm run dev

# بناء التطبيق للإنتاج
npm run build

# إنشاء مثبت Windows
npm run dist
```

## البنية التقنية

```
iradoo-desktop/
├── src/
│   ├── main.js          # الملف الرئيسي للتطبيق
│   ├── preload.js       # جسر الأمان بين العمليات
│   └── database/
│       └── database.js  # إدارة قاعدة البيانات
├── webapp/
│   ├── index.html       # الواجهة الرئيسية
│   ├── styles.css       # ملفات التصميم
│   ├── app.js          # منطق التطبيق
│   └── splash.html      # شاشة التحميل
├── assets/              # الأيقونات والصور
└── package.json         # إعدادات المشروع
```

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite محلية مع الجداول التالية:

- **users** - المستخدمين والصلاحيات
- **products** - المنتجات والمخزون
- **sales** - المبيعات والفواتير
- **sale_items** - تفاصيل المبيعات
- **settings** - إعدادات التطبيق
- **app_logs** - سجلات النظام

### البيانات الافتراضية:
- مستخدم إداري: `admin` / `admin123`
- منتجات تجريبية للاختبار
- إعدادات أساسية للمتجر

## الاستخدام

### تسجيل الدخول:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### إضافة بيع جديد:
1. اضغط على "بيع جديد"
2. ابحث عن المنتجات وأضفها للسلة
3. اختر طريقة الدفع
4. اضغط "إتمام البيع"

### إدارة المخزون:
- عرض المنتجات منخفضة المخزون في لوحة التحكم
- تحديث الكميات تلقائياً مع كل بيع
- تنبيهات عند نفاد المخزون

### النسخ الاحتياطي:
- نسخ احتياطي يدوي من الإعدادات
- ملفات النسخ في مجلد `backups`
- استعادة البيانات من ملفات النسخ

## التخصيص

### تغيير الأيقونة:
استبدل الملفات في مجلد `assets/`:
- `icon.png` - أيقونة التطبيق (512x512)
- `icon.ico` - أيقونة Windows

### تخصيص الواجهة:
عدل ملف `webapp/styles.css` لتغيير:
- الألوان والخطوط
- التخطيط والتصميم
- الرسوم المتحركة

### إضافة مميزات:
- عدل `webapp/app.js` لإضافة وظائف جديدة
- استخدم `electronAPI` للتفاعل مع قاعدة البيانات
- أضف صفحات جديدة في `webapp/index.html`

## الأوامر المتاحة

```bash
npm run dev          # تشغيل وضع التطوير
npm run build        # بناء التطبيق
npm run dist         # إنشاء مثبت Windows
npm run pack         # حزم التطبيق بدون مثبت
npm run rebuild      # إعادة بناء التبعيات الأصلية
```

## استكشاف الأخطاء

### مشاكل شائعة:

**خطأ في قاعدة البيانات:**
- تأكد من وجود مجلد `data`
- احذف ملف `database.db` لإعادة الإنشاء
- تحقق من صلاحيات الكتابة

**مشاكل في البناء:**
```bash
# إعادة تثبيت التبعيات
rm -rf node_modules package-lock.json
npm install

# إعادة بناء التبعيات الأصلية
npm run rebuild
```

**مشاكل في الأداء:**
- تحقق من حجم قاعدة البيانات
- نظف ملفات السجلات القديمة
- أعد تشغيل التطبيق

## الدعم والتطوير

### ملفات السجلات:
- Windows: `%USERPROFILE%\AppData\Roaming\iradoo-pos\logs`
- السجلات تتضمن أخطاء النظام والعمليات

### التحديثات:
- التحديثات التلقائية مدمجة
- فحص التحديثات عند بدء التشغيل
- تحميل وتثبيت تلقائي

### المساهمة:
1. Fork المشروع
2. أنشئ فرع للميزة الجديدة
3. اختبر التغييرات
4. أرسل Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الاتصال

للدعم التقني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الموقع: https://iradoo.com
- التوثيق: https://docs.iradoo.com

---

**ملاحظة:** هذا التطبيق مصمم للاستخدام التجاري الصغير والمتوسط. للمشاريع الكبيرة، يُنصح بحلول قواعد بيانات أكثر تقدماً.
