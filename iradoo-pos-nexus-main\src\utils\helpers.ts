
interface WhatsAppMessage {
  phone: string;
  message: string;
  type: 'debt' | 'installment' | 'reminder';
}

export const sendWhatsAppMessage = async ({ phone, message, type }: WhatsAppMessage): Promise<void> => {
  try {
    // Format phone number for WhatsApp (remove any non-digits and add country code if needed)
    const formattedPhone = phone.replace(/\D/g, '');
    const whatsappPhone = formattedPhone.startsWith('964') ? formattedPhone : `964${formattedPhone}`;
    
    // Create WhatsApp URL with the message
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${whatsappPhone}?text=${encodedMessage}`;
    
    // Open WhatsApp in a new window
    window.open(whatsappUrl, '_blank');
    
    console.log(`WhatsApp message sent for ${type}:`, message);
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    throw error;
  }
};
