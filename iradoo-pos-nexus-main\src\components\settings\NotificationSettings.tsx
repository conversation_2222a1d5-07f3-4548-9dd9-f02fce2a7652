
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Bell, Mail, MessageSquare, Smartphone, Save } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';

const NotificationSettings = () => {
  const { toast } = useToast();
  const { settings, updateSettings } = useSettings();

  const handleSave = () => {
    toast({
      title: "تم الحفظ",
      description: "تم حفظ إعدادات الإشعارات بنجاح",
    });
  };

  const handleInputChange = (field: string, value: boolean) => {
    updateSettings({ [field]: value });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="w-5 h-5" />
            إعدادات الإشعارات
          </CardTitle>
          <CardDescription>
            تكوين طرق الإشعارات وأنواعها
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5 flex items-center gap-3">
                <Mail className="w-5 h-5 text-blue-500" />
                <div>
                  <Label>الإشعارات عبر البريد الإلكتروني</Label>
                  <p className="text-sm text-muted-foreground">
                    إرسال الإشعارات عبر البريد الإلكتروني
                  </p>
                </div>
              </div>
              <Switch
                checked={settings.emailNotifications}
                onCheckedChange={(checked) => handleInputChange('emailNotifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5 flex items-center gap-3">
                <MessageSquare className="w-5 h-5 text-green-500" />
                <div>
                  <Label>الإشعارات عبر الرسائل النصية</Label>
                  <p className="text-sm text-muted-foreground">
                    إرسال الإشعارات عبر الرسائل النصية
                  </p>
                </div>
              </div>
              <Switch
                checked={settings.smsNotifications}
                onCheckedChange={(checked) => handleInputChange('smsNotifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5 flex items-center gap-3">
                <Smartphone className="w-5 h-5 text-purple-500" />
                <div>
                  <Label>الإشعارات المنبثقة</Label>
                  <p className="text-sm text-muted-foreground">
                    إظهار الإشعارات في المتصفح
                  </p>
                </div>
              </div>
              <Switch
                checked={settings.pushNotifications}
                onCheckedChange={(checked) => handleInputChange('pushNotifications', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>أنواع الإشعارات</CardTitle>
          <CardDescription>
            اختر أنواع الإشعارات التي تريد تلقيها
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>إشعارات المخزون المنخفض</Label>
              <p className="text-sm text-muted-foreground">
                تنبيه عندما ينخفض مخزون المنتجات
              </p>
            </div>
            <Switch
              checked={settings.lowStockNotifications}
              onCheckedChange={(checked) => handleInputChange('lowStockNotifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>إشعارات المبيعات</Label>
              <p className="text-sm text-muted-foreground">
                تنبيه عند إتمام عمليات البيع
              </p>
            </div>
            <Switch
              checked={settings.salesNotifications}
              onCheckedChange={(checked) => handleInputChange('salesNotifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>إشعارات النظام</Label>
              <p className="text-sm text-muted-foreground">
                تنبيهات النظام والتحديثات
              </p>
            </div>
            <Switch
              checked={settings.systemNotifications}
              onCheckedChange={(checked) => handleInputChange('systemNotifications', checked)}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} className="flex items-center gap-2">
          <Save className="w-4 h-4" />
          حفظ الإعدادات
        </Button>
      </div>
    </div>
  );
};

export default NotificationSettings;
