
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { Plus, Edit, Trash2, UserPlus, Shield, AlertTriangle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Alert,
  AlertDescription,
} from '@/components/ui/alert';
import { localUserManager, LocalUser } from '@/utils/localUserManager';
import { useLocalAuth } from '@/hooks/useLocalAuth';

const LocalUserSettings = () => {
  const { toast } = useToast();
  const { canManageUsers, user: currentUser } = useLocalAuth();
  const [users, setUsers] = useState<LocalUser[]>([]);
  const [showAddUser, setShowAddUser] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    password: '',
    role: 'cashier' as 'admin' | 'cashier' | 'supervisor'
  });
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    if (canManageUsers) {
      fetchUsers();
    }
  }, [canManageUsers]);

  const fetchUsers = () => {
    const allUsers = localUserManager.getAllUsers();
    setUsers(allUsers);
  };

  const validateNewUser = (): boolean => {
    const errors: string[] = [];
    
    if (!newUser.name.trim()) {
      errors.push('اسم المستخدم مطلوب');
    }
    
    if (!newUser.email.trim()) {
      errors.push('البريد الإلكتروني مطلوب');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)) {
      errors.push('البريد الإلكتروني غير صحيح');
    }
    
    if (!newUser.password.trim()) {
      errors.push('كلمة المرور مطلوبة');
    } else if (newUser.password.length < 6) {
      errors.push('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    // تحقق من وجود البريد الإلكتروني
    const existingUser = localUserManager.getUserByEmail(newUser.email);
    if (existingUser) {
      errors.push('البريد الإلكتروني مستخدم بالفعل');
    }
    
    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleAddUser = () => {
    if (!validateNewUser()) return;

    try {
      const createdUser = localUserManager.createUser({
        name: newUser.name.trim(),
        email: newUser.email.trim(),
        password: newUser.password,
        role: newUser.role,
        active: true
      });

      setNewUser({ name: '', email: '', password: '', role: 'cashier' });
      setShowAddUser(false);
      setValidationErrors([]);
      fetchUsers();
      
      toast({
        title: "تم الإضافة",
        description: "تم إضافة المستخدم بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في إضافة المستخدم",
        variant: "destructive"
      });
    }
  };

  const handleDeleteUser = (userId: string) => {
    // منع حذف المستخدم الحالي
    if (currentUser?.id === userId) {
      toast({
        title: "خطأ",
        description: "لا يمكنك حذف حسابك الحالي",
        variant: "destructive"
      });
      setShowDeleteConfirm(null);
      return;
    }

    try {
      const success = localUserManager.deleteUser(userId);
      if (success) {
        fetchUsers();
        toast({
          title: "تم الحذف",
          description: "تم حذف المستخدم بنجاح",
        });
      } else {
        toast({
          title: "خطأ",
          description: "فشل في حذف المستخدم",
          variant: "destructive"
        });
      }
      setShowDeleteConfirm(null);
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في حذف المستخدم",
        variant: "destructive"
      });
    }
  };

  const updateUserRole = (userId: string, newRole: 'admin' | 'cashier' | 'supervisor') => {
    try {
      const updatedUser = localUserManager.updateUser(userId, { role: newRole });
      if (updatedUser) {
        fetchUsers();
        toast({
          title: "تم التحديث",
          description: "تم تحديث صلاحيات المستخدم بنجاح",
        });
      }
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث الصلاحيات",
        variant: "destructive"
      });
    }
  };

  const toggleUserStatus = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    // منع إيقاف المستخدم الحالي
    if (currentUser?.id === userId && user.active) {
      toast({
        title: "خطأ",
        description: "لا يمكنك إيقاف حسابك الحالي",
        variant: "destructive"
      });
      return;
    }

    try {
      const updatedUser = localUserManager.updateUser(userId, { active: !user.active });
      if (updatedUser) {
        fetchUsers();
        toast({
          title: "تم التحديث",
          description: `تم ${!user.active ? 'تفعيل' : 'إيقاف'} المستخدم بنجاح`,
        });
      }
    } catch (error) {
      toast({
        title: "خطأ",
        description: "فشل في تحديث حالة المستخدم",
        variant: "destructive"
      });
    }
  };

  const roleLabels = {
    admin: 'مدير النظام',
    supervisor: 'مشرف',
    cashier: 'كاشير'
  };

  const roleColors = {
    admin: 'bg-red-100 text-red-800',
    supervisor: 'bg-blue-100 text-blue-800',
    cashier: 'bg-green-100 text-green-800'
  };

  if (!canManageUsers) {
    return (
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          ليس لديك صلاحية للوصول إلى إدارة المستخدمين
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                إدارة المستخدمين - النظام المحلي
              </CardTitle>
              <CardDescription>
                إضافة وإدارة مستخدمي النظام المحلي (البيانات محفوظة في المتصفح)
              </CardDescription>
            </div>
            <Button onClick={() => setShowAddUser(true)} className="flex items-center gap-2">
              <UserPlus className="w-4 h-4" />
              إضافة مستخدم
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <Avatar className="w-12 h-12">
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                      {user.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{user.name}</h3>
                    <p className="text-sm text-gray-500">{user.email}</p>
                    <p className="text-xs text-gray-400">
                      آخر دخول: {user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar-SA') : 'لم يسجل دخول بعد'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Select 
                    value={user.role} 
                    onValueChange={(value: 'admin' | 'cashier' | 'supervisor') => 
                      updateUserRole(user.id, value)
                    }
                    disabled={currentUser?.id === user.id}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cashier">كاشير</SelectItem>
                      <SelectItem value="supervisor">مشرف</SelectItem>
                      <SelectItem value="admin">مدير النظام</SelectItem>
                    </SelectContent>
                  </Select>
                  <Badge className={roleColors[user.role]}>
                    {roleLabels[user.role]}
                  </Badge>
                  <Badge variant={user.active ? "default" : "secondary"}>
                    {user.active ? 'نشط' : 'غير نشط'}
                  </Badge>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleUserStatus(user.id)}
                      disabled={currentUser?.id === user.id && user.active}
                    >
                      {user.active ? 'إيقاف' : 'تفعيل'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowDeleteConfirm(user.id)}
                      className="text-red-600 hover:text-red-700"
                      disabled={currentUser?.id === user.id}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Add User Dialog */}
      <Dialog open={showAddUser} onOpenChange={setShowAddUser}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>إضافة مستخدم جديد</DialogTitle>
            <DialogDescription>
              أدخل معلومات المستخدم الجديد
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {validationErrors.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="list-disc list-inside">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
            <div className="space-y-2">
              <Label htmlFor="userName">الاسم</Label>
              <Input
                id="userName"
                value={newUser.name}
                onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="userEmail">البريد الإلكتروني</Label>
              <Input
                id="userEmail"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                className="text-right"
                dir="ltr"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="userPassword">كلمة المرور</Label>
              <Input
                id="userPassword"
                type="password"
                value={newUser.password}
                onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                className="text-right"
              />
            </div>
            <div className="space-y-2">
              <Label>الصلاحية</Label>
              <Select 
                value={newUser.role} 
                onValueChange={(value: 'admin' | 'cashier' | 'supervisor') => 
                  setNewUser({ ...newUser, role: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cashier">كاشير</SelectItem>
                  <SelectItem value="supervisor">مشرف</SelectItem>
                  <SelectItem value="admin">مدير النظام</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddUser(false)}>
              إلغاء
            </Button>
            <Button onClick={handleAddUser}>
              إضافة المستخدم
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!showDeleteConfirm} onOpenChange={() => setShowDeleteConfirm(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد الحذف</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
              إلغاء
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => showDeleteConfirm && handleDeleteUser(showDeleteConfirm)}
            >
              حذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LocalUserSettings;
