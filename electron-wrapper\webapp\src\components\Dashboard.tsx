
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, ShoppingCart, Users, Package, CreditCard, AlertCircle, DollarSign, BarChart3 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { getDashboardStats, getSales, resetAllData } from '@/utils/database';
import { useSettings } from '@/contexts/SettingsContext';

// ألوان وتدرجات نابضة للحيوية
const STAT_CARD_GRADIENTS = [
  'from-fuchsia-500 to-pink-400',
  'from-cyan-500 to-blue-500',
  'from-teal-500 to-lime-400',
  'from-orange-500 to-red-400',
];

const STAT_ICON_BG = [
  'bg-gradient-to-br from-fuchsia-400 to-pink-300',
  'bg-gradient-to-br from-cyan-400 to-blue-300',
  'bg-gradient-to-br from-teal-300 to-lime-300',
  'bg-gradient-to-br from-orange-300 to-red-300',
];

const Dashboard = () => {
  const { toast } = useToast();
  const { t, formatCurrency } = useSettings();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [loading, setLoading] = useState(true);

  const [stats, setStats] = useState([
    {
      title: t('today_sales'),
      value: '0',
      unit: '',
      change: '0%',
      trend: 'up',
      icon: DollarSign,
    },
    {
      title: t('orders_today'),
      value: '0',
      unit: '',
      change: '0%',
      trend: 'up',
      icon: CreditCard,
    },
    {
      title: t('products'),
      value: '0',
      unit: '',
      change: '0%',
      trend: 'up',
      icon: Package,
    },
    {
      title: t('low_stock'),
      value: '0',
      unit: '',
      change: '0%',
      trend: 'down',
      icon: Package,
    }
  ]);

  const [recentSales, setRecentSales] = useState<any[]>([]);

  const alerts = [
    { type: 'info', message: 'مرحباً بك في نظام أريدو لإدارة المبيعات', time: 'الآن' },
    { type: 'info', message: 'تم ربط النظام بقاعدة البيانات بنجاح', time: 'منذ لحظات' },
  ];

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [dashboardStats, salesData] = await Promise.all([
        getDashboardStats(),
        getSales()
      ]);
      setStats(prevStats => [
        {
          ...prevStats[0],
          value: dashboardStats.todayTotal.toLocaleString(),
        },
        {
          ...prevStats[1],
          value: salesData.length.toString(),
        },
        {
          ...prevStats[2],
          value: dashboardStats.totalProducts.toString(),
        },
        {
          ...prevStats[3],
          value: dashboardStats.lowStockCount.toString(),
        }
      ]);
      const recentSalesData = salesData.slice(0, 5).map(sale => ({
        id: `#${sale.id.substring(0, 8)}`,
        customer: sale.customer?.name || t('no_data'),
        amount: formatCurrency(sale.final_amount),
        time: new Date(sale.created_at).toLocaleTimeString('ar-IQ', {
          hour: '2-digit',
          minute: '2-digit'
        }),
        status: t('success')
      }));
      setRecentSales(recentSalesData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: t('error'),
        description: t('operation_failed'),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const handleRefreshData = async () => {
    setIsRefreshing(true);
    try {
      await loadDashboardData();
      toast({
        title: t('success'),
        description: t('operation_successful'),
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('operation_failed'),
        variant: "destructive"
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleResetData = async () => {
    const confirmReset = window.confirm(t('confirm_delete') + ' ' + t('cannot_undo'));
    if (!confirmReset) return;
    setIsResetting(true);
    try {
      await resetAllData();
      await loadDashboardData();
      toast({
        title: t('success'),
        description: t('operation_successful'),
      });
    } catch (error) {
      console.error('Error resetting data:', error);
      toast({
        title: t('error'),
        description: t('operation_failed'),
        variant: "destructive"
      });
    } finally {
      setIsResetting(false);
    }
  };

  const handleExportReport = () => {
    toast({
      title: t('loading'),
      description: t('operation_successful'),
    });
    setTimeout(() => {
      const data = {
        date: new Date().toLocaleDateString('ar-IQ'),
        totalSales: stats[0].value,
        totalInvoices: stats[1].value,
        totalProducts: stats[2].value,
        sales: recentSales
      };
      const dataStr = JSON.stringify(data, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `تقرير-المبيعات-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      toast({
        title: t('success'),
        description: t('operation_successful'),
      });
    }, 1500);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg text-fuchsia-600 animate-bounce-in font-bold">
          جاري تحميل بيانات لوحة التحكم...
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6 min-h-screen bg-gradient-to-br from-fuchsia-50 via-blue-50 to-cyan-50">
      {/* Page Title */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-extrabold bg-gradient-to-r from-fuchsia-500 via-pink-500 to-cyan-400 bg-clip-text text-transparent animate-slide-in">
            {t('dashboard')}
          </h1>
          <p className="text-gray-700 mt-2 font-medium animate-fade-in">{t('welcome')}</p>
        </div>
        <div className="flex gap-3">
          <Button
            onClick={handleRefreshData}
            disabled={isRefreshing}
            className="bg-gradient-to-r from-fuchsia-500 via-pink-500 to-cyan-400 hover:from-pink-400 hover:to-fuchsia-400 text-white font-bold shadow-lg focus:ring-4 focus:ring-fuchsia-200 animate-fade-in"
          >
            {isRefreshing ? t('loading') : t('refresh')}
          </Button>
          <Button
            variant="outline"
            onClick={handleExportReport}
            className="border-fuchsia-300 text-fuchsia-600 font-semibold hover:bg-fuchsia-50 animate-fade-in"
          >
            {t('export_pdf')}
          </Button>
          <Button
            variant="destructive"
            onClick={handleResetData}
            disabled={isResetting}
            className="font-semibold animate-fade-in"
          >
            {isResetting ? t('loading') : t('reset')}
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card
            key={index}
            className={`relative overflow-hidden border-0 shadow-xl group hover:scale-[1.03] hover:ring-4 hover:ring-fuchsia-200 focus:ring-4 focus:ring-fuchsia-300 transition-all duration-300 bg-gradient-to-br ${STAT_CARD_GRADIENTS[index % STAT_CARD_GRADIENTS.length]} text-white`}
          >
            <CardContent className="p-6 flex flex-col h-full animate-fade-in">
              <div className="flex items-center gap-4 justify-between">
                <div>
                  <p className="text-base font-bold uppercase tracking-wider drop-shadow">{stat.title}</p>
                  <div className="flex items-baseline gap-2 mt-2 mb-3">
                    <p className="text-3xl font-extrabold drop-shadow">{stat.value}</p>
                    <span className="text-lg font-light drop-shadow">{stat.unit}</span>
                  </div>
                  <div className="flex items-center mt-1">
                    {stat.trend === 'up' ? (
                      <TrendingUp className="w-5 h-5 text-lime-200 ml-1 animate-bounce-in" />
                    ) : (
                      <TrendingDown className="w-5 h-5 text-rose-200 ml-1 animate-bounce-in" />
                    )}
                    <span className={`text-sm font-bold drop-shadow ${stat.trend === 'up' ? 'text-lime-50' : 'text-rose-50'}`}>
                      {stat.change}
                    </span>
                    <span className="text-xs text-fuchsia-50/70 mr-1">من الأمس</span>
                  </div>
                </div>
                <div className={`p-4 rounded-2xl shadow-xl animate-bounce-in ${STAT_ICON_BG[index % STAT_ICON_BG.length]} flex items-center justify-center`}>
                  <stat.icon className="w-8 h-8" />
                </div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-fuchsia-400 via-pink-300 to-cyan-300 group-hover:bg-fuchsia-500 transition-all"></div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Sales Chart */}
        <Card className="xl:col-span-2 border-0 shadow-lg bg-gradient-to-br from-pink-100/70 via-blue-200/60 to-cyan-100/60 animate-fade-in">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-bold">
                <span className="bg-gradient-to-r from-fuchsia-500 via-pink-400 to-cyan-400 bg-clip-text text-transparent">{t('weekly_report')}</span>
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                className="border-fuchsia-300 text-fuchsia-600 font-semibold hover:bg-fuchsia-50"
              >
                {t('view_all')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-80 flex items-center justify-center rounded-xl">
              <div className="text-center animate-bounce-in">
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-200 to-fuchsia-400 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="w-8 h-8 text-fuchsia-700" />
                </div>
                <p className="text-fuchsia-600 font-medium">رسم بياني للمبيعات</p>
                <p className="text-sm text-pink-400">سيتم إضافته قريباً</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Sales */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-fuchsia-50/70 to-cyan-50/50 animate-fade-in">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold">
              <span className="bg-gradient-to-r from-pink-500 to-cyan-400 bg-clip-text text-transparent">{t('recent_transactions')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {recentSales.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingCart className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">{t('no_data')}</p>
              </div>
            ) : (
              recentSales.map((sale) => (
                <div
                  key={sale.id}
                  className="flex items-center justify-between p-4 bg-gradient-to-l from-fuchsia-50/60 to-cyan-50/60 rounded-xl hover:from-fuchsia-100 hover:to-cyan-100 transition-colors shadow-sm group hover:scale-105 animate-fade-in"
                >
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-semibold text-fuchsia-800">{sale.customer}</p>
                      <Badge className="bg-gradient-to-r from-green-200 to-lime-200 text-green-900 group-hover:bg-green-400">
                        {sale.status}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-fuchsia-700">{sale.id}</p>
                      <p className="text-sm text-blue-700">{sale.time}</p>
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <p className="font-extrabold text-lg text-cyan-700 drop-shadow">{sale.amount} د.ع</p>
                  </div>
                </div>
              ))
            )}
            <Button variant="outline" className="w-full mt-4 border-fuchsia-300 text-fuchsia-600 font-bold hover:bg-fuchsia-50 animate-fade-in">عرض جميع المبيعات</Button>
          </CardContent>
        </Card>
      </div>

      {/* Alerts Section */}
      <Card className="border-0 shadow-md bg-gradient-to-l from-fuchsia-50/80 to-cyan-50/80 animate-fade-in">
        <CardHeader>
          <CardTitle className="text-xl font-bold flex items-center gap-2">
            <AlertCircle className="w-6 h-6 text-orange-500" />
            <span className="bg-gradient-to-r from-orange-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">{t('notifications')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {alerts.map((alert, index) => (
              <div key={index} className={`p-4 rounded-xl border-r-4 animate-fade-in ${
                alert.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                alert.type === 'error' ? 'bg-red-50 border-red-400' :
                'bg-gradient-to-l from-cyan-50/90 to-fuchsia-50/90 border-fuchsia-300'
              }`}>
                <div className="flex items-center justify-between">
                  <p className="font-medium text-fuchsia-800">{alert.message}</p>
                  <span className="text-sm text-blue-600">{alert.time}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
