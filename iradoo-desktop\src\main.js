const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const log = require('electron-log');
const { autoUpdater } = require('electron-updater');
const Store = require('electron-store');
const dbManager = require('./database/database');

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

// Initialize store for app settings
const store = new Store();

class IradooApp {
    constructor() {
        this.mainWindow = null;
        this.splashWindow = null;
        this.isDevMode = process.argv.includes('--dev');
        this.appConfig = {
            name: 'Iradoo POS',
            version: app.getVersion(),
            minWidth: 1024,
            minHeight: 768,
            defaultWidth: 1200,
            defaultHeight: 800
        };
    }

    /**
     * Initialize the application
     */
    async initialize() {
        try {
            log.info('Starting Iradoo POS Desktop Application');
            
            // Set app user model ID for Windows
            if (process.platform === 'win32') {
                app.setAppUserModelId('com.iradoo.pos.desktop');
            }

            // Handle app events
            this.setupAppEvents();

            // Initialize database first
            await this.initializeDatabase();

            // Setup IPC handlers after database is ready
            this.setupIpcHandlers();
            
            // Create application windows
            await this.createWindows();
            
            // Setup application menu
            this.createMenu();
            
            // Setup auto updater
            this.setupAutoUpdater();
            
            log.info('Application initialized successfully');
        } catch (error) {
            log.error('Application initialization failed:', error);
            this.showErrorDialog('Initialization Error', error.message);
        }
    }

    /**
     * Setup application event handlers
     */
    setupAppEvents() {
        app.whenReady().then(() => this.initialize());

        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                this.cleanup();
                app.quit();
            }
        });

        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                this.createMainWindow();
            }
        });

        app.on('before-quit', async () => {
            await this.cleanup();
        });
    }

    /**
     * Initialize database
     */
    async initializeDatabase() {
        try {
            this.updateSplashStatus('Initializing database...');
            await dbManager.initialize();
            log.info('Database initialized successfully');
        } catch (error) {
            log.error('Database initialization failed:', error);
            throw new Error(`Database initialization failed: ${error.message}`);
        }
    }

    /**
     * Create application windows
     */
    async createWindows() {
        // Create splash screen first
        await this.createSplashWindow();
        
        // Simulate loading time
        await this.simulateLoading();
        
        // Create main window
        await this.createMainWindow();
        
        // Close splash screen
        if (this.splashWindow) {
            this.splashWindow.close();
            this.splashWindow = null;
        }
    }

    /**
     * Create splash screen
     */
    async createSplashWindow() {
        this.splashWindow = new BrowserWindow({
            width: 400,
            height: 300,
            frame: false,
            alwaysOnTop: true,
            transparent: true,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, 'preload.js')
            }
        });

        const splashPath = path.join(__dirname, '../webapp/splash.html');
        if (fs.existsSync(splashPath)) {
            await this.splashWindow.loadFile(splashPath);
        } else {
            // Create simple splash content
            const splashContent = this.createSplashContent();
            await this.splashWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(splashContent)}`);
        }

        this.splashWindow.center();
        this.splashWindow.show();
    }

    /**
     * Create main application window
     */
    async createMainWindow() {
        // Get saved window bounds or use defaults
        const bounds = store.get('windowBounds', {
            width: this.appConfig.defaultWidth,
            height: this.appConfig.defaultHeight
        });

        this.mainWindow = new BrowserWindow({
            ...bounds,
            minWidth: this.appConfig.minWidth,
            minHeight: this.appConfig.minHeight,
            show: false,
            icon: this.getAppIcon(),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js'),
                webSecurity: true
            }
        });

        // Load the web application
        await this.loadWebApp();

        // Window event handlers
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            if (this.isDevMode) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // Save window bounds when resized or moved
        this.mainWindow.on('resize', () => this.saveWindowBounds());
        this.mainWindow.on('move', () => this.saveWindowBounds());

        // Handle external links
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            shell.openExternal(url);
            return { action: 'deny' };
        });
    }

    /**
     * Load the web application
     */
    async loadWebApp() {
        const config = store.get('appConfig', { mode: 'local' });
        
        try {
            if (config.mode === 'url' && config.url) {
                // Load from URL
                await this.mainWindow.loadURL(config.url);
                log.info('Loaded web app from URL:', config.url);
            } else {
                // Load local files
                const webappPath = path.join(__dirname, '../webapp');
                const indexPath = path.join(webappPath, 'index.html');
                
                if (fs.existsSync(indexPath)) {
                    await this.mainWindow.loadFile(indexPath);
                    log.info('Loaded local web app from:', indexPath);
                } else {
                    // Load default app
                    await this.loadDefaultApp();
                }
            }
        } catch (error) {
            log.error('Failed to load web app:', error);
            await this.loadDefaultApp();
        }
    }

    /**
     * Load default application when webapp is not available
     */
    async loadDefaultApp() {
        const defaultContent = this.createDefaultAppContent();
        await this.mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(defaultContent)}`);
        log.info('Loaded default application content');
    }

    /**
     * Setup IPC handlers for communication with renderer
     */
    setupIpcHandlers() {
        // Database operations
        ipcMain.handle('db:getStats', async () => {
            return await dbManager.getStats();
        });

        ipcMain.handle('db:query', async (event, sql, params) => {
            return await dbManager.all(sql, params);
        });

        ipcMain.handle('db:backup', async () => {
            return await dbManager.backup();
        });

        // App operations
        ipcMain.handle('app:getInfo', () => {
            return {
                name: this.appConfig.name,
                version: this.appConfig.version,
                platform: process.platform,
                arch: process.arch,
                isDevMode: this.isDevMode
            };
        });

        ipcMain.handle('app:getConfig', () => {
            return store.get('appConfig', {});
        });

        ipcMain.handle('app:setConfig', (event, config) => {
            store.set('appConfig', config);
            return true;
        });

        // File operations
        ipcMain.handle('file:selectFolder', async () => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                properties: ['openDirectory']
            });
            return result.filePaths[0];
        });

        ipcMain.handle('file:saveFile', async (event, options) => {
            const result = await dialog.showSaveDialog(this.mainWindow, options);
            return result.filePath;
        });
    }

    /**
     * Create application menu
     */
    createMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'New Sale',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => this.sendToRenderer('menu:newSale')
                    },
                    { type: 'separator' },
                    {
                        label: 'Backup Database',
                        click: async () => {
                            try {
                                const backupPath = await dbManager.backup();
                                this.showInfoDialog('Backup Complete', `Database backed up to:\n${backupPath}`);
                            } catch (error) {
                                this.showErrorDialog('Backup Failed', error.message);
                            }
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Exit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => app.quit()
                    }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Window',
                submenu: [
                    { role: 'minimize' },
                    { role: 'close' }
                ]
            },
            {
                label: 'Help',
                submenu: [
                    {
                        label: 'About',
                        click: () => this.showAboutDialog()
                    },
                    {
                        label: 'Check for Updates',
                        click: () => autoUpdater.checkForUpdatesAndNotify()
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    /**
     * Setup auto updater
     */
    setupAutoUpdater() {
        if (this.isDevMode) return;

        autoUpdater.checkForUpdatesAndNotify();
        
        autoUpdater.on('update-available', () => {
            log.info('Update available');
        });

        autoUpdater.on('update-downloaded', () => {
            log.info('Update downloaded');
            dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'Update Ready',
                message: 'Update downloaded. Application will restart to apply the update.',
                buttons: ['Restart Now', 'Later']
            }).then((result) => {
                if (result.response === 0) {
                    autoUpdater.quitAndInstall();
                }
            });
        });
    }

    /**
     * Utility methods
     */
    getAppIcon() {
        const iconPath = path.join(__dirname, '../assets/icon.png');
        return fs.existsSync(iconPath) ? iconPath : null;
    }

    saveWindowBounds() {
        if (this.mainWindow) {
            store.set('windowBounds', this.mainWindow.getBounds());
        }
    }

    sendToRenderer(channel, data) {
        if (this.mainWindow && this.mainWindow.webContents) {
            this.mainWindow.webContents.send(channel, data);
        }
    }

    updateSplashStatus(status) {
        if (this.splashWindow && this.splashWindow.webContents) {
            this.splashWindow.webContents.send('splash:updateStatus', status);
        }
    }

    async simulateLoading() {
        const steps = [
            'Loading configuration...',
            'Connecting to database...',
            'Preparing interface...',
            'Almost ready...'
        ];

        for (const step of steps) {
            this.updateSplashStatus(step);
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    async cleanup() {
        try {
            log.info('Cleaning up application...');
            if (dbManager) {
                await dbManager.close();
            }
            log.info('Cleanup completed');
        } catch (error) {
            log.error('Cleanup error:', error);
        }
    }

    showErrorDialog(title, message) {
        dialog.showErrorBox(title, message);
    }

    showInfoDialog(title, message) {
        if (this.mainWindow) {
            dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title,
                message
            });
        }
    }

    showAboutDialog() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'About Iradoo POS',
            message: `${this.appConfig.name} v${this.appConfig.version}`,
            detail: 'Professional Point of Sale System\nBuilt with Electron and SQLite'
        });
    }

    createSplashContent() {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Loading...</title>
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    text-align: center;
                }
                .logo { font-size: 32px; font-weight: bold; margin-bottom: 20px; }
                .status { font-size: 14px; opacity: 0.8; margin-top: 20px; }
                .spinner {
                    border: 3px solid rgba(255,255,255,0.3);
                    border-radius: 50%;
                    border-top: 3px solid white;
                    width: 30px;
                    height: 30px;
                    animation: spin 1s linear infinite;
                    margin: 20px auto;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        </head>
        <body>
            <div class="logo">🏪 Iradoo POS</div>
            <div class="spinner"></div>
            <div class="status" id="status">Starting application...</div>
            <script>
                const { ipcRenderer } = require('electron');
                ipcRenderer.on('splash:updateStatus', (event, status) => {
                    document.getElementById('status').textContent = status;
                });
            </script>
        </body>
        </html>
        `;
    }

    createDefaultAppContent() {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Iradoo POS</title>
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #f5f5f5;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    margin-bottom: 40px;
                }
                .logo {
                    font-size: 48px;
                    margin-bottom: 10px;
                }
                .title {
                    font-size: 32px;
                    color: #333;
                    margin-bottom: 10px;
                }
                .subtitle {
                    color: #666;
                    font-size: 16px;
                }
                .features {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin-top: 40px;
                }
                .feature {
                    padding: 20px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    text-align: center;
                }
                .feature-icon {
                    font-size: 32px;
                    margin-bottom: 10px;
                }
                .feature-title {
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .feature-desc {
                    color: #666;
                    font-size: 14px;
                }
                .status {
                    background: #e3f2fd;
                    padding: 20px;
                    border-radius: 8px;
                    margin-top: 30px;
                }
                .btn {
                    background: #2196f3;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin: 5px;
                }
                .btn:hover {
                    background: #1976d2;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">🏪</div>
                    <div class="title">Iradoo POS</div>
                    <div class="subtitle">Professional Point of Sale System</div>
                </div>
                
                <div class="features">
                    <div class="feature">
                        <div class="feature-icon">💾</div>
                        <div class="feature-title">Local Database</div>
                        <div class="feature-desc">SQLite database with automatic backup</div>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">🔒</div>
                        <div class="feature-title">Secure</div>
                        <div class="feature-desc">User authentication and data encryption</div>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">📱</div>
                        <div class="feature-title">Responsive</div>
                        <div class="feature-desc">Works on all screen sizes</div>
                    </div>
                    <div class="feature">
                        <div class="feature-icon">🚀</div>
                        <div class="feature-title">Fast</div>
                        <div class="feature-desc">Optimized for performance</div>
                    </div>
                </div>
                
                <div class="status">
                    <h3>🎯 Setup Required</h3>
                    <p>To get started, please copy your web application files to the <code>webapp</code> folder, or configure a URL in the settings.</p>
                    <button class="btn" onclick="openWebappFolder()">Open Webapp Folder</button>
                    <button class="btn" onclick="showDbStats()">Database Info</button>
                </div>
            </div>
            
            <script>
                const { ipcRenderer } = require('electron');
                
                async function openWebappFolder() {
                    const path = require('path');
                    const { shell } = require('electron');
                    const webappPath = path.join(__dirname, '../webapp');
                    shell.openPath(webappPath);
                }
                
                async function showDbStats() {
                    try {
                        const stats = await ipcRenderer.invoke('db:getStats');
                        alert(\`Database Statistics:
Users: \${stats.users.count}
Products: \${stats.products.count}
Sales: \${stats.sales.count}
Total Sales: $\${stats.totalSales.total || 0}
Database Size: \${stats.dbSize} KB\`);
                    } catch (error) {
                        alert('Error getting database stats: ' + error.message);
                    }
                }
            </script>
        </body>
        </html>
        `;
    }
}

// Create and start the application
const iradooApp = new IradooApp();

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    log.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    log.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

module.exports = iradooApp;
