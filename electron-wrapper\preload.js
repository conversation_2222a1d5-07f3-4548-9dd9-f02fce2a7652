const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Add any APIs you want to expose to your web app here
  // For example:
  
  // Get app version
  getVersion: () => {
    return process.env.npm_package_version || '1.0.0';
  },
  
  // Check if running in Electron
  isElectron: () => {
    return true;
  },
  
  // Platform info
  getPlatform: () => {
    return process.platform;
  },
  
  // You can add more APIs here as needed
  // Example: file operations, notifications, etc.
});

// Prevent the web app from accessing Node.js APIs directly
delete window.require;
delete window.exports;
delete window.module;
