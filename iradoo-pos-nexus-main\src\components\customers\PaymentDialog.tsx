import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import { sendWhatsAppMessage } from '@/utils/helpers';
import { 
  getCustomerDebts, 
  getCustomerInstallments,
  payDebt,
  payInstallment,
  type Customer,
  type Debt,
  type Installment
} from '@/utils/database';
import { Calendar, CreditCard } from 'lucide-react';

interface PaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer: Customer | null;
  onPaymentComplete: () => void;
}

const PaymentDialog: React.FC<PaymentDialogProps> = ({
  open,
  onOpenChange,
  customer,
  onPaymentComplete
}) => {
  const { toast } = useToast();
  const { formatCurrency } = useSettings();
  const [debts, setDebts] = useState<Debt[]>([]);
  const [installments, setInstallments] = useState<Installment[]>([]);
  const [selectedDebt, setSelectedDebt] = useState<Debt | null>(null);
  const [selectedInstallment, setSelectedInstallment] = useState<Installment | null>(null);
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [paymentType, setPaymentType] = useState<'full' | 'partial'>('partial');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const loadPaymentData = async () => {
    if (!customer) return;

    try {
      setLoading(true);
      const [debtsData, installmentsData] = await Promise.all([
        getCustomerDebts(customer.id),
        getCustomerInstallments(customer.id)
      ]);

      setDebts(debtsData);
      setInstallments(installmentsData);
    } catch (error) {
      console.error('Error loading payment data:', error);
      toast({
        title: "خطأ في تحميل البيانات",
        description: "حدث خطأ أثناء تحميل بيانات الديون والأقساط",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && customer) {
      loadPaymentData();
    }
  }, [open, customer]);

  const handlePayment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedDebt && !selectedInstallment) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار دين أو قسط للدفع",
        variant: "destructive"
      });
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال مبلغ صحيح",
        variant: "destructive"
      });
      return;
    }

    try {
      setSubmitting(true);
      const paymentAmount = parseFloat(amount);

      if (selectedDebt) {
        await payDebt(selectedDebt.id, paymentAmount, paymentType);
      } else if (selectedInstallment) {
        await payInstallment(selectedInstallment.id, paymentAmount);
      }

      // Print receipt
      await printPaymentReceipt({
        customer,
        amount: paymentAmount,
        paymentMethod,
        type: selectedDebt ? 'debt' : 'installment',
        reference: selectedDebt || selectedInstallment,
        date: new Date().toLocaleDateString('ar-IQ')
      });

      // Send WhatsApp confirmation
      if (customer?.phone) {
        const message = selectedDebt 
          ? `تم دفع مبلغ ${paymentAmount.toLocaleString()} د.ع من الدين. المبلغ المتبقي: ${(selectedDebt.remaining_amount - paymentAmount).toLocaleString()} د.ع`
          : `تم دفع قسط بمبلغ ${paymentAmount.toLocaleString()} د.ع. المبلغ المتبقي: ${(selectedInstallment!.remaining_amount - paymentAmount).toLocaleString()} د.ع`;
        
        await sendWhatsAppMessage({
          phone: customer.phone,
          message,
          type: selectedDebt ? 'debt' : 'installment'
        });
      }

      toast({
        title: "تم الدفع بنجاح",
        description: `تم دفع ${paymentAmount.toLocaleString()} د.ع وطباعة الفاتورة`,
      });

      onPaymentComplete();
      resetForm();
    } catch (error) {
      console.error('Error processing payment:', error);
      toast({
        title: "خطأ في الدفع",
        description: "حدث خطأ أثناء معالجة الدفع",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  const resetForm = () => {
    setSelectedDebt(null);
    setSelectedInstallment(null);
    setAmount('');
    setPaymentMethod('cash');
    setPaymentType('partial');
  };

  const printPaymentReceipt = async ({ customer, amount, paymentMethod, type, reference, date }: any) => {
    const receiptContent = `
      <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
        <h2 style="font-size: 1.5em; margin-bottom: 0.5em;">إيصال دفع</h2>
        <p style="font-size: 1em; margin-bottom: 0.3em;"><strong>التاريخ:</strong> ${date}</p>
        <p style="font-size: 1em; margin-bottom: 0.3em;"><strong>العميل:</strong> ${customer.name}</p>
        <p style="font-size: 1em; margin-bottom: 0.3em;"><strong>رقم الهاتف:</strong> ${customer.phone || 'لا يوجد'}</p>
        <p style="font-size: 1em; margin-bottom: 0.3em;"><strong>نوع الدفع:</strong> ${type === 'debt' ? 'دين' : 'قسط'}</p>
        <p style="font-size: 1em; margin-bottom: 0.3em;"><strong>المبلغ المدفوع:</strong> ${amount.toLocaleString()} د.ع</p>
        <p style="font-size: 1em; margin-bottom: 0.3em;"><strong>طريقة الدفع:</strong> ${paymentMethod === 'cash' ? 'نقداً' : 'بطاقة'}</p>
        ${type === 'debt' ? `<p style="font-size: 1em; margin-bottom: 0.3em;"><strong>الدين المتبقي:</strong> ${(reference.remaining_amount - amount).toLocaleString()} د.ع</p>` : ''}
        ${type === 'installment' ? `<p style="font-size: 1em; margin-bottom: 0.3em;"><strong>القسط المتبقي:</strong> ${(reference.remaining_amount - amount).toLocaleString()} د.ع</p>` : ''}
        <hr style="border: none; border-top: 1px dashed #ccc; margin: 0.5em 0;">
        <p style="font-size: 0.8em; color: #888;">شكراً لتعاملكم</p>
      </div>
    `;

    const printWindow = window.open('', '_blank', 'width=600,height=400');
    printWindow!.document.write(`
      <html>
        <head>
          <title>إيصال دفع</title>
        </head>
        <body style="font-family: Arial, sans-serif; direction: rtl; text-align: right; padding: 20px;">
          ${receiptContent}
        </body>
      </html>
    `);
    printWindow!.document.close();
    printWindow!.focus();
    printWindow!.print();
    printWindow!.close();
  };

  if (!customer) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            دفع ديون وأقساط للعميل: {customer.name}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center h-48">
            <div className="text-lg">جاري تحميل البيانات...</div>
          </div>
        ) : (
          <form onSubmit={handlePayment} className="space-y-6">
            {/* Debts Section */}
            {debts.length > 0 && (
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    الديون ({debts.length})
                  </h3>
                  <div className="space-y-3">
                    {debts.map((debt) => (
                      <div
                        key={debt.id}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          selectedDebt?.id === debt.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => {
                          setSelectedDebt(debt);
                          setSelectedInstallment(null);
                        }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <div className="font-medium">دين رقم #{debt.id.substring(0, 8)}</div>
                            <div className="text-sm text-gray-600">
                              تاريخ الإنشاء: {new Date(debt.created_at).toLocaleDateString('ar-IQ')}
                            </div>
                          </div>
                          <Badge variant={debt.status === 'pending' ? 'default' : 'secondary'}>
                            {debt.status === 'pending' ? 'غير مدفوع' : 'مدفوع'}
                          </Badge>
                        </div>

                        {/* Product Items */}
                        {debt.items && debt.items.length > 0 && (
                          <div className="mb-3">
                            <div className="text-sm font-medium text-gray-700 mb-2">المنتجات:</div>
                            <div className="space-y-1">
                              {debt.items.map((item: any, index: number) => (
                                <div key={index} className="text-sm text-gray-600 bg-gray-100 p-2 rounded">
                                  {item.product_name || item.name} - كمية: {item.quantity} - السعر: {item.price?.toLocaleString()} د.ع
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">المبلغ الإجمالي:</span>
                            <div className="font-bold text-blue-600">{debt.total_amount.toLocaleString()} د.ع</div>
                          </div>
                          <div>
                            <span className="text-gray-600">المبلغ المتبقي:</span>
                            <div className="font-bold text-red-600">{debt.remaining_amount.toLocaleString()} د.ع</div>
                          </div>
                        </div>
                        
                        {debt.notes && (
                          <div className="mt-2 text-sm text-gray-600">
                            <strong>ملاحظات:</strong> {debt.notes}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Installments Section */}
            {installments.length > 0 && (
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    الأقساط ({installments.length})
                  </h3>
                  <div className="space-y-3">
                    {installments.map((installment) => (
                      <div
                        key={installment.id}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          selectedInstallment?.id === installment.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => {
                          setSelectedInstallment(installment);
                          setSelectedDebt(null);
                        }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <div className="font-medium">قسط رقم #{installment.id.substring(0, 8)}</div>
                            <div className="text-sm text-gray-600">
                              تاريخ الإنشاء: {new Date(installment.created_at).toLocaleDateString('ar-IQ')}
                            </div>
                          </div>
                          <Badge variant={installment.status === 'active' ? 'default' : 'secondary'}>
                            {installment.status === 'active' ? 'نشط' : 'مكتمل'}
                          </Badge>
                        </div>

                        {/* Product Items */}
                        {installment.items && installment.items.length > 0 && (
                          <div className="mb-3">
                            <div className="text-sm font-medium text-gray-700 mb-2">المنتجات:</div>
                            <div className="space-y-1">
                              {installment.items.map((item: any, index: number) => (
                                <div key={index} className="text-sm text-gray-600 bg-gray-100 p-2 rounded">
                                  {item.product_name || item.name} - كمية: {item.quantity} - السعر: {item.price?.toLocaleString()} د.ع
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">المبلغ الإجمالي:</span>
                            <div className="font-bold text-blue-600">{installment.total_amount.toLocaleString()} د.ع</div>
                          </div>
                          <div>
                            <span className="text-gray-600">المبلغ المتبقي:</span>
                            <div className="font-bold text-red-600">{installment.remaining_amount.toLocaleString()} د.ع</div>
                          </div>
                          <div>
                            <span className="text-gray-600">القسط الشهري:</span>
                            <div className="font-bold">{installment.monthly_payment.toLocaleString()} د.ع</div>
                          </div>
                          <div>
                            <span className="text-gray-600">الأشهر المدفوعة:</span>
                            <div className="font-bold">{installment.paid_months}/{installment.months}</div>
                          </div>
                        </div>
                        
                        {installment.notes && (
                          <div className="mt-2 text-sm text-gray-600">
                            <strong>ملاحظات:</strong> {installment.notes}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Payment Details */}
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-4">تفاصيل الدفع</h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="amount">المبلغ المدفوع (د.ع)</Label>
                    <Input
                      type="number"
                      id="amount"
                      placeholder="أدخل المبلغ"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="paymentMethod">طريقة الدفع</Label>
                    <select
                      id="paymentMethod"
                      className="w-full p-3 border rounded"
                      value={paymentMethod}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    >
                      <option value="cash">نقداً</option>
                      <option value="card">بطاقة</option>
                    </select>
                  </div>

                  {selectedDebt && (
                    <div>
                      <Label htmlFor="paymentType">نوع الدفع</Label>
                      <select
                        id="paymentType"
                        className="w-full p-3 border rounded"
                        value={paymentType}
                        onChange={(e) => setPaymentType(e.target.value as 'full' | 'partial')}
                      >
                        <option value="partial">جزئي</option>
                        <option value="full">كامل</option>
                      </select>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Submit Button */}
            <Button type="submit" disabled={submitting} className="w-full">
              {submitting ? "جاري الدفع..." : "تأكيد الدفع"}
            </Button>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PaymentDialog;
